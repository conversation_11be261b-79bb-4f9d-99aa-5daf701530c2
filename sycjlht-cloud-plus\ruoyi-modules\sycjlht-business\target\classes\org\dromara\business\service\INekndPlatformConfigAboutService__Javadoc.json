{"doc": " 关于平台信息编辑Service接口\n\n <AUTHOR>\n @date 2025-02-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPlatformConfigAboutById", "paramTypes": ["java.lang.Integer"], "doc": " 查询关于平台信息编辑\n\n @param id 关于平台信息编辑主键\n @return 关于平台信息编辑\n"}, {"name": "selectNekndPlatformConfigAboutList", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 查询关于平台信息编辑列表\n\n @param nekndPlatformConfigAbout 关于平台信息编辑\n @return 关于平台信息编辑集合\n"}, {"name": "insertNekndPlatformConfigAbout", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 新增关于平台信息编辑\n\n @param nekndPlatformConfigAbout 关于平台信息编辑\n @return 结果\n"}, {"name": "updateNekndPlatformConfigAbout", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 修改关于平台信息编辑\n\n @param nekndPlatformConfigAbout 关于平台信息编辑\n @return 结果\n"}, {"name": "deleteNekndPlatformConfigAboutByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除关于平台信息编辑\n\n @param ids 需要删除的关于平台信息编辑主键集合\n @return 结果\n"}, {"name": "deleteNekndPlatformConfigAboutById", "paramTypes": ["java.lang.Integer"], "doc": " 删除关于平台信息编辑信息\n\n @param id 关于平台信息编辑主键\n @return 结果\n"}], "constructors": []}