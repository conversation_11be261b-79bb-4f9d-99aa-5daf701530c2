{"doc": " 用户点赞记录Mapper接口\n\n <AUTHOR>\n @date 2025-04-03\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndLikeRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 查询用户点赞记录\n\n @param id 用户点赞记录主键\n @return 用户点赞记录\n"}, {"name": "selectNekndLikeRecordsList", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": " 查询用户点赞记录列表\n\n @param nekndLikeRecords 用户点赞记录\n @return 用户点赞记录集合\n"}, {"name": "insertNekndLikeRecords", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": " 新增用户点赞记录\n\n @param nekndLikeRecords 用户点赞记录\n @return 结果\n"}, {"name": "updateNekndLikeRecords", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": " 修改用户点赞记录\n\n @param nekndLikeRecords 用户点赞记录\n @return 结果\n"}, {"name": "deleteNekndLikeRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 删除用户点赞记录\n\n @param id 用户点赞记录主键\n @return 结果\n"}, {"name": "deleteNekndLikeRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除用户点赞记录\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}