{"doc": " 师资库Mapper接口\n\n <AUTHOR>\n @date 2024-12-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTeacherPoolById", "paramTypes": ["java.lang.Integer"], "doc": " 查询师资库\n\n @param id 师资库主键\n @return 师资库\n"}, {"name": "selectNekndTeacherPoolList", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": " 查询师资库列表\n\n @param nekndTeacherPool 师资库\n @return 师资库集合\n"}, {"name": "insertNekndTeacherPool", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": " 新增师资库\n\n @param nekndTeacherPool 师资库\n @return 结果\n"}, {"name": "updateNekndTeacherPool", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": " 修改师资库\n\n @param nekndTeacherPool 师资库\n @return 结果\n"}, {"name": "deleteNekndTeacherPoolById", "paramTypes": ["java.lang.Integer"], "doc": " 删除师资库\n\n @param id 师资库主键\n @return 结果\n"}, {"name": "updateNekndTeacherPoolToDeletedByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除师资库\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}