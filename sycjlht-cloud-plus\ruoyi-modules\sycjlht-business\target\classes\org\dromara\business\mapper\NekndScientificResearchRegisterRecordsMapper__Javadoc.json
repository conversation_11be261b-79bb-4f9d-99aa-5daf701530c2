{"doc": " 科普研学报名记录Mapper接口\n\n <AUTHOR>\n @date 2024-08-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 查询科普研学报名记录\n\n @param id 科普研学报名记录主键\n @return 科普研学报名记录\n"}, {"name": "selectNekndScientificResearchRegisterRecordsList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": " 查询科普研学报名记录列表\n\n @param nekndScientificResearchRegisterRecords 科普研学报名记录\n @return 科普研学报名记录集合\n"}, {"name": "insertNekndScientificResearchRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": " 新增科普研学报名记录\n\n @param nekndScientificResearchRegisterRecords 科普研学报名记录\n @return 结果\n"}, {"name": "updateNekndScientificResearchRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": " 修改科普研学报名记录\n\n @param nekndScientificResearchRegisterRecords 科普研学报名记录\n @return 结果\n"}, {"name": "deleteNekndScientificResearchRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 删除科普研学报名记录\n\n @param id 科普研学报名记录主键\n @return 结果\n"}, {"name": "deleteNekndScientificResearchRegisterRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除科普研学报名记录\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}