{"doc": " 培训报名Controller\n \n <AUTHOR>\n @date 2024-06-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询培训报名列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 导出培训报名列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取培训报名详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 新增培训报名\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 修改培训报名\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除培训报名（软删除）\n"}, {"name": "registration", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 报名培训\n"}, {"name": "getRegistrationStatus", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 查询报名状态\n"}, {"name": "selectPersonalTraining", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 个人培训项目查看\n"}, {"name": "selectEnterpriseTraining", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 企业培训项目查看\n"}, {"name": "cancelRegistration", "paramTypes": ["java.lang.Integer"], "doc": " 取消报名\n"}, {"name": "getMyRegistrations", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前用户的报名记录\n"}, {"name": "batch<PERSON><PERSON><PERSON>", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量审核报名\n"}], "constructors": []}