{"doc": " 市域产教融合项目库Service接口\n\n <AUTHOR>\n @date 2025-02-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": " 查询市域产教融合项目库\n\n @param id 市域产教融合项目库主键\n @return 市域产教融合项目库\n"}, {"name": "selectNekndIndustryEducationProjectsList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 查询市域产教融合项目库列表\n\n @param nekndIndustryEducationProjects 市域产教融合项目库\n @return 市域产教融合项目库集合\n"}, {"name": "insertNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 新增市域产教融合项目库\n\n @param nekndIndustryEducationProjects 市域产教融合项目库\n @return 结果\n"}, {"name": "updateNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 修改市域产教融合项目库\n\n @param nekndIndustryEducationProjects 市域产教融合项目库\n @return 结果\n"}, {"name": "deleteNekndIndustryEducationProjectsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除市域产教融合项目库\n\n @param ids 需要删除的市域产教融合项目库主键集合\n @return 结果\n"}, {"name": "deleteNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": " 删除市域产教融合项目库信息\n\n @param id 市域产教融合项目库主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询产教融合项目列表\n\n @param nekndIndustryEducationProjects 查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}, {"name": "getProjectStatisticsByType", "paramTypes": [], "doc": " 按类型获取项目统计信息\n\n @return 统计结果\n"}, {"name": "getProjectStatisticsByStatus", "paramTypes": [], "doc": " 按状态获取项目统计信息\n\n @return 统计结果\n"}, {"name": "queryPopularProjects", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询热门项目\n\n @param pageQuery 分页参数\n @return 热门项目列表\n"}], "constructors": []}