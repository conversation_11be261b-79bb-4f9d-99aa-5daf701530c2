{"doc": " 服务需求Controller\n \n <AUTHOR>\n @date 2024-05-30\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询服务需求列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户查询服务需求列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndServiceRequirements"], "doc": " 导出服务需求列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 导入服务需求\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取服务需求详细信息\n"}, {"name": "getInfoPublic", "paramTypes": ["java.lang.Integer"], "doc": " 获取服务需求详细信息（门户使用）\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": " 新增服务需求\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": " 修改服务需求\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除服务需求\n"}, {"name": "getRecommendedServices", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询推荐服务需求列表\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 审核服务需求\n"}, {"name": "getMyServiceRequirements", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前用户的服务需求\n"}, {"name": "filterByField", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按服务领域筛选\n"}, {"name": "filterByRegion", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按地区筛选服务需求\n"}, {"name": "getPopularServices", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取热门服务需求\n"}], "constructors": []}