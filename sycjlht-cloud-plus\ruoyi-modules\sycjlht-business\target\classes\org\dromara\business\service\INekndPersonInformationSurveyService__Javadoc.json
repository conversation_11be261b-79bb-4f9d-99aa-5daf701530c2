{"doc": " 学生调查Service接口\n\n <AUTHOR>\n @date 2024-05-21\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPersonInformationSurveyById", "paramTypes": ["java.lang.Integer"], "doc": " 查询学生调查\n\n @param id 学生调查主键\n @return 学生调查\n"}, {"name": "selectNekndPersonInformationSurveyList", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 查询学生调查列表\n\n @param nekndPersonInformationSurvey 学生调查\n @return 学生调查集合\n"}, {"name": "insertNekndPersonInformationSurvey", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 新增学生调查\n\n @param nekndPersonInformationSurvey 学生调查\n @return 结果\n"}, {"name": "updateNekndPersonInformationSurvey", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 修改学生调查\n\n @param nekndPersonInformationSurvey 学生调查\n @return 结果\n"}, {"name": "deleteNekndPersonInformationSurveyByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除学生调查\n\n @param ids 需要删除的学生调查主键集合\n @return 结果\n"}, {"name": "deleteNekndPersonInformationSurveyById", "paramTypes": ["java.lang.Integer"], "doc": " 删除学生调查信息\n\n @param id 学生调查主键\n @return 结果\n"}, {"name": "selectFormY", "paramTypes": ["java.lang.String"], "doc": " 查询填写和未填写问卷调查的人员\n @return\n"}, {"name": "getItem1Counts", "paramTypes": [], "doc": " 就业信息统计\n\n @return 个人信息集合\n"}], "constructors": []}