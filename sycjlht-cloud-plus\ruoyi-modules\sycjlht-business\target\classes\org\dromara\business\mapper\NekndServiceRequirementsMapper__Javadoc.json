{"doc": " 服务需求Mapper接口\n\n <AUTHOR>\n @date 2024-05-30\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndServiceRequirementsById", "paramTypes": ["java.lang.Integer"], "doc": " 查询服务需求\n\n @param id 服务需求主键\n @return 服务需求\n"}, {"name": "selectNekndServiceRequirementsList", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": " 查询服务需求列表\n\n @param nekndServiceRequirements 服务需求\n @return 服务需求集合\n"}, {"name": "insertNekndServiceRequirements", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": " 新增服务需求\n\n @param nekndServiceRequirements 服务需求\n @return 结果\n"}, {"name": "updateNekndServiceRequirements", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": " 修改服务需求\n\n @param nekndServiceRequirements 服务需求\n @return 结果\n"}, {"name": "deleteNekndServiceRequirementsById", "paramTypes": ["java.lang.Integer"], "doc": " 删除服务需求\n\n @param id 服务需求主键\n @return 结果\n"}, {"name": "deleteNekndServiceRequirementsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除服务需求\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}