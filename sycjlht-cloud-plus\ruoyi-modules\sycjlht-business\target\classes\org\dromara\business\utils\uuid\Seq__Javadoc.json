{"doc": " <AUTHOR> 序列生成类\n", "fields": [], "enumConstants": [], "methods": [{"name": "getId", "paramTypes": [], "doc": " 获取通用序列号\n\n @return 序列值\n"}, {"name": "getId", "paramTypes": ["java.lang.String"], "doc": " 默认16位序列号 yyMMddHHmmss + 一位机器标识 + 3长度循环递增字符串\n\n @return 序列值\n"}, {"name": "getId", "paramTypes": ["java.util.concurrent.atomic.AtomicInteger", "int"], "doc": " 通用接口序列号 yyMMddHHmmss + 一位机器标识 + length长度循环递增字符串\n\n @param atomicInt 序列数\n @param length 数值长度\n @return 序列值\n"}, {"name": "getSeq", "paramTypes": ["java.util.concurrent.atomic.AtomicInteger", "int"], "doc": " 序列循环递增字符串[1, 10 的 (length)幂次方), 用0左补齐length位数\n\n @return 序列值\n"}], "constructors": []}