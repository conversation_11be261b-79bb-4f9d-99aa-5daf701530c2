{"doc": " spring redis 工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 缓存基本的对象，Integer、String、实体类等\n\n @param key 缓存的键值\n @param value 缓存的值\n"}, {"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Integer", "java.util.concurrent.TimeUnit"], "doc": " 缓存基本的对象，Integer、String、实体类等\n\n @param key 缓存的键值\n @param value 缓存的值\n @param timeout 时间\n @param timeUnit 时间颗粒度\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "long"], "doc": " 设置有效时间\n\n @param key Redis键\n @param timeout 超时时间\n @return true=设置成功；false=设置失败\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "long", "java.util.concurrent.TimeUnit"], "doc": " 设置有效时间\n\n @param key Redis键\n @param timeout 超时时间\n @param unit 时间单位\n @return true=设置成功；false=设置失败\n"}, {"name": "getExpire", "paramTypes": ["java.lang.String"], "doc": " 获取有效时间\n\n @param key Redis键\n @return 有效时间\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 判断 key是否存在\n\n @param key 键\n @return true 存在 false不存在\n"}, {"name": "getCacheObject", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的基本对象。\n\n @param key 缓存键值\n @return 缓存键值对应的数据\n"}, {"name": "deleteObject", "paramTypes": ["java.lang.String"], "doc": " 删除单个对象\n\n @param key\n"}, {"name": "deleteObject", "paramTypes": ["java.util.Collection"], "doc": " 删除集合对象\n\n @param collection 多个对象\n @return\n"}, {"name": "setCacheList", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 缓存List数据\n\n @param key 缓存的键值\n @param dataList 待缓存的List数据\n @return 缓存的对象\n"}, {"name": "getCacheList", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的list对象\n\n @param key 缓存的键值\n @return 缓存键值对应的数据\n"}, {"name": "setCacheSet", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": " 缓存Set\n\n @param key 缓存键值\n @param dataSet 缓存的数据\n @return 缓存数据的对象\n"}, {"name": "getCacheSet", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的set\n\n @param key\n @return\n"}, {"name": "setCacheMap", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 缓存Map\n\n @param key\n @param dataMap\n"}, {"name": "getCacheMap", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的Map\n\n @param key\n @return\n"}, {"name": "setCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 往Hash中存入数据\n\n @param key Redis键\n @param hKey Hash键\n @param value 值\n"}, {"name": "getCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取Hash中的数据\n\n @param key Redis键\n @param hKey Hash键\n @return Hash中的对象\n"}, {"name": "getMultiCacheMapValue", "paramTypes": ["java.lang.String", "java.util.Collection"], "doc": " 获取多个Hash中的数据\n\n @param key Redis键\n @param hKeys Hash键集合\n @return Hash对象集合\n"}, {"name": "deleteCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 删除Hash中的某条数据\n\n @param key Redis键\n @param hKey Hash键\n @return 是否成功\n"}, {"name": "keys", "paramTypes": ["java.lang.String"], "doc": " 获得缓存的基本对象列表\n\n @param pattern 字符串前缀\n @return 对象列表\n"}], "constructors": []}