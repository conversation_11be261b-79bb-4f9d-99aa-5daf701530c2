{"doc": " 招聘岗位远程服务实现\n\n <AUTHOR>\n @date 2024-12-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployListByDept", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 查询招聘岗位列表\n\n @param deptId 部门ID\n @param reviewStatus 审核状态\n @param status 状态\n @return 招聘岗位集合\n"}, {"name": "selectNekndEmployCount", "paramTypes": [], "doc": " 查询招聘岗位数量\n\n @return 招聘岗位数量\n"}, {"name": "selectJobTypeByUserId", "paramTypes": ["java.lang.Long"], "doc": " 通过企业的userId查询企业所有的招聘岗位类型列表\n\n @param userId 用户ID\n @return 岗位类型列表\n"}], "constructors": []}