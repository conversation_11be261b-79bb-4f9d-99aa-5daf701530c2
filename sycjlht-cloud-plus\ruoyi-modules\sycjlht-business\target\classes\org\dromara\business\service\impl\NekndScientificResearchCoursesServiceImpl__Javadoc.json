{"doc": " 科普研学课程Service业务层处理\n\n <AUTHOR>\n @date 2025-06-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchCoursesById", "paramTypes": ["java.lang.Integer"], "doc": " 查询科普研学课程\n\n @param id 科普研学课程主键\n @return 科普研学课程\n"}, {"name": "selectNekndScientificResearchCoursesList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchCourses"], "doc": " 查询科普研学课程列表\n\n @param nekndScientificResearchCourses 科普研学课程\n @return 科普研学课程\n"}, {"name": "insertNekndScientificResearchCourses", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchCourses"], "doc": " 新增科普研学课程\n\n @param nekndScientificResearchCourses 科普研学课程\n @return 结果\n"}, {"name": "updateNekndScientificResearchCourses", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchCourses"], "doc": " 修改科普研学课程\n\n @param nekndScientificResearchCourses 科普研学课程\n @return 结果\n"}, {"name": "deleteNekndScientificResearchCoursesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除科普研学课程\n\n @param ids 需要删除的科普研学课程主键\n @return 结果\n"}, {"name": "deleteNekndScientificResearchCoursesById", "paramTypes": ["java.lang.Integer"], "doc": " 删除科普研学课程信息\n\n @param id 科普研学课程主键\n @return 结果\n"}], "constructors": []}