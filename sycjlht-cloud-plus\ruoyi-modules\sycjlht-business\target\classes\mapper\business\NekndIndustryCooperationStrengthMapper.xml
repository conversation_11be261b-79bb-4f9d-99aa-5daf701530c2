<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryCooperationStrengthMapper">
    
    <resultMap type="NekndIndustryCooperationStrength" id="NekndIndustryCooperationStrengthResult">
        <result property="industryId"    column="industry_id"    />
        <result property="year"    column="year"    />
        <result property="technologyInstitute"    column="technology_institute"    />
        <result property="trainingBase"    column="training_base"    />
    </resultMap>

    <sql id="selectNekndIndustryCooperationStrengthVo">
        select industry_id, year, technology_institute, training_base from neknd_industry_cooperation_strength
    </sql>

    <select id="selectNekndIndustryCooperationStrengthList" parameterType="NekndIndustryCooperationStrength" resultMap="NekndIndustryCooperationStrengthResult">
        <include refid="selectNekndIndustryCooperationStrengthVo"/>
        <where>  
            <if test="year != null "> and year = #{year}</if>
            <if test="technologyInstitute != null "> and technology_institute = #{technologyInstitute}</if>
            <if test="trainingBase != null "> and training_base = #{trainingBase}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryCooperationStrengthByIndustryId" parameterType="Long" resultMap="NekndIndustryCooperationStrengthResult">
        <include refid="selectNekndIndustryCooperationStrengthVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustryCooperationStrengthByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_cooperation_strength
        where industry_id = #{industryId}
    </select>

    <insert id="insertNekndIndustryCooperationStrength" parameterType="NekndIndustryCooperationStrength" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_cooperation_strength
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="year != null">year,</if>
            <if test="technologyInstitute != null">technology_institute,</if>
            <if test="trainingBase != null">training_base,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="year != null">#{year},</if>
            <if test="technologyInstitute != null">#{technologyInstitute},</if>
            <if test="trainingBase != null">#{trainingBase},</if>
        </trim>
        On Duplicate Key Update industry_id = values(industry_id), year = values(year), technology_institute = values(technology_institute), training_base = values(training_base)
    </insert>

    <update id="updateNekndIndustryCooperationStrength" parameterType="NekndIndustryCooperationStrength">
        update neknd_industry_cooperation_strength
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="technologyInstitute != null">technology_institute = #{technologyInstitute},</if>
            <if test="trainingBase != null">training_base = #{trainingBase},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryCooperationStrengthByIndustryId" parameterType="Long">
        delete from neknd_industry_cooperation_strength where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryCooperationStrengthByIndustryIds" parameterType="String">
        delete from neknd_industry_cooperation_strength where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>