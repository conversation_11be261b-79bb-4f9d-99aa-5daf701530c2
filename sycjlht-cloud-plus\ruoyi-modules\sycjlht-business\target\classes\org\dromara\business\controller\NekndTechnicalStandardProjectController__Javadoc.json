{"doc": " 技术标准立项公告信息Controller\n \n <AUTHOR>\n @date 2024-12-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询技术标准立项公告信息列表\n"}, {"name": "adminList", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 管理端查询技术标准立项公告信息列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": " 导出技术标准立项公告信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取技术标准立项公告信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": " 新增技术标准立项公告信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": " 修改技术标准立项公告信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除技术标准立项公告信息\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新状态\n"}, {"name": "getLatestProjects", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取最新的技术标准立项公告\n"}, {"name": "filterByStatus", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按状态筛选技术标准项目\n"}], "constructors": []}