{"doc": " 共同体Controller\n \n <AUTHOR>\n @date 2025-01-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCommunity", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询共同体列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCommunity"], "doc": " 导出共同体列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取共同体详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": " 新增共同体\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": " 修改共同体\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除共同体\n"}, {"name": "joinCommunity", "paramTypes": ["java.lang.Long"], "doc": " 加入共同体\n"}, {"name": "leaveCommunity", "paramTypes": ["java.lang.Long"], "doc": " 离开共同体\n"}, {"name": "getMyCommunities", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取用户加入的共同体\n"}, {"name": "getCommunityMembers", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取共同体成员列表\n"}, {"name": "getPopularCommunities", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取热门共同体\n"}, {"name": "searchCommunities", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 搜索共同体\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取共同体统计信息\n"}, {"name": "getByType", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按类型查询共同体\n"}, {"name": "checkMembership", "paramTypes": ["java.lang.Long"], "doc": " 检查用户是否已加入共同体\n"}, {"name": "updateCommunityActivity", "paramTypes": [], "doc": " 定时任务：更新共同体活跃度\n"}], "constructors": []}