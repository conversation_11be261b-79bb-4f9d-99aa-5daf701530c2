{"doc": " 通用http发送方法\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendGet", "paramTypes": ["java.lang.String"], "doc": " 向指定 URL 发送GET方法的请求\n\n @param url 发送请求的 URL\n @return 所代表远程资源的响应结果\n"}, {"name": "sendGet", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 向指定 URL 发送GET方法的请求\n\n @param url 发送请求的 URL\n @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。\n @return 所代表远程资源的响应结果\n"}, {"name": "sendGet", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 向指定 URL 发送GET方法的请求\n\n @param url 发送请求的 URL\n @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。\n @param contentType 编码类型\n @return 所代表远程资源的响应结果\n"}, {"name": "sendPost", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 向指定 URL 发送POST方法的请求\n\n @param url 发送请求的 URL\n @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。\n @return 所代表远程资源的响应结果\n"}, {"name": "getHttpClient", "paramTypes": [], "doc": " 获取httpClient\n\n @return\n"}, {"name": "createConnectionManager", "paramTypes": [], "doc": " 创建连接池管理器\n\n @return\n"}, {"name": "createRequestConfig", "paramTypes": [], "doc": " 根据当前配置创建HTTP请求配置参数。\n\n @return 返回HTTP请求配置。\n"}, {"name": "createHttpClient", "paramTypes": ["org.apache.http.conn.HttpClientConnectionManager"], "doc": " 创建默认的HTTPS客户端，信任所有的证书。\n\n @return 返回HTTPS客户端，如果创建失败，返回HTTP客户端。\n"}, {"name": "initClient", "paramTypes": [], "doc": " 初始化 只需调用一次\n"}, {"name": "shutdown", "paramTypes": [], "doc": " 关闭HTTP客户端。\n"}, {"name": "getCall", "paramTypes": ["java.lang.String"], "doc": " 请求上游 GET提交\n\n @param uri\n @throws IOException\n"}, {"name": "getCall", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 请求上游 GET提交\n\n @param uri\n @param contentType\n @throws IOException\n"}, {"name": "getCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 请求上游 GET提交\n\n @param uri\n @param contentType\n @param charsetName\n @throws IOException\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 请求上游 POST提交\n\n @param uri\n @param paramsMap\n @throws IOException\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 请求上游 POST提交\n\n @param uri\n @param contentType\n @param paramsMap\n @throws IOException\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.String"], "doc": " 请求上游 POST提交\n\n @param uri\n @param contentType\n @param paramsMap\n @param charsetName\n @throws IOException\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 请求上游 POST提交\n\n @param uri\n @param param\n @throws IOException\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 请求上游 POST提交\n\n @param uri\n @param contentType\n @param param\n @throws IOException\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 请求上游 POST提交\n\n @param uri\n @param contentType\n @param param\n @param charsetName\n @throws IOException\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 请求上游 POST提交，支持自定义请求头\n\n @param uri 请求地址\n @param contentType 内容类型\n @param param 请求参数\n @param charsetName 字符集\n @param token 认证token\n @param headers 自定义请求头\n @throws Exception\n"}, {"name": "isReadTimeout", "paramTypes": ["java.lang.Throwable"], "doc": " 判断HTTP异常是否为读取超时。\n\n @param e 异常对象。\n @return 如果是读取引起的异常（而非连接），则返回true；否则返回false。\n"}, {"name": "isCausedBy", "paramTypes": ["java.lang.Throwable", "java.lang.Class"], "doc": " 检测异常e被触发的原因是不是因为异常cause。检测被封装的异常。\n\n @param e 捕获的异常。\n @param cause 异常触发原因。\n @return 如果异常e是由cause类异常触发，则返回true；否则返回false。\n"}], "constructors": []}