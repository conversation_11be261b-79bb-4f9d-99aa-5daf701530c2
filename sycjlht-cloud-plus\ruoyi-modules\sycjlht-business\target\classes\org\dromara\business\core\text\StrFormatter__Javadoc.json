{"doc": " 字符串格式化\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "format", "paramTypes": ["java.lang.String", "java.lang.Object[]"], "doc": " 格式化字符串<br>\n 此方法只是简单将占位符 {} 按照顺序替换为参数<br>\n 如果想输出 {} 使用 \\\\转义 { 即可，如果想输出 {} 之前的 \\ 使用双转义符 \\\\\\\\ 即可<br>\n 例：<br>\n 通常使用：format(\"this is {} for {}\", \"a\", \"b\") -> this is a for b<br>\n 转义{}： format(\"this is \\\\{} for {}\", \"a\", \"b\") -> this is \\{} for a<br>\n 转义\\： format(\"this is \\\\\\\\{} for {}\", \"a\", \"b\") -> this is \\a for b<br>\n\n @param strPattern 字符串模板\n @param argArray 参数列表\n @return 结果\n"}], "constructors": []}