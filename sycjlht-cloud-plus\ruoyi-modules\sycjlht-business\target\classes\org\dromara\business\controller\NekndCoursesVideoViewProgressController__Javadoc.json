{"doc": " 视频观看进度记录Controller\n \n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询视频观看进度记录列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": " 导出视频观看进度记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取视频观看进度记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": " 新增视频观看进度记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": " 修改视频观看进度记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除视频观看进度记录\n"}, {"name": "getUserProgress", "paramTypes": ["java.lang.Integer"], "doc": " 获取用户的视频观看进度\n"}, {"name": "updateProgress", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": " 更新或创建视频观看进度\n"}, {"name": "getStudyStatistics", "paramTypes": [], "doc": " 获取用户的学习统计\n"}, {"name": "markComplete", "paramTypes": ["java.lang.Integer"], "doc": " 标记视频为已完成\n"}], "constructors": []}