{"doc": " 【请填写功能名称】Service业务层处理\n\n <AUTHOR>\n @date 2024-12-25\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustrySchoolCompanyCooperationByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industry 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustrySchoolCompanyCooperationList", "paramTypes": ["org.dromara.business.domain.NekndIndustrySchoolCompanyCooperation"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustrySchoolCompanyCooperation 【请填写功能名称】\n @return 【请填写功能名称】\n"}, {"name": "insertNekndIndustrySchoolCompanyCooperation", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "org.dromara.business.domain.NekndIndustrySchoolCompanyCooperation"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustrySchoolCompanyCooperation 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustrySchoolCompanyCooperation", "paramTypes": ["org.dromara.business.domain.NekndIndustrySchoolCompanyCooperation"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustrySchoolCompanyCooperation 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustrySchoolCompanyCooperationByIndustrys", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industrys 需要删除的【请填写功能名称】主键\n @return 结果\n"}, {"name": "deleteNekndIndustrySchoolCompanyCooperationByIndustry", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】信息\n\n @param industry 【请填写功能名称】主键\n @return 结果\n"}], "constructors": []}