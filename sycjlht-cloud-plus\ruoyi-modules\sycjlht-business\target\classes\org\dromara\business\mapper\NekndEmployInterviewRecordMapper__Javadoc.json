{"doc": " AI面试记录Mapper接口\n\n <AUTHOR>\n @date 2024-05-29\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployInterviewRecordById", "paramTypes": ["java.lang.Integer"], "doc": " 查询AI面试记录\n\n @param id AI面试记录主键\n @return AI面试记录\n"}, {"name": "selectNekndEmployInterviewRecordList", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": " 查询AI面试记录列表\n\n @param nekndEmployInterviewRecord AI面试记录\n @return AI面试记录集合\n"}, {"name": "insertNekndEmployInterviewRecord", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": " 新增AI面试记录\n\n @param nekndEmployInterviewRecord AI面试记录\n @return 结果\n"}, {"name": "updateNekndEmployInterviewRecord", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": " 修改AI面试记录\n\n @param nekndEmployInterviewRecord AI面试记录\n @return 结果\n"}, {"name": "deleteNekndEmployInterviewRecordById", "paramTypes": ["java.lang.Integer"], "doc": " 删除AI面试记录\n\n @param id AI面试记录主键\n @return 结果\n"}, {"name": "deleteNekndEmployInterviewRecordByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除AI面试记录\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}