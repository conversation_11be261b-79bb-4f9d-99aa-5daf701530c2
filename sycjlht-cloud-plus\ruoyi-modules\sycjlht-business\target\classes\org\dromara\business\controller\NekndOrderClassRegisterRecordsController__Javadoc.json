{"doc": " 订单班报名记录Controller\n \n <AUTHOR>\n @date 2024-08-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 查询订单班报名记录列表\n"}, {"name": "orderClassStudents", "paramTypes": ["java.lang.Integer"], "doc": " 根据订单班id查看报名该订单班的人员信息\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 导出订单班报名记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取订单班报名记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 新增订单班报名记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 修改订单班报名记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除订单班报名记录\n"}], "constructors": []}