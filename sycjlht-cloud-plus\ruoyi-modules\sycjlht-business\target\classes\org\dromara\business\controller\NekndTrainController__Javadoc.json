{"doc": " 培训管理Controller\n \n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndTrain", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询培训列表（基于角色权限控制）\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndTrain"], "doc": " 导出培训列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 导入培训数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取培训详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 新增培训\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 修改培训\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除培训\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 审核培训\n"}, {"name": "orderClassList", "paramTypes": ["org.dromara.business.domain.NekndTrain", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户网站获取订单班列表\n"}, {"name": "orderClassDetail", "paramTypes": ["java.lang.Integer"], "doc": " 门户网站获取订单班详情\n"}, {"name": "trainProgramList", "paramTypes": ["org.dromara.business.domain.NekndTrain", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户网站获取培训项目列表\n"}, {"name": "trainProgramDetail", "paramTypes": ["java.lang.Integer"], "doc": " 门户网站获取培训项目详情\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取培训统计信息\n"}, {"name": "trainStudents", "paramTypes": ["java.lang.Integer", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据培训id查看报名该培训项目的学员\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 字典翻译辅助方法\n"}], "constructors": []}