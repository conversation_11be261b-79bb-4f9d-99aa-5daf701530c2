<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndPlatformConfigMapper">
    
    <resultMap type="NekndPlatformConfig" id="NekndPlatformConfigResult">
        <result property="id"    column="id"    />
        <result property="logo"    column="logo"    />
        <result property="title"    column="title"    />
        <result property="homeIntro"    column="home_intro"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="platform"    column="platform"    />
        <result property="labelOne"    column="label_one"    />
        <result property="labelTwo"    column="label_two"    />
        <result property="homeDataInformation"    column="home_data_information"    />
        <result property="individualNeeds"    column="individual_needs"    />
        <result property="enterprisesNeeds"    column="enterprises_needs"    />
        <result property="trainingAndActivities"    column="training_and_activities"    />
        <result property="help"    column="help"    />
        <result property="careerPlanningServicesQr"    column="career_planning_services_qr"    />
        <result property="weixinMiniProgramsQr"    column="weixin_mini_programs_qr"    />
        <result property="companyInfo"    column="company_info"    />
        <result property="internetContentProvider"    column="internet_content_provider"    />
        <result property="icp"    column="icp"    />
        <result property="backgroundTitle"    column="background_title"    />
        <result property="friendshipLink"    column="friendship_link"    />
        <result property="roleLabels"    column="role_labels"    />
        <result property="theme"    column="theme"    />
    </resultMap>

    <sql id="selectNekndPlatformConfigVo">
        select id, logo, title, home_intro, del_flag, platform, label_one, label_two, home_data_information, individual_needs, enterprises_needs, training_and_activities, help, career_planning_services_qr, weixin_mini_programs_qr, company_info, internet_content_provider, icp, background_title, friendship_link,role_labels,theme from neknd_platform_config
    </sql>

    <select id="selectNekndPlatformConfigList" parameterType="NekndPlatformConfig" resultMap="NekndPlatformConfigResult">
        <include refid="selectNekndPlatformConfigVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="backgroundTitle != null  and backgroundTitle != ''"> and background_title like concat('%', #{backgroundTitle}, '%')</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag=#{delFlag}</if>
            <if test="theme != null  and theme != ''"> and theme=#{theme}</if>
        </where>
    </select>
    
    <select id="selectNekndPlatformConfigById" parameterType="Integer" resultMap="NekndPlatformConfigResult">
        <include refid="selectNekndPlatformConfigVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndPlatformConfig" parameterType="NekndPlatformConfig" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_platform_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logo != null and logo != ''">logo,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="homeIntro != null and homeIntro != ''">home_intro,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="platform != null and platform != ''">platform,</if>
            <if test="labelOne != null and labelOne != ''">label_one,</if>
            <if test="labelTwo != null and labelTwo != ''">label_two,</if>
            <if test="homeDataInformation != null and homeDataInformation != ''">home_data_information,</if>
            <if test="individualNeeds != null and individualNeeds != ''">individual_needs,</if>
            <if test="enterprisesNeeds != null and enterprisesNeeds != ''">enterprises_needs,</if>
            <if test="trainingAndActivities != null and trainingAndActivities != ''">training_and_activities,</if>
            <if test="help != null and help != ''">help,</if>
            <if test="careerPlanningServicesQr != null and careerPlanningServicesQr != ''">career_planning_services_qr,</if>
            <if test="weixinMiniProgramsQr != null and weixinMiniProgramsQr != ''">weixin_mini_programs_qr,</if>
            <if test="companyInfo != null and companyInfo != ''">company_info,</if>
            <if test="internetContentProvider != null and internetContentProvider != ''">internet_content_provider,</if>
            <if test="icp != null and icp != ''">icp,</if>
            <if test="backgroundTitle != null and backgroundTitle != ''">background_title,</if>
            <if test="friendshipLink != null and friendshipLink != ''">friendship_link,</if>
            <if test="roleLabels != null and roleLabels != ''">role_labels,</if>
            <if test="theme != null and theme != ''">theme,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logo != null and logo != ''">#{logo},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="homeIntro != null and homeIntro != ''">#{homeIntro},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="platform != null and platform != ''">#{platform},</if>
            <if test="labelOne != null and labelOne != ''">#{labelOne},</if>
            <if test="labelTwo != null and labelTwo != ''">#{labelTwo},</if>
            <if test="homeDataInformation != null and homeDataInformation != ''">#{homeDataInformation},</if>
            <if test="individualNeeds != null and individualNeeds != ''">#{individualNeeds},</if>
            <if test="enterprisesNeeds != null and enterprisesNeeds != ''">#{enterprisesNeeds},</if>
            <if test="trainingAndActivities != null and trainingAndActivities != ''">#{trainingAndActivities},</if>
            <if test="help != null and help != ''">#{help},</if>
            <if test="careerPlanningServicesQr != null and careerPlanningServicesQr != ''">#{careerPlanningServicesQr},</if>
            <if test="weixinMiniProgramsQr != null and weixinMiniProgramsQr != ''">#{weixinMiniProgramsQr},</if>
            <if test="companyInfo != null and companyInfo != ''">#{companyInfo},</if>
            <if test="internetContentProvider != null and internetContentProvider != ''">#{internetContentProvider},</if>
            <if test="icp != null and icp != ''">#{icp},</if>
            <if test="backgroundTitle != null and backgroundTitle != ''">#{backgroundTitle},</if>
            <if test="friendshipLink != null and friendshipLink != ''">#{friendshipLink},</if>
            <if test="roleLabels != null and roleLabels != ''">#{roleLabels},</if>
            <if test="theme != null and theme != ''">#{theme},</if>
         </trim>
    </insert>

    <update id="updateNekndPlatformConfig" parameterType="NekndPlatformConfig">
        update neknd_platform_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="logo != null and logo != ''">logo = #{logo},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="homeIntro != null and homeIntro != ''">home_intro = #{homeIntro},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="labelOne != null and labelOne != ''">label_one = #{labelOne},</if>
            <if test="labelTwo != null and labelTwo != ''">label_two = #{labelTwo},</if>
            <if test="homeDataInformation != null and homeDataInformation != ''">home_data_information = #{homeDataInformation},</if>
            <if test="individualNeeds != null and individualNeeds != ''">individual_needs = #{individualNeeds},</if>
            <if test="enterprisesNeeds != null and enterprisesNeeds != ''">enterprises_needs = #{enterprisesNeeds},</if>
            <if test="trainingAndActivities != null and trainingAndActivities != ''">training_and_activities = #{trainingAndActivities},</if>
            <if test="help != null and help != ''">help = #{help},</if>
            <if test="careerPlanningServicesQr != null and careerPlanningServicesQr != ''">career_planning_services_qr = #{careerPlanningServicesQr},</if>
            <if test="weixinMiniProgramsQr != null and weixinMiniProgramsQr != ''">weixin_mini_programs_qr = #{weixinMiniProgramsQr},</if>
            <if test="companyInfo != null and companyInfo != ''">company_info = #{companyInfo},</if>
            <if test="internetContentProvider != null and internetContentProvider != ''">internet_content_provider = #{internetContentProvider},</if>
            <if test="icp != null and icp != ''">icp = #{icp},</if>
            <if test="backgroundTitle != null and backgroundTitle != ''">background_title = #{backgroundTitle},</if>
            <if test="friendshipLink != null and friendshipLink != ''">friendship_link = #{friendshipLink},</if>
            <if test="roleLabels != null and roleLabels != ''">role_labels = #{roleLabels},</if>
            <if test="theme != null and theme != ''">theme = #{theme},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndPlatformConfigById" parameterType="Integer">
        delete from neknd_platform_config where id = #{id}
    </delete>

    <delete id="deleteNekndPlatformConfigByIds" parameterType="String">
        delete from neknd_platform_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>