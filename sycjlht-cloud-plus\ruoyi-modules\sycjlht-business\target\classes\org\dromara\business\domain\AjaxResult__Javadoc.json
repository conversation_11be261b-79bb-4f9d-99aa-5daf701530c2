{"doc": " 操作消息提醒\n\n <AUTHOR>\n", "fields": [{"name": "CODE_TAG", "doc": "状态码 "}, {"name": "MSG_TAG", "doc": "返回内容 "}, {"name": "DATA_TAG", "doc": "数据对象 "}], "enumConstants": [], "methods": [{"name": "success", "paramTypes": [], "doc": " 返回成功消息\n\n @return 成功消息\n"}, {"name": "success", "paramTypes": ["java.lang.Object"], "doc": " 返回成功数据\n\n @return 成功消息\n"}, {"name": "success", "paramTypes": ["java.lang.String"], "doc": " 返回成功消息\n\n @param msg 返回内容\n @return 成功消息\n"}, {"name": "success", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 返回成功消息\n\n @param msg 返回内容\n @param data 数据对象\n @return 成功消息\n"}, {"name": "warn", "paramTypes": ["java.lang.String"], "doc": " 返回警告消息\n\n @param msg 返回内容\n @return 警告消息\n"}, {"name": "warn", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 返回警告消息\n\n @param msg 返回内容\n @param data 数据对象\n @return 警告消息\n"}, {"name": "error", "paramTypes": [], "doc": " 返回错误消息\n\n @return 错误消息\n"}, {"name": "error", "paramTypes": ["java.lang.String"], "doc": " 返回错误消息\n\n @param msg 返回内容\n @return 错误消息\n"}, {"name": "error", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 返回错误消息\n\n @param msg 返回内容\n @param data 数据对象\n @return 错误消息\n"}, {"name": "error", "paramTypes": ["int", "java.lang.String"], "doc": " 返回错误消息\n\n @param code 状态码\n @param msg 返回内容\n @return 错误消息\n"}, {"name": "isSuccess", "paramTypes": [], "doc": " 是否为成功消息\n\n @return 结果\n"}, {"name": "is<PERSON><PERSON>n", "paramTypes": [], "doc": " 是否为警告消息\n\n @return 结果\n"}, {"name": "isError", "paramTypes": [], "doc": " 是否为错误消息\n\n @return 结果\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 方便链式调用\n\n @param key 键\n @param value 值\n @return 数据对象\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。\n"}, {"name": "<init>", "paramTypes": ["int", "java.lang.String"], "doc": " 初始化一个新创建的 AjaxResult 对象\n\n @param code 状态码\n @param msg 返回内容\n"}, {"name": "<init>", "paramTypes": ["int", "java.lang.String", "java.lang.Object"], "doc": " 初始化一个新创建的 AjaxResult 对象\n\n @param code 状态码\n @param msg 返回内容\n @param data 数据对象\n"}]}