package org.dromara.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 政策新闻信息对象 neknd_policy_news
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("neknd_policy_news")
public class PolicyNews extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 政策新闻ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 封面图
     */
    private String coverUri;

    /**
     * 政策新闻标题
     */
    private String newsTitle;

    /**
     * 新闻类型（1职教动态 2职教新闻 3政策解读 4其他）
     */
    private String newsType;

    /**
     * 政策新闻内容
     */
    private String newsContent;

    /**
     * 新闻状态（0正常 1关闭）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 政策新闻来源名称
     */
    private String sourceTitle;

    /**
     * 政策类别（政策库字段）
     */
    private String policyCategory;

    /**
     * 政策类别二级筛选（政策库字段）
     */
    private String policy2category;

    /**
     * 文件类型（1指导意见 2实施方案 3政策文件）
     */
    private String fileType;

    /**
     * 是否为专题会议（0无 1是 2不是）
     */
    private String isThematicMeeting;

    /**
     * 方案类型（0无 1经费方案 2土地方案 3税收方案）
     */
    private String planType;

    /**
     * 所属园区
     */
    private String belongPark;

    /**
     * 新闻位置
     */
    private String newsPosition;

    /**
     * 文件类型（0无 1有资金 2无资金）
     */
    private String fileTypeFunds;

    /**
     * 所属园区
     */
    private String belongDistrict;

    // 非数据库字段，用于前端显示
    @TableField(exist = false)
    private Long previousId;

    @TableField(exist = false)
    private String previousTitle;

    @TableField(exist = false)
    private Long nextId;

    @TableField(exist = false)
    private String nextTitle;
}
