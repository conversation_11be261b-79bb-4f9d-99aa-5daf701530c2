{"doc": " 招聘岗位Mapper接口\n\n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": " 查询招聘岗位\n\n @param id 招聘岗位主键\n @return 招聘岗位\n"}, {"name": "selectNekndEmployList", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 查询招聘岗位列表\n\n @param nekndEmploy 招聘岗位\n @return 招聘岗位集合\n"}, {"name": "selectNekndEmployListReview", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 审核查询招聘岗位列表\n\n @param nekndEmploy 招聘岗位\n @return 招聘岗位集合\n"}, {"name": "insertNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 新增招聘岗位\n\n @param nekndEmploy 招聘岗位\n @return 结果\n"}, {"name": "updateNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 修改招聘岗位\n\n @param nekndEmploy 招聘岗位\n @return 结果\n"}, {"name": "deleteNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": " 删除招聘岗位\n\n @param id 招聘岗位主键\n @return 结果\n"}, {"name": "deleteNekndEmployByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除招聘岗位\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}