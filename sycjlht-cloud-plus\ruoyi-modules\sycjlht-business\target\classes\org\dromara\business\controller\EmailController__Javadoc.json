{"doc": " 邮件推荐控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendEmail", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发送简单邮件\n"}, {"name": "employEmailRecommended", "paramTypes": ["java.lang.Integer"], "doc": " 邮件推荐岗位\n 查询所有的用户，获取jobType，然后根据jobType查询所有正常的岗位，然后批量发送邮件\n 目前是填了期望岗位类型的用户，才会推荐岗位\n"}, {"name": "renderJobTemplate", "paramTypes": ["java.util.List"], "doc": " 渲染岗位模版\n"}, {"name": "personRecommended", "paramTypes": ["java.lang.Integer"], "doc": " 通过邮件推荐个人给公司\n\n @param userId 公司的userId ,传了公司的userId会只给这家公司推荐相应的人才\n"}, {"name": "renderPersonTemplate", "paramTypes": ["java.util.List"], "doc": " 渲染人才模版\n"}], "constructors": []}