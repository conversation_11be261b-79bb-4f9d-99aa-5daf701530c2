{"doc": " 省级管理Controller\n \n <AUTHOR>\n @date 2024-05-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取省级详细信息\n"}, {"name": "getList", "paramTypes": [], "doc": " 获取所有省份列表（湖北省咸宁市优先显示）\n"}, {"name": "list", "paramTypes": ["org.dromara.business.domain.NekndProvincial", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询省份列表（分页）\n"}, {"name": "search", "paramTypes": ["java.lang.String"], "doc": " 搜索省份\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取省份统计信息\n"}], "constructors": []}