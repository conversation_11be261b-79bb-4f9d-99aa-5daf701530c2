package org.dromara.business.domain;

import javax.annotation.processing.Generated;
import org.dromara.business.domain.vo.PolicyNewsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-10T20:08:19+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class PolicyNewsToPolicyNewsVoMapperImpl implements PolicyNewsToPolicyNewsVoMapper {

    @Override
    public PolicyNewsVo convert(PolicyNews arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PolicyNewsVo policyNewsVo = new PolicyNewsVo();

        policyNewsVo.setId( arg0.getId() );
        policyNewsVo.setCoverUri( arg0.getCoverUri() );
        policyNewsVo.setNewsTitle( arg0.getNewsTitle() );
        policyNewsVo.setNewsType( arg0.getNewsType() );
        policyNewsVo.setNewsContent( arg0.getNewsContent() );
        policyNewsVo.setStatus( arg0.getStatus() );
        policyNewsVo.setSourceTitle( arg0.getSourceTitle() );
        policyNewsVo.setPolicyCategory( arg0.getPolicyCategory() );
        policyNewsVo.setPolicy2category( arg0.getPolicy2category() );
        policyNewsVo.setFileType( arg0.getFileType() );
        policyNewsVo.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        policyNewsVo.setPlanType( arg0.getPlanType() );
        policyNewsVo.setBelongPark( arg0.getBelongPark() );
        policyNewsVo.setNewsPosition( arg0.getNewsPosition() );
        policyNewsVo.setFileTypeFunds( arg0.getFileTypeFunds() );
        policyNewsVo.setBelongDistrict( arg0.getBelongDistrict() );
        policyNewsVo.setCreateTime( arg0.getCreateTime() );
        policyNewsVo.setUpdateTime( arg0.getUpdateTime() );
        policyNewsVo.setPreviousId( arg0.getPreviousId() );
        policyNewsVo.setPreviousTitle( arg0.getPreviousTitle() );
        policyNewsVo.setNextId( arg0.getNextId() );
        policyNewsVo.setNextTitle( arg0.getNextTitle() );

        return policyNewsVo;
    }

    @Override
    public PolicyNewsVo convert(PolicyNews arg0, PolicyNewsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setCoverUri( arg0.getCoverUri() );
        arg1.setNewsTitle( arg0.getNewsTitle() );
        arg1.setNewsType( arg0.getNewsType() );
        arg1.setNewsContent( arg0.getNewsContent() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSourceTitle( arg0.getSourceTitle() );
        arg1.setPolicyCategory( arg0.getPolicyCategory() );
        arg1.setPolicy2category( arg0.getPolicy2category() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        arg1.setPlanType( arg0.getPlanType() );
        arg1.setBelongPark( arg0.getBelongPark() );
        arg1.setNewsPosition( arg0.getNewsPosition() );
        arg1.setFileTypeFunds( arg0.getFileTypeFunds() );
        arg1.setBelongDistrict( arg0.getBelongDistrict() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setPreviousId( arg0.getPreviousId() );
        arg1.setPreviousTitle( arg0.getPreviousTitle() );
        arg1.setNextId( arg0.getNextId() );
        arg1.setNextTitle( arg0.getNextTitle() );

        return arg1;
    }
}
