{"doc": " 文件处理工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "writeBytes", "paramTypes": ["java.lang.String", "java.io.OutputStream"], "doc": " 输出指定文件的byte数组\n\n @param filePath 文件路径\n @param os 输出流\n @return\n"}, {"name": "writeImportBytes", "paramTypes": ["byte[]"], "doc": " 写数据到文件中\n\n @param data 数据\n @return 目标文件\n @throws IOException IO异常\n"}, {"name": "writeBytes", "paramTypes": ["byte[]", "java.lang.String"], "doc": " 写数据到文件中\n\n @param data 数据\n @param uploadDir 目标文件\n @return 目标文件\n @throws IOException IO异常\n"}, {"name": "deleteFile", "paramTypes": ["java.lang.String"], "doc": " 删除文件\n\n @param filePath 文件\n @return\n"}, {"name": "isValidFilename", "paramTypes": ["java.lang.String"], "doc": " 文件名称验证\n\n @param filename 文件名称\n @return true 正常 false 非法\n"}, {"name": "checkAllowDownload", "paramTypes": ["java.lang.String"], "doc": " 检查文件是否可下载\n\n @param resource 需要下载的文件\n @return true 正常 false 非法\n"}, {"name": "setFileDownloadHeader", "paramTypes": ["jakarta.servlet.http.HttpServletRequest", "java.lang.String"], "doc": " 下载文件名重新编码\n\n @param request 请求对象\n @param fileName 文件名\n @return 编码后的文件名\n"}, {"name": "setAttachmentResponseHeader", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": " 下载文件名重新编码\n\n @param response 响应对象\n @param realFileName 真实文件名\n"}, {"name": "percentEncode", "paramTypes": ["java.lang.String"], "doc": " 百分号编码工具方法\n\n @param s 需要百分号编码的字符串\n @return 百分号编码后的字符串\n"}, {"name": "getFileExtendName", "paramTypes": ["byte[]"], "doc": " 获取图像后缀\n\n @param photoByte 图像数据\n @return 后缀名\n"}, {"name": "getName", "paramTypes": ["java.lang.String"], "doc": " 获取文件名称 /profile/upload/2022/04/16/ruoyi.png -- ruoyi.png\n\n @param fileName 路径名称\n @return 没有文件路径的名称\n"}, {"name": "getNameNotSuffix", "paramTypes": ["java.lang.String"], "doc": " 获取不带后缀文件名称 /profile/upload/2022/04/16/ruoyi.png -- ruoyi\n\n @param fileName 路径名称\n @return 没有文件路径和后缀的名称\n"}], "constructors": []}