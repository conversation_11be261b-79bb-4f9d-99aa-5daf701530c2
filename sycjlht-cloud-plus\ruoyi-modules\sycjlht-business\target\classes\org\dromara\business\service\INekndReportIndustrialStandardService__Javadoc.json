{"doc": " 报告管理Service接口\n\n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndReportIndustrialStandardById", "paramTypes": ["java.lang.Integer"], "doc": " 查询报告管理\n\n @param id 报告管理主键\n @return 报告管理\n"}, {"name": "selectNekndReportIndustrialStandardList", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": " 查询报告管理列表\n\n @param nekndReportIndustrialStandard 报告管理\n @return 报告管理集合\n"}, {"name": "insertNekndReportIndustrialStandard", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": " 新增报告管理\n\n @param nekndReportIndustrialStandard 报告管理\n @return 结果\n"}, {"name": "updateNekndReportIndustrialStandard", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": " 修改报告管理\n\n @param nekndReportIndustrialStandard 报告管理\n @return 结果\n"}, {"name": "deleteNekndReportIndustrialStandardByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除报告管理\n\n @param ids 需要删除的报告管理主键集合\n @return 结果\n"}, {"name": "deleteNekndReportIndustrialStandardById", "paramTypes": ["java.lang.Integer"], "doc": " 删除报告管理信息\n\n @param id 报告管理主键\n @return 结果\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新状态\n\n @param ids 主键集合\n @param status 状态\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询报告管理列表\n\n @param nekndReportIndustrialStandard 报告管理\n @param pageQuery 分页查询\n @return 报告管理集合\n"}, {"name": "queryPublicPageList", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询公开报告管理列表\n\n @param nekndReportIndustrialStandard 报告管理\n @param pageQuery 分页查询\n @return 报告管理集合\n"}], "constructors": []}