{"doc": " 供需管理控制器\n\n <AUTHOR>\n @date 2024-05-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询项目需求列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSupplyDemand"], "doc": " 导出项目需求列表\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取项目需求信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": " 新增项目需求\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": " 修改项目需求\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除项目需求\n"}, {"name": "dockingStatus", "paramTypes": ["java.lang.Integer"], "doc": " 门户网站查看对接状态\n"}, {"name": "dockingRecord", "paramTypes": ["java.lang.Integer", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 企业后台项目对接记录\n"}, {"name": "getInfoDetail", "paramTypes": ["java.lang.Integer"], "doc": " 获取项目需求详细信息\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询项目推荐列表\n"}, {"name": "sortList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询项目需求列表,并排序\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 企业查询自己的项目需求列表\n"}, {"name": "audit", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 审核项目需求\n 1.审核通过 2.审核不通过\n"}, {"name": "getListAudit", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查看项目审核列表\n"}, {"name": "getDemandStatus", "paramTypes": ["java.lang.Integer"], "doc": " 根据项目需求id查询项目需求的对接状态\n"}, {"name": "getData", "paramTypes": [], "doc": " 首页企业需求数据获取\n"}], "constructors": []}