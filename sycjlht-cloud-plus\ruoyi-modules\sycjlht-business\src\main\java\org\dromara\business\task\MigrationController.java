package org.dromara.business.task;

import cn.dev33.satoken.annotation.SaIgnore;
import org.dromara.business.domain.AjaxResult;
import org.dromara.business.service.DataMigrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("migration")
@RequestMapping("/migration")
public class MigrationController {

    @Autowired
    private DataMigrationService migrationService;

    @SaIgnore
    @GetMapping("/execute")
    public AjaxResult executeMigration() {
        return AjaxResult.success(migrationService.migrate());
    }
}
