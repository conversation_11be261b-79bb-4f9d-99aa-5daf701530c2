{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "paginateList", "paramTypes": ["java.util.List", "java.lang.Integer", "java.lang.Integer"], "doc": " 对给定的列表进行分页处理。\n\n @param <T> 列表元素的类型\n @param list    要分页的原始列表\n @param pageNum 页码，从1开始\n @param pageSize 每页显示的记录数\n @return 包含分页后数据和总记录数的TableDataInfo对象\n"}, {"name": "pageStart", "paramTypes": [], "doc": " 开始分页\n"}, {"name": "pageEnd", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 结束分页\n @param oldList 携带分页信息的列表\n @param list 需要分页的列表，分页后操作过的数据，没有分页信息，需要重新携带\n @param <T>\n @return\n"}], "constructors": []}