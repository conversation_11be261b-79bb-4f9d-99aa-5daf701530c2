{"doc": " 继续教育报名记录Service业务层处理\n\n <AUTHOR>\n @date 2024-10-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndJuniorcollegeUpgradeUndergraduateRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 查询继续教育报名记录\n\n @param id 继续教育报名记录主键\n @return 继续教育报名记录\n"}, {"name": "selectNekndJuniorcollegeUpgradeUndergraduateRecordsList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 查询继续教育报名记录列表\n\n @param nekndJuniorcollegeUpgradeUndergraduateRecords 继续教育报名记录\n @return 继续教育报名记录\n"}, {"name": "insertNekndJuniorcollegeUpgradeUndergraduateRecords", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 新增继续教育报名记录\n\n @param nekndJuniorcollegeUpgradeUndergraduateRecords 继续教育报名记录\n @return 结果\n"}, {"name": "updateNekndJuniorcollegeUpgradeUndergraduateRecords", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 修改继续教育报名记录\n\n @param nekndJuniorcollegeUpgradeUndergraduateRecords 继续教育报名记录\n @return 结果\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除继续教育报名记录\n\n @param ids 需要删除的继续教育报名记录主键\n @return 结果\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 删除继续教育报名记录信息\n\n @param id 继续教育报名记录主键\n @return 结果\n"}, {"name": "selectNekndJuniorcollegeUpgradeUndergraduateRecordsListForExport", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 查询导出用的继续教育报名记录列表\n"}], "constructors": []}