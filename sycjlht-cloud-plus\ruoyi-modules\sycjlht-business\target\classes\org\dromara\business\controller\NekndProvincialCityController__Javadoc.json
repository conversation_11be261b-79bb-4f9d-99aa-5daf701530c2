{"doc": " 市级管理Controller\n \n <AUTHOR>\n @date 2024-05-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": " 获取城市列表（湖北省咸宁市优先显示）\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取市级详细信息\n"}, {"name": "list", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询城市列表（分页）\n"}, {"name": "getByProvince", "paramTypes": ["java.lang.Long"], "doc": " 按省份ID查询城市\n"}, {"name": "search", "paramTypes": ["java.lang.String"], "doc": " 搜索城市\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取城市统计信息\n"}, {"name": "getPopular", "paramTypes": [], "doc": " 获取热门城市\n"}], "constructors": []}