{"doc": " 培训(证书/活动/项目)Service接口\n\n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": " 查询培训(证书/活动/项目)\n\n @param id 培训(证书/活动/项目)主键\n @return 培训(证书/活动/项目)\n"}, {"name": "selectNekndTrainList", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 查询培训(证书/活动/项目)列表\n\n @param nekndTrain 培训(证书/活动/项目)\n @return 培训(证书/活动/项目)集合\n"}, {"name": "selectPageNekndTrainList", "paramTypes": ["org.dromara.business.domain.NekndTrain", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询培训(证书/活动/项目)列表\n\n @param nekndTrain 培训(证书/活动/项目)\n @param pageQuery 分页参数\n @return 分页结果\n"}, {"name": "insertNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 新增培训(证书/活动/项目)\n\n @param nekndTrain 培训(证书/活动/项目)\n @return 结果\n"}, {"name": "updateNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 修改培训(证书/活动/项目)\n\n @param nekndTrain 培训(证书/活动/项目)\n @return 结果\n"}, {"name": "deleteNekndTrainByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除培训(证书/活动/项目)\n\n @param ids 需要删除的培训(证书/活动/项目)主键集合\n @return 结果\n"}, {"name": "deleteNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": " 删除培训(证书/活动/项目)信息\n\n @param id 培训(证书/活动/项目)主键\n @return 结果\n"}, {"name": "selectState", "paramTypes": ["java.lang.Integer"], "doc": " 查询是否为线上课程\n @param id\n @return\n"}], "constructors": []}