{"doc": " 师资评价Mapper接口\n\n <AUTHOR>\n @date 2024-12-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTeacherEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询师资评价\n\n @param id 师资评价主键\n @return 师资评价\n"}, {"name": "selectNekndTeacherEvaluationList", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": " 查询师资评价列表\n\n @param nekndTeacherEvaluation 师资评价\n @return 师资评价集合\n"}, {"name": "insertNekndTeacherEvaluation", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": " 新增师资评价\n\n @param nekndTeacherEvaluation 师资评价\n @return 结果\n"}, {"name": "updateNekndTeacherEvaluation", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": " 修改师资评价\n\n @param nekndTeacherEvaluation 师资评价\n @return 结果\n"}, {"name": "deleteNekndTeacherEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除师资评价\n\n @param id 师资评价主键\n @return 结果\n"}, {"name": "deleteNekndTeacherEvaluationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除师资评价\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}