<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndSubmitResumesMapper">
    
    <resultMap type="NekndSubmitResumes" id="NekndSubmitResumesResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="status"    column="status"    />
        <result property="userName"    column="user_name"    />
        <result property="userId"    column="employ_id"    />
        <result property="employName"    column="employ_name"    />
    </resultMap>

    <sql id="selectNekndSubmitResumesVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, user_id, dept_id, status,user_name,employ_id,employ_name
        from neknd_submit_resumes
    </sql>

    <select id="selectNekndSubmitResumesList" parameterType="NekndSubmitResumes" resultMap="NekndSubmitResumesResult">
        <include refid="selectNekndSubmitResumesVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndSubmitResumesById" parameterType="Integer" resultMap="NekndSubmitResumesResult">
        <include refid="selectNekndSubmitResumesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndSubmitResumes" parameterType="NekndSubmitResumes" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_submit_resumes
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="status != null">status,</if>
            <if test="userName != null">user_name,</if>
            <if test="employId != null">employ_id,</if>
            <if test="employName != null">employ_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="status != null">#{status},</if>
            <if test="userName != null">#{userName},</if>
            <if test="employId != null">#{employId},</if>
            <if test="employName != null">#{employName},</if>
         </trim>
    </insert>

    <update id="updateNekndSubmitResumes" parameterType="NekndSubmitResumes">
        update neknd_submit_resumes
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="employId != null">employ_id = #{employId},</if>
            <if test="employName != null">employ_name = #{employName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndSubmitResumesById" parameterType="Integer">
        delete from neknd_submit_resumes where id = #{id}
    </delete>

    <delete id="deleteNekndSubmitResumesByIds" parameterType="String">
        delete from neknd_submit_resumes where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>