{"doc": " 师资评价控制器\n \n <AUTHOR>\n @date 2024-12-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询师资评价列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndTeacherEvaluation"], "doc": " 导出师资评价列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取师资评价详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": " 新增师资评价\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": " 修改师资评价\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除师资评价\n"}], "constructors": []}