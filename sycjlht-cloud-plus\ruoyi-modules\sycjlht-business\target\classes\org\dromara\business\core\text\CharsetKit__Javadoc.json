{"doc": " 字符集工具类\n\n <AUTHOR>\n", "fields": [{"name": "ISO_8859_1", "doc": "ISO-8859-1 "}, {"name": "UTF_8", "doc": "UTF-8 "}, {"name": "GBK", "doc": "GBK "}, {"name": "CHARSET_ISO_8859_1", "doc": "ISO-8859-1 "}, {"name": "CHARSET_UTF_8", "doc": "UTF-8 "}, {"name": "CHARSET_GBK", "doc": "GBK "}], "enumConstants": [], "methods": [{"name": "charset", "paramTypes": ["java.lang.String"], "doc": " 转换为Charset对象\n\n @param charset 字符集，为空则返回默认字符集\n @return Charset\n"}, {"name": "convert", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 转换字符串的字符集编码\n\n @param source 字符串\n @param srcCharset 源字符集，默认ISO-8859-1\n @param destCharset 目标字符集，默认UTF-8\n @return 转换后的字符集\n"}, {"name": "convert", "paramTypes": ["java.lang.String", "java.nio.charset.Charset", "java.nio.charset.Charset"], "doc": " 转换字符串的字符集编码\n\n @param source 字符串\n @param srcCharset 源字符集，默认ISO-8859-1\n @param destCharset 目标字符集，默认UTF-8\n @return 转换后的字符集\n"}, {"name": "systemCharset", "paramTypes": [], "doc": " @return 系统字符集编码\n"}], "constructors": []}