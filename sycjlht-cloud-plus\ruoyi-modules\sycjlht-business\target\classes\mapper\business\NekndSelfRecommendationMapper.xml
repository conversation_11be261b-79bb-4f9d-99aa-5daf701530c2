<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndSelfRecommendationMapper">
    
    <resultMap type="NekndSelfRecommendation" id="NekndSelfRecommendationResult">
        <result property="id"    column="id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="content"    column="content"    />
        <result property="money"    column="money"    />
        <result property="completionTime"    column="completion_time"    />
        <result property="unit"    column="unit"    />
        <result property="phone"    column="phone"    />
        <result property="requirementId"    column="requirement_id"    />
        <result property="requirementName"    column="requirement_name"    />
        <result property="serviceProviderId"    column="service_provider_id"    />
        <result property="serviceProviderName"    column="service_provider_name"    />
        <result property="status"    column="status"    />
        <result property="dockingStatus"    column="docking_status"    />

    </resultMap>

    <sql id="selectNekndSelfRecommendationVo">
        select id, create_by, create_time, update_by, update_time, content, money, completion_time, unit, phone, requirement_name,requirement_id,service_provider_name,service_provider_id,status,docking_status from neknd_self_recommendation
    </sql>

    <select id="selectNekndSelfRecommendationList" parameterType="NekndSelfRecommendation" resultType="NekndSelfRecommendation">
        <include refid="selectNekndSelfRecommendationVo"/>
        <where>  
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="money != null  and money != ''"> and money = #{money}</if>
            <if test="completionTime != null "> and completion_time = #{completionTime}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="requirementName != null  and requirementName != ''"> and requirement_name = #{requirementName}</if>
            <if test="serviceProviderName != null  and serviceProviderName != ''"> and service_provider_name = #{serviceProviderName}</if>
            <if test="requirementId != null  and requirementId != ''"> and requirement_id = #{requirementId}</if>
            <if test="serviceProviderId != null  and serviceProviderId != ''"> and service_provider_id = #{serviceProviderId}</if>
            <if test="dockingStatus != null  and dockingStatus != ''"> and docking_status = #{dockingStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndSelfRecommendationById" parameterType="Integer" >
        <include refid="selectNekndSelfRecommendationVo"/>
        where id = #{id}
    </select>


    <select id="getListByrequirementId" resultType="com.neknd.system.domain.NekndSelfRecommendation">
        <include refid="selectNekndSelfRecommendationVo"/>
        where requirement_id = #{requirementId} and status = 1
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        select count(1) from neknd_self_recommendation
        where requirement_id = #{requirementId}
--           and status = 1
    </select>
    <select id="getDockingRecord" parameterType="Map" resultType="com.neknd.system.domain.NekndSelfRecommendation">
<!--        <include refid="selectNekndSelfRecommendationVo"/>-->
<!--        where requirement_id = #{requirementId} and status = 1-->
        SELECT
        a.id as id,
        a.create_by as create_by,
        a.create_time as create_time,
        a.update_by as update_by,
        a.update_time as update_time,
        a.content as content,
        a.money as money,
        a.completion_time as completion_time,
        a.unit as unit,
        a.phone as phone ,
        a.requirement_name as requirement_name,
        a.requirement_id as requirement_id,
        a.service_provider_name as service_provider_name,
        a.service_provider_id as service_provider_id,
        a.status as status,
        a.docking_status as docking_status
        FROM
        neknd_self_recommendation as a left join neknd_supply_demand as b
        on a.requirement_id = b.id
        <where>
            a.status = 1
            <if test="requirementId != null  and requirementId != '' and requirementId !=0"> and a.requirement_id = #{requirementId}</if>
            <if test="deptId != null  and deptId != '' "> and b.dept_id =#{deptId}</if>
            <if test="money != null  and money != ''"> and a.money = #{money}</if>
            <if test="completionTime != null "> and  a.completion_time = #{completionTime}</if>
            <if test="unit != null  and unit != ''"> and  a.unit = #{unit}</if>
            <if test="phone != null  and phone != ''"> and  a.phone = #{phone}</if>
            <if test="requirementName != null  and requirementName != ''"> and  a.requirement_name = #{requirementName}</if>
        </where>
        order by a.create_time desc
    </select>

    <insert id="insertNekndSelfRecommendation" parameterType="NekndSelfRecommendation">
        insert into neknd_self_recommendation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="content != null">content,</if>
            <if test="money != null">money,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="unit != null">unit,</if>
            <if test="phone != null">phone,</if>
            <if test="requirementId != null">requirement_id,</if>
            <if test="requirementName != null and requirementName != ''">requirement_name,</if>
            <if test="serviceProviderId != null and serviceProviderId != ''">service_provider_id,</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">service_provider_name,</if>
            <if test="dockingStatus != null and dockingStatus != ''">docking_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="content != null">#{content},</if>
            <if test="money != null">#{money},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="unit != null">#{unit},</if>
            <if test="phone != null">#{phone},</if>
            <if test="requirementId != null">#{requirementId},</if>
            <if test="requirementName != null and requirementName != ''">#{requirementName},</if>
            <if test="serviceProviderId != null and serviceProviderId != ''">#{serviceProviderId},</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">#{serviceProviderName},</if>
            <if test="dockingStatus != null and dockingStatus != ''">#{dockingStatus},</if>
         </trim>
    </insert>

    <update id="updateNekndSelfRecommendation" parameterType="NekndSelfRecommendation">
        update neknd_self_recommendation
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="content != null">content = #{content},</if>
            <if test="money != null">money = #{money},</if>
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="requirementId != null">requirement_id = #{requirementId},</if>
            <if test="requirementName != null and requirementName != ''">requirement_name = #{requirementName},</if>
            <if test="serviceProviderId != null and serviceProviderId != ''">service_provider_id = #{serviceProviderId},</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">service_provider_name = #{serviceProviderName},</if>
            <if test="dockingStatus != null and dockingStatus != ''">docking_status = #{dockingStatus},</if>
            <if test="status != null and status != ''">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndSelfRecommendationById" parameterType="Integer">
        delete from neknd_self_recommendation where id = #{id}
    </delete>

    <delete id="deleteNekndSelfRecommendationByIds" parameterType="String">
        delete from neknd_self_recommendation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>