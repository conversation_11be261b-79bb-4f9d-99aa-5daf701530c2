{"doc": " ID生成器工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "randomUUID", "paramTypes": [], "doc": " 获取随机UUID\n\n @return 随机UUID\n"}, {"name": "simpleUUID", "paramTypes": [], "doc": " 简化的UUID，去掉了横线\n\n @return 简化的UUID，去掉了横线\n"}, {"name": "fastUUID", "paramTypes": [], "doc": " 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID\n\n @return 随机UUID\n"}, {"name": "fastSimpleUUID", "paramTypes": [], "doc": " 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID\n\n @return 简化的UUID，去掉了横线\n"}], "constructors": []}