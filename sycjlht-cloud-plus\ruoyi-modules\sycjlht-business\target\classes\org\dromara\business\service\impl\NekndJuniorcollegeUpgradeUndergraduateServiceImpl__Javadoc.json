{"doc": " 继续教育信息Service业务层处理\n\n <AUTHOR>\n @date 2024-10-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndJuniorcollegeUpgradeUndergraduateById", "paramTypes": ["java.lang.Integer"], "doc": " 查询继续教育信息\n\n @param id 继续教育信息主键\n @return 继续教育信息\n"}, {"name": "selectNekndJuniorcollegeUpgradeUndergraduateList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 查询继续教育信息列表\n\n @param nekndJuniorcollegeUpgradeUndergraduate 继续教育信息\n @return 继续教育信息\n"}, {"name": "insertNekndJuniorcollegeUpgradeUndergraduate", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 新增继续教育信息\n\n @param nekndJuniorcollegeUpgradeUndergraduate 继续教育信息\n @return 结果\n"}, {"name": "updateNekndJuniorcollegeUpgradeUndergraduate", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 修改继续教育信息\n\n @param nekndJuniorcollegeUpgradeUndergraduate 继续教育信息\n @return 结果\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除继续教育信息\n\n @param ids 需要删除的继续教育信息主键\n @return 结果\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateById", "paramTypes": ["java.lang.Integer"], "doc": " 删除继续教育信息信息\n\n @param id 继续教育信息主键\n @return 结果\n"}, {"name": "selectNekndJuniorcollegeUpgradeUndergraduateListForExport", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 查询导出用的继续教育信息列表\n"}], "constructors": []}