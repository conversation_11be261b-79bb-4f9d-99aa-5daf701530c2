{"doc": " 资源共建（校企合作）Controller\n \n <AUTHOR>\n @date 2024-11-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 查询资源共建（校企合作）列表\n"}, {"name": "listPage", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 查询资源共建（校企合作）列表（分页）\n"}, {"name": "listCompany", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 查询资源共建（校企合作）企业列表\n"}, {"name": "listGovernment", "paramTypes": [], "doc": " 查询资源共建（校企合作）政府列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndResourcesConservation"], "doc": " 导出资源共建（校企合作）列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入资源共建信息\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取资源共建（校企合作）详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 新增资源共建（校企合作）\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 修改资源共建（校企合作）\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除资源共建（校企合作）\n"}, {"name": "getList", "paramTypes": [], "doc": " 根据部门ID获取资源共建统计列表\n"}, {"name": "getSortList", "paramTypes": [], "doc": " 根据部门ID获取资源共建排序列表\n"}], "constructors": []}