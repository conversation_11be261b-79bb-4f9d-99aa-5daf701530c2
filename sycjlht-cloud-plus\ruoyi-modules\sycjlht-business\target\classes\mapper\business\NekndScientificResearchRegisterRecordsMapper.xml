<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndScientificResearchRegisterRecordsMapper">
    
    <resultMap type="NekndScientificResearchRegisterRecords" id="NekndScientificResearchRegisterRecordsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="scientificResearchTime"    column="scientific_research_time"    />
        <result property="scientificResearchNumber"    column="scientific_research_number"    />
        <result property="scientificResearchId"    column="scientific_research_id"    />
        <result property="scientificResearchTitle"    column="scientific_research_title"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="address"    column="address"    />
        <result property="graduateStage"    column="graduate_stage"    />
        <result property="days"    column="days"    />
        <result property="cost"    column="cost"    />
        <result property="scientificResearchAdultNumber"    column="scientific_research_adult_number"    />
        <result property="scientificResearchChildrenNumber"    column="scientific_research_children_number"    />
    </resultMap>

    <sql id="selectNekndScientificResearchRegisterRecordsVo">
        select id, user_id, name, phone, scientific_research_time, scientific_research_number, scientific_research_id,
               scientific_research_title, dept_id, dept_name, del_flag,create_by, create_time, update_by, update_time,
               address,graduate_stage,days,cost,scientific_research_adult_number,scientific_research_children_number
        from neknd_scientific_research_register_records
    </sql>

    <select id="selectNekndScientificResearchRegisterRecordsList" parameterType="NekndScientificResearchRegisterRecords" resultMap="NekndScientificResearchRegisterRecordsResult">
        <include refid="selectNekndScientificResearchRegisterRecordsVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="scientificResearchTime != null "> and scientific_research_time = #{scientificResearchTime}</if>
            <if test="scientificResearchNumber != null "> and scientific_research_number = #{scientificResearchNumber}</if>
            <if test="scientificResearchTitle != null  and scientificResearchTitle != ''"> and scientific_research_title like concat('%', #{scientificResearchTitle}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="deptId != null  and deptId != ''"> and dept_id = #{deptId}</if>
            <if test="scientificResearchAdultNumber != null  and scientificResearchAdultNumber != ''"> and scientific_research_adult_number = #{scientificResearchAdultNumber}</if>
            <if test="scientificResearchChildrenNumber != null  and scientificResearchChildrenNumber != ''"> and scientific_research_children_number = #{scientificResearchChildrenNumber}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndScientificResearchRegisterRecordsById" parameterType="Long" resultMap="NekndScientificResearchRegisterRecordsResult">
        <include refid="selectNekndScientificResearchRegisterRecordsVo"/>
        where id = #{id}
    </select>
    <select id="selectScientificResearchStudentsByScientificResearchId"
            resultMap="NekndScientificResearchRegisterRecordsResult">
        <include refid="selectNekndScientificResearchRegisterRecordsVo"/>
        where scientific_research_id = #{scientificResearchId} and del_flag = 0
        order by create_time desc
    </select>

    <insert id="insertNekndScientificResearchRegisterRecords" parameterType="NekndScientificResearchRegisterRecords" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_scientific_research_register_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="scientificResearchTime != null">scientific_research_time,</if>
            <if test="scientificResearchNumber != null">scientific_research_number,</if>
            <if test="scientificResearchId != null">scientific_research_id,</if>
            <if test="scientificResearchTitle != null">scientific_research_title,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="graduateStage != null and graduateStage != ''">graduate_stage,</if>
            <if test="days != null and days != ''">days,</if>
            <if test="cost != null and cost != ''">cost,</if>
            <if test="scientificResearchAdultNumber != null and scientificResearchAdultNumber != ''">scientific_research_adult_number,</if>
            <if test="scientificResearchChildrenNumber != null and scientificResearchChildrenNumber != ''">scientific_research_children_number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="scientificResearchTime != null">#{scientificResearchTime},</if>
            <if test="scientificResearchNumber != null">#{scientificResearchNumber},</if>
            <if test="scientificResearchId != null">#{scientificResearchId},</if>
            <if test="scientificResearchTitle != null">#{scientificResearchTitle},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="graduateStage != null and graduateStage != ''">#{graduateStage},</if>
            <if test="days != null and days != ''">#{days},</if>
            <if test="cost != null and cost != ''">#{cost},</if>
            <if test="scientificResearchAdultNumber != null and scientificResearchAdultNumber != ''">#{scientificResearchAdultNumber},</if>
            <if test="scientificResearchChildrenNumber != null and scientificResearchChildrenNumber != ''">#{scientificResearchChildrenNumber},</if>
         </trim>
    </insert>

    <update id="updateNekndScientificResearchRegisterRecords" parameterType="NekndScientificResearchRegisterRecords">
        update neknd_scientific_research_register_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="scientificResearchTime != null">scientific_research_time = #{scientificResearchTime},</if>
            <if test="scientificResearchNumber != null">scientific_research_number = #{scientificResearchNumber},</if>
            <if test="scientificResearchId != null">scientific_research_id = #{scientificResearchId},</if>
            <if test="scientificResearchTitle != null">scientific_research_title = #{scientificResearchTitle},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="graduateStage != null and graduateStage != ''">graduate_stage = #{graduateStage},</if>
            <if test="days != null and days != ''">days = #{days},</if>
            <if test="cost != null and cost != ''">cost = #{cost},</if>
            <if test="scientificResearchAdultNumber != null and scientificResearchAdultNumber != ''">scientific_research_adult_number = #{scientificResearchAdultNumber},</if>
            <if test="scientificResearchChildrenNumber != null and scientificResearchChildrenNumber != ''">scientific_research_children_number = #{scientificResearchChildrenNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndScientificResearchRegisterRecordsById" parameterType="Long">
        delete from neknd_scientific_research_register_records where id = #{id}
    </delete>

    <delete id="deleteNekndScientificResearchRegisterRecordsByIds" parameterType="String">
        delete from neknd_scientific_research_register_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>