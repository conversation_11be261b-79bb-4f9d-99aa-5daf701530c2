{"doc": " 平台首页信息对象 neknd_platform_config\n\n <AUTHOR>\n @date 2025-02-05\n", "fields": [{"name": "id", "doc": "平台首页信息信息id "}, {"name": "logo", "doc": "首页LOGO "}, {"name": "title", "doc": "首页标题 "}, {"name": "homeIntro", "doc": "首页简介 "}, {"name": "delFlag", "doc": "删除标志（0代表存在 2代表删除） "}, {"name": "platform", "doc": "平台标志（0代表联合体 1代表共同体） "}, {"name": "labelOne", "doc": "首页标签1 "}, {"name": "labelTwo", "doc": "首页标签2 "}, {"name": "homeDataInformation", "doc": "首页数据信息 "}, {"name": "individualNeeds", "doc": "个人需求 "}, {"name": "enterprisesNeeds", "doc": "企业需求 "}, {"name": "trainingAndActivities", "doc": "培训与活动 "}, {"name": "help", "doc": "帮助 "}, {"name": "careerPlanningServicesQr", "doc": "职业规划服务二维码 "}, {"name": "weixinMiniProgramsQr", "doc": "微信小程序二维码 "}, {"name": "companyInfo", "doc": "底部公司信息 "}, {"name": "internetContentProvider", "doc": "底部备案号 "}, {"name": "icp", "doc": "底部ICP许可证 "}, {"name": "backgroundTitle", "doc": "后台标题 "}, {"name": "friendshipLink", "doc": "友情链接 "}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "角色标签 "}, {"name": "theme", "doc": "主题颜色 "}], "enumConstants": [], "methods": [], "constructors": []}