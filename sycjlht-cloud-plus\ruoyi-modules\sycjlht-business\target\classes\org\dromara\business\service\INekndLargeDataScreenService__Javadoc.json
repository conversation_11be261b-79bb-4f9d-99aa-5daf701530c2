{"doc": " 数据大屏Service接口\n\n <AUTHOR>\n @date 2025-04-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndLargeDataScreenById", "paramTypes": ["java.lang.Long"], "doc": " 查询数据大屏\n\n @param id 数据大屏主键\n @return 数据大屏\n"}, {"name": "selectNekndLargeDataScreenList", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 查询数据大屏列表\n\n @param nekndLargeDataScreen 数据大屏\n @return 数据大屏集合\n"}, {"name": "insertNekndLargeDataScreen", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 新增数据大屏\n\n @param nekndLargeDataScreen 数据大屏\n @return 结果\n"}, {"name": "updateNekndLargeDataScreen", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 修改数据大屏\n\n @param nekndLargeDataScreen 数据大屏\n @return 结果\n"}, {"name": "deleteNekndLargeDataScreenByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除数据大屏\n\n @param ids 需要删除的数据大屏主键集合\n @return 结果\n"}, {"name": "deleteNekndLargeDataScreenById", "paramTypes": ["java.lang.Long"], "doc": " 删除数据大屏信息\n\n @param id 数据大屏主键\n @return 结果\n"}, {"name": "getRealtimeData", "paramTypes": ["java.lang.String"], "doc": " 获取实时数据\n\n @param type 数据类型\n @return 实时数据\n"}, {"name": "refreshScreenData", "paramTypes": ["java.lang.String"], "doc": " 刷新屏幕数据\n\n @param type 数据类型\n @return 刷新结果\n"}], "constructors": []}