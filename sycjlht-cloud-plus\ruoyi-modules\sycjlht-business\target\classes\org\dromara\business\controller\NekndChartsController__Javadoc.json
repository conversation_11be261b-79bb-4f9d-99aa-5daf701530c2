{"doc": " 图表管理Controller\n \n <AUTHOR>\n @date 2025-01-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCharts", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询图表管理列表\n"}, {"name": "getPublicList", "paramTypes": ["org.dromara.business.domain.NekndCharts", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户查询图表管理列表（公开接口）\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCharts"], "doc": " 导出图表管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取图表管理详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": " 新增图表管理\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": " 修改图表管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除图表管理\n"}, {"name": "getChartsStatistics", "paramTypes": [], "doc": " 获取图表统计数据（用于仪表板）\n"}, {"name": "getChartData", "paramTypes": ["java.lang.Integer"], "doc": " 获取图表数据（用于前端图表展示）\n"}], "constructors": []}