{"doc": " 报告信息管理Controller\n \n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询报告信息列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户查询报告信息列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndReportInformation"], "doc": " 导出报告信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取报告信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": " 新增报告信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": " 修改报告信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除报告信息\n"}, {"name": "applyAccess", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 申请报告访问权限\n"}, {"name": "auditAccess", "paramTypes": ["java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 审核报告访问权限申请\n"}, {"name": "getMyPrivileges", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取用户的权限申请记录\n"}], "constructors": []}