{"doc": " 留言主题Service业务层处理\n\n <AUTHOR>\n @date 2024-08-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": " 查询留言主题\n\n @param id 留言主题主键\n @return 留言主题\n"}, {"name": "selectNekndMessageTopicList", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 查询留言主题列表\n\n @param nekndMessageTopic 留言主题\n @return 留言主题\n"}, {"name": "insertNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 新增留言主题\n\n @param nekndMessageTopic 留言主题\n @return 结果\n"}, {"name": "updateNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 修改留言主题\n\n @param nekndMessageTopic 留言主题\n @return 结果\n"}, {"name": "deleteNekndMessageTopicByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除留言主题\n\n @param ids 需要删除的留言主题主键\n @return 结果\n"}, {"name": "deleteNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": " 删除留言主题信息\n\n @param id 留言主题主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询分页列表\n\n @param nekndMessageTopic 查询条件\n @param pageQuery 分页参数\n @return 留言主题集合\n"}, {"name": "createTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 创建主题\n\n @param nekndMessageTopic 留言主题\n @return 结果\n"}, {"name": "logicalDeleteForUser", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 为用户逻辑删除\n\n @param topicId 主题ID\n @param userId 用户ID\n @return 结果\n"}, {"name": "markTopicAsRead", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 标记主题为已读\n\n @param topicId 主题ID\n @param userId 用户ID\n @return 结果\n"}, {"name": "getUnreadTopicCount", "paramTypes": ["java.lang.Long"], "doc": " 获取未读主题数量\n\n @param userId 用户ID\n @return 未读数量\n"}, {"name": "searchTopicsForUser", "paramTypes": ["java.lang.Long", "java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 为用户搜索主题\n\n @param userId 用户ID\n @param keyword 关键词  \n @param pageQuery 分页参数\n @return 主题集合\n"}], "constructors": []}