{"doc": " 产教融合项目Service业务层处理\n\n <AUTHOR>\n @date 2024-10-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": " 查询产教融合项目\n\n @param id 产教融合项目主键\n @return 产教融合项目\n"}, {"name": "selectNekndIndustryEducationProjectsList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 查询产教融合项目列表\n\n @param nekndIndustryEducationProjects 产教融合项目\n @return 产教融合项目\n"}, {"name": "insertNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 新增产教融合项目\n\n @param nekndIndustryEducationProjects 产教融合项目\n @return 结果\n"}, {"name": "updateNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 修改产教融合项目\n\n @param nekndIndustryEducationProjects 产教融合项目\n @return 结果\n"}, {"name": "deleteNekndIndustryEducationProjectsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除产教融合项目\n\n @param ids 需要删除的产教融合项目主键\n @return 结果\n"}, {"name": "deleteNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": " 删除产教融合项目信息\n\n @param id 产教融合项目主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询产教融合项目列表\n"}, {"name": "getProjectStatisticsByType", "paramTypes": [], "doc": " 按类型获取项目统计信息\n"}, {"name": "getProjectStatisticsByStatus", "paramTypes": [], "doc": " 按状态获取项目统计信息\n"}, {"name": "queryPopularProjects", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询热门项目\n"}], "constructors": []}