{"doc": " 市级Mapper接口\n\n <AUTHOR>\n @date 2024-05-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndProvincialCityByCid", "paramTypes": ["java.lang.Long"], "doc": " 查询市级\n\n @param cid 市级主键\n @return 市级\n"}, {"name": "selectNekndProvincialCityList", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": " 查询市级列表\n\n @param nekndProvincialCity 市级\n @return 市级集合\n"}, {"name": "insertNekndProvincialCity", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": " 新增市级\n\n @param nekndProvincialCity 市级\n @return 结果\n"}, {"name": "updateNekndProvincialCity", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": " 修改市级\n\n @param nekndProvincialCity 市级\n @return 结果\n"}, {"name": "deleteNekndProvincialCityByCid", "paramTypes": ["java.lang.Long"], "doc": " 删除市级\n\n @param cid 市级主键\n @return 结果\n"}, {"name": "deleteNekndProvincialCityByCids", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除市级\n\n @param cids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}