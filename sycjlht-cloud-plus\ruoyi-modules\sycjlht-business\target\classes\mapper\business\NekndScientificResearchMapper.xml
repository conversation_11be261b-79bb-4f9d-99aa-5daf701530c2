<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndScientificResearchMapper">
    
    <resultMap type="NekndScientificResearch" id="NekndScientificResearchResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="address"    column="address"    />
        <result property="graduateStage"    column="graduate_stage"    />
        <result property="days"    column="days"    />
        <result property="cost"    column="cost"    />
        <result property="phone"    column="phone"    />
        <result property="detailedIntroduction"    column="detailed_introduction"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="studyType"    column="study_type"    />
        <result property="honorQualification"    column="honor_qualification"    />
        <result property="security"    column="security"    />
        <result property="modesOfTransportation"    column="modes_of_transportation"    />
        <result property="other"    column="other"    />
    </resultMap>

    <sql id="selectNekndScientificResearchVo">
        select id, cover_uri, title, content, address, graduate_stage, days, cost, phone, detailed_introduction, remark, del_flag, create_id, create_by, create_time, update_by, update_time,review_status,study_type, honor_qualification, security, modes_of_transportation, other from neknd_scientific_research
    </sql>

    <select id="selectNekndScientificResearchList" parameterType="NekndScientificResearch" resultMap="NekndScientificResearchResult">
        <include refid="selectNekndScientificResearchVo"/>
        <where>
            del_flag = 0
            <if test="searchValue != null  and searchValue != ''"> and title like concat('%', #{searchValue}, '%')</if>
<!--            <if test="graduateStage != null  and graduateStage != ''"> and graduate_stage = #{graduateStage}</if>-->
            <if test="days != null  and days != ''"> and days = #{days}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="createId != null  and createId != ''"> and create_id = #{createId}</if>
            <if test="reviewStatus != null  and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
            <if test="studyType != null  and studyType != ''"> and study_type = #{studyType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndScientificResearchById" parameterType="Integer" resultMap="NekndScientificResearchResult">
        <include refid="selectNekndScientificResearchVo"/>
        where id = #{id}
    </select>
    <select id="getCountScientificResearches" resultType="java.lang.Integer">
        select count(1) from neknd_scientific_research where del_flag = 0 and review_status = 1
    </select>

    <insert id="insertNekndScientificResearch" parameterType="NekndScientificResearch" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_scientific_research
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">cover_uri,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="graduateStage != null and graduateStage != ''">graduate_stage,</if>
            <if test="days != null and days != ''">days,</if>
            <if test="cost != null and cost != ''">cost,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="detailedIntroduction != null and detailedIntroduction != ''">detailed_introduction,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="studyType != null">study_type,</if>
            <if test="honorQualification != null">honor_qualification,</if>
            <if test="security != null">security,</if>
            <if test="modesOfTransportation != null">modes_of_transportation,</if>
            <if test="other != null">other,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">#{coverUri},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="graduateStage != null and graduateStage != ''">#{graduateStage},</if>
            <if test="days != null and days != ''">#{days},</if>
            <if test="cost != null and cost != ''">#{cost},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="detailedIntroduction != null and detailedIntroduction != ''">#{detailedIntroduction},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="studyType != null">#{studyType},</if>
            <if test="honorQualification != null">#{honorQualification},</if>
            <if test="security != null">#{security},</if>
            <if test="modesOfTransportation != null">#{modesOfTransportation},</if>
            <if test="other != null">#{other},</if>
         </trim>
    </insert>

    <update id="updateNekndScientificResearch" parameterType="NekndScientificResearch">
        update neknd_scientific_research
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="graduateStage != null and graduateStage != ''">graduate_stage = #{graduateStage},</if>
            <if test="days != null and days != ''">days = #{days},</if>
            <if test="cost != null and cost != ''">cost = #{cost},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="detailedIntroduction != null and detailedIntroduction != ''">detailed_introduction = #{detailedIntroduction},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="studyType != null">study_type = #{studyType},</if>
            <if test="honorQualification != null">honor_qualification = #{honorQualification},</if>
            <if test="security != null">security = #{security},</if>
            <if test="modesOfTransportation != null">modes_of_transportation = #{modesOfTransportation},</if>
            <if test="other != null">other = #{other},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAuditing" >
        update neknd_scientific_research set review_status = #{reviewStatus} where id=#{id}
    </update>

    <delete id="deleteNekndScientificResearchById" parameterType="Integer">
        delete from neknd_scientific_research where id = #{id}
    </delete>

    <delete id="deleteNekndScientificResearchByIds" parameterType="String">
        delete from neknd_scientific_research where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>