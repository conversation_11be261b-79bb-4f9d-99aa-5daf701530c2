{"doc": " 科普研学导师Service接口\n\n <AUTHOR>\n @date 2025-06-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchMentorById", "paramTypes": ["java.lang.Integer"], "doc": " 查询科普研学导师\n\n @param id 科普研学导师主键\n @return 科普研学导师\n"}, {"name": "selectNekndScientificResearchMentorList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": " 查询科普研学导师列表\n\n @param nekndScientificResearchMentor 科普研学导师\n @return 科普研学导师集合\n"}, {"name": "insertNekndScientificResearchMentor", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": " 新增科普研学导师\n\n @param nekndScientificResearchMentor 科普研学导师\n @return 结果\n"}, {"name": "updateNekndScientificResearchMentor", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": " 修改科普研学导师\n\n @param nekndScientificResearchMentor 科普研学导师\n @return 结果\n"}, {"name": "deleteNekndScientificResearchMentorByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除科普研学导师\n\n @param ids 需要删除的科普研学导师主键集合\n @return 结果\n"}, {"name": "deleteNekndScientificResearchMentorById", "paramTypes": ["java.lang.Integer"], "doc": " 删除科普研学导师信息\n\n @param id 科普研学导师主键\n @return 结果\n"}], "constructors": []}