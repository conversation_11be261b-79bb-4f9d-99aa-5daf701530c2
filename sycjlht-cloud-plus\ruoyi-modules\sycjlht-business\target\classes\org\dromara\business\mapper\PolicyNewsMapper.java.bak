package org.dromara.business.mapper;

import org.dromara.business.domain.PolicyNews;
import org.dromara.business.domain.vo.PolicyNewsVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 政策新闻信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface PolicyNewsMapper extends BaseMapperPlus<PolicyNews, PolicyNewsVo> {

    /**
     * 查询上一条新闻
     *
     * @param newsType 新闻类型
     * @param currentId 当前新闻ID
     * @return 上一条新闻
     */
    PolicyNewsVo selectPreviousNews(@Param("newsType") String newsType, @Param("currentId") Long currentId);

    /**
     * 查询下一条新闻
     *
     * @param newsType 新闻类型
     * @param currentId 当前新闻ID
     * @return 下一条新闻
     */
    PolicyNewsVo selectNextNews(@Param("newsType") String newsType, @Param("currentId") Long currentId);
}
