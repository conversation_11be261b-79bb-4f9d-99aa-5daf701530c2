{"doc": " 公益活动Service接口\n\n <AUTHOR>\n @date 2024-11-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSociallyUsefulActivityById", "paramTypes": ["java.lang.Integer"], "doc": " 查询公益活动\n\n @param id 公益活动主键\n @return 公益活动\n"}, {"name": "selectNekndSociallyUsefulActivityList", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": " 查询公益活动列表\n\n @param nekndSociallyUsefulActivity 公益活动\n @return 公益活动集合\n"}, {"name": "insertNekndSociallyUsefulActivity", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": " 新增公益活动\n\n @param nekndSociallyUsefulActivity 公益活动\n @return 结果\n"}, {"name": "updateNekndSociallyUsefulActivity", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": " 修改公益活动\n\n @param nekndSociallyUsefulActivity 公益活动\n @return 结果\n"}, {"name": "deleteNekndSociallyUsefulActivityByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除公益活动\n\n @param ids 需要删除的公益活动主键集合\n @return 结果\n"}, {"name": "deleteNekndSociallyUsefulActivityById", "paramTypes": ["java.lang.Integer"], "doc": " 删除公益活动信息\n\n @param id 公益活动主键\n @return 结果\n"}], "constructors": []}