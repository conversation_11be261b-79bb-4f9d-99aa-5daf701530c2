{"doc": " 公司和学校的员工申请记录Mapper接口\n\n <AUTHOR>\n @date 2024-09-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询公司和学校的员工申请记录\n\n @param id 公司和学校的员工申请记录主键\n @return 公司和学校的员工申请记录\n"}, {"name": "selectNekndEmployeeApplicationList", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 查询公司和学校的员工申请记录列表\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 公司和学校的员工申请记录集合\n"}, {"name": "insertNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 新增公司和学校的员工申请记录\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 结果\n"}, {"name": "updateNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 修改公司和学校的员工申请记录\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 结果\n"}, {"name": "deleteNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除公司和学校的员工申请记录\n\n @param id 公司和学校的员工申请记录主键\n @return 结果\n"}, {"name": "deleteNekndEmployeeApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除公司和学校的员工申请记录\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}, {"name": "selectNekndEmployeeApplicationByIdCount", "paramTypes": ["java.lang.Long"], "doc": " 查询对应id记录数\n\n @return 【记录数】\n"}, {"name": "updateEmployeeApplicationReviewStatus", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 更新员工申请审核状态\n\n @param deptId 部门ID\n @param userId 用户ID\n @param reviewStatus 审核状态\n @param status 申请类型\n @return 结果\n"}], "constructors": []}