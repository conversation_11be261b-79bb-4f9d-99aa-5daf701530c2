{"doc": " Excel相关处理\n\n <AUTHOR>\n", "fields": [{"name": "sysDictMap", "doc": " 用于dictType属性数据存储，避免重复查缓存\n"}, {"name": "sheetSize", "doc": " Excel sheet最大行数，默认65536\n"}, {"name": "sheetName", "doc": " 工作表名称\n"}, {"name": "type", "doc": " 导出类型（EXPORT:导出数据；IMPORT：导入模板）\n"}, {"name": "wb", "doc": " 工作薄对象\n"}, {"name": "sheet", "doc": " 工作表对象\n"}, {"name": "styles", "doc": " 样式列表\n"}, {"name": "list", "doc": " 导入导出数据列表\n"}, {"name": "fields", "doc": " 注解列表\n"}, {"name": "rownum", "doc": " 当前行号\n"}, {"name": "title", "doc": " 标题\n"}, {"name": "maxHeight", "doc": " 最大高度\n"}, {"name": "subMergedLastRowNum", "doc": " 合并后最后行数\n"}, {"name": "subMergedFirstRowNum", "doc": " 合并后开始行数\n"}, {"name": "subMethod", "doc": " 对象的子列表方法\n"}, {"name": "subFields", "doc": " 对象的子列表属性\n"}, {"name": "statistics", "doc": " 统计列表\n"}, {"name": "DOUBLE_FORMAT", "doc": " 数字格式\n"}, {"name": "clazz", "doc": " 实体对象\n"}, {"name": "excludeFields", "doc": " 需要排除列属性\n"}], "enumConstants": [], "methods": [{"name": "hideColumn", "paramTypes": ["java.lang.String[]"], "doc": " 隐藏Excel中列属性\n\n @param fields 列属性名 示例[单个\"name\"/多个\"id\",\"name\"]\n @throws Exception\n"}, {"name": "createTitle", "paramTypes": [], "doc": " 创建excel第一行标题\n"}, {"name": "createSubHead", "paramTypes": [], "doc": " 创建对象的子列表名称\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream"], "doc": " 对excel表单默认第一个索引名转换成list\n\n @param is 输入流\n @return 转换后集合\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream", "int"], "doc": " 对excel表单默认第一个索引名转换成list\n\n @param is 输入流\n @param titleNum 标题占用行数\n @return 转换后集合\n"}, {"name": "importExcelIndustry", "paramTypes": ["java.lang.String", "java.io.InputStream", "int"], "doc": " 对excel表单指定表格索引名转换成list（专业专区使用）\n\n @param sheetName 表格索引名\n @param titleNum 标题占用行数\n @param is 输入流\n @return 转换后集合\n"}, {"name": "importExcel", "paramTypes": ["java.lang.String", "java.io.InputStream", "int"], "doc": " 对excel表单指定表格索引名转换成list\n\n @param sheetName 表格索引名\n @param titleNum 标题占用行数\n @param is 输入流\n @return 转换后集合\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param list 导出数据集合\n @param sheetName 工作表的名称\n @return 结果\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param list 导出数据集合\n @param sheetName 工作表的名称\n @param title 标题\n @return 结果\n"}, {"name": "exportExcel", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.util.List", "java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param response 返回数据\n @param list 导出数据集合\n @param sheetName 工作表的名称\n @return 结果\n"}, {"name": "exportExcel", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.util.List", "java.lang.String", "java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param response 返回数据\n @param list 导出数据集合\n @param sheetName 工作表的名称\n @param title 标题\n @return 结果\n"}, {"name": "importTemplateExcel", "paramTypes": ["java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param sheetName 工作表的名称\n @return 结果\n"}, {"name": "importTemplateExcel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param sheetName 工作表的名称\n @param title 标题\n @return 结果\n"}, {"name": "importTemplateExcel", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param sheetName 工作表的名称\n @return 结果\n"}, {"name": "importTemplateExcel", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String", "java.lang.String"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @param sheetName 工作表的名称\n @param title 标题\n @return 结果\n"}, {"name": "exportExcel", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @return 结果\n"}, {"name": "exportExcel", "paramTypes": [], "doc": " 对list数据源将其里面的数据导入到excel表单\n\n @return 结果\n"}, {"name": "writeSheet", "paramTypes": [], "doc": " 创建写入数据到Sheet\n"}, {"name": "fillExcelData", "paramTypes": ["int", "org.apache.poi.ss.usermodel.Row"], "doc": " 填充excel数据\n\n @param index 序号\n @param row 单元格行\n"}, {"name": "createStyles", "paramTypes": ["org.apache.poi.ss.usermodel.Workbook"], "doc": " 创建表格样式\n\n @param wb 工作薄对象\n @return 样式列表\n"}, {"name": "annotationHeaderStyles", "paramTypes": ["org.apache.poi.ss.usermodel.Workbook", "java.util.Map"], "doc": " 根据Excel注解创建表格头样式\n\n @param wb 工作薄对象\n @return 自定义样式列表\n"}, {"name": "annotationDataStyles", "paramTypes": ["org.apache.poi.ss.usermodel.Workbook"], "doc": " 根据Excel注解创建表格列样式\n\n @param wb 工作薄对象\n @return 自定义样式列表\n"}, {"name": "annotationDataStyles", "paramTypes": ["java.util.Map", "java.lang.reflect.Field", "org.dromara.business.annotation.Excel"], "doc": " 根据Excel注解创建表格列样式\n\n @param styles 自定义样式列表\n @param field  属性列信息\n @param excel  注解信息\n"}, {"name": "createHeadCell", "paramTypes": ["org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Row", "int"], "doc": " 创建单元格\n"}, {"name": "setCellVo", "paramTypes": ["java.lang.Object", "org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Cell"], "doc": " 设置单元格信息\n\n @param value 单元格值\n @param attr 注解相关\n @param cell 单元格信息\n"}, {"name": "getDrawingPatriarch", "paramTypes": ["org.apache.poi.ss.usermodel.Sheet"], "doc": " 获取画布\n"}, {"name": "getImageType", "paramTypes": ["byte[]"], "doc": " 获取图片类型,设置图片插入类型\n"}, {"name": "setDataValidation", "paramTypes": ["org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Row", "int"], "doc": " 创建表格样式\n"}, {"name": "addCell", "paramTypes": ["org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Row", "java.lang.Object", "java.lang.reflect.Field", "int"], "doc": " 添加单元格\n"}, {"name": "setPromptOrValidation", "paramTypes": ["org.apache.poi.ss.usermodel.Sheet", "java.lang.String[]", "java.lang.String", "int", "int", "int", "int"], "doc": " 设置 POI XSSFSheet 单元格提示或选择框\n\n @param sheet 表单\n @param textlist 下拉框显示的内容\n @param promptContent 提示内容\n @param firstRow 开始行\n @param endRow 结束行\n @param firstCol 开始列\n @param endCol 结束列\n"}, {"name": "setXSSFValidationWithHidden", "paramTypes": ["org.apache.poi.ss.usermodel.Sheet", "java.lang.String[]", "java.lang.String", "int", "int", "int", "int"], "doc": " 设置某些列的值只能输入预制的数据,显示下拉框（兼容超出一定数量的下拉框）.\n\n @param sheet 要设置的sheet.\n @param textlist 下拉框显示的内容\n @param promptContent 提示内容\n @param firstRow 开始行\n @param endRow 结束行\n @param firstCol 开始列\n @param endCol 结束列\n"}, {"name": "convertByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 解析导出值 0=男,1=女,2=未知\n\n @param propertyValue 参数值\n @param converterExp 翻译注解\n @param separator 分隔符\n @return 解析后值\n"}, {"name": "reverseByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 反向解析值 男=0,女=1,未知=2\n\n @param propertyValue 参数值\n @param converterExp 翻译注解\n @param separator 分隔符\n @return 解析后值\n"}, {"name": "convertDictByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 解析字典值\n\n @param dictValue 字典值\n @param dictType 字典类型\n @param separator 分隔符\n @return 字典标签\n"}, {"name": "reverseDictByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 反向解析值字典值\n\n @param dictLabel 字典标签\n @param dictType 字典类型\n @param separator 分隔符\n @return 字典值\n"}, {"name": "dataFormatHandlerAdapter", "paramTypes": ["java.lang.Object", "org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Cell"], "doc": " 数据处理器\n\n @param value 数据值\n @param excel 数据注解\n @return\n"}, {"name": "addStatisticsData", "paramTypes": ["java.lang.Integer", "java.lang.String", "org.dromara.business.annotation.Excel"], "doc": " 合计统计信息\n"}, {"name": "addStatisticsRow", "paramTypes": [], "doc": " 创建统计行\n"}, {"name": "encodingFilename", "paramTypes": ["java.lang.String"], "doc": " 编码文件名\n"}, {"name": "getAbsoluteFile", "paramTypes": ["java.lang.String"], "doc": " 获取下载路径\n\n @param filename 文件名称\n"}, {"name": "getTargetValue", "paramTypes": ["java.lang.Object", "java.lang.reflect.Field", "org.dromara.business.annotation.Excel"], "doc": " 获取bean中的属性值\n\n @param vo 实体对象\n @param field 字段\n @param excel 注解\n @return 最终的属性值\n @throws Exception\n"}, {"name": "getValue", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": " 以类的属性的get方法方法形式获取值\n\n @param o\n @param name\n @return value\n @throws Exception\n"}, {"name": "createExcelField", "paramTypes": [], "doc": " 得到所有定义字段\n"}, {"name": "getFields", "paramTypes": [], "doc": " 获取字段注解信息\n"}, {"name": "getRowHeight", "paramTypes": [], "doc": " 根据注解获取最大行高\n"}, {"name": "createWorkbook", "paramTypes": [], "doc": " 创建一个工作簿\n"}, {"name": "createSheet", "paramTypes": ["int", "int"], "doc": " 创建工作表\n\n @param sheetNo sheet数量\n @param index 序号\n"}, {"name": "getCellValue", "paramTypes": ["org.apache.poi.ss.usermodel.Row", "int"], "doc": " 获取单元格值\n\n @param row 获取的行\n @param column 获取单元格列号\n @return 单元格值\n"}, {"name": "isRowEmpty", "paramTypes": ["org.apache.poi.ss.usermodel.Row"], "doc": " 判断是否是空行\n\n @param row 判断的行\n @return\n"}, {"name": "getSheetPictures03", "paramTypes": ["org.apache.poi.hssf.usermodel.HSSFSheet", "org.apache.poi.hssf.usermodel.HSSFWorkbook"], "doc": " 获取Excel2003图片\n\n @param sheet 当前sheet对象\n @param workbook 工作簿对象\n @return Map key:图片单元格索引（1_1）String，value:图片流PictureData\n"}, {"name": "getSheetPictures07", "paramTypes": ["org.apache.poi.xssf.usermodel.XSSFSheet", "org.apache.poi.xssf.usermodel.XSSFWorkbook"], "doc": " 获取Excel2007图片\n\n @param sheet 当前sheet对象\n @param workbook 工作簿对象\n @return Map key:图片单元格索引（1_1）String，value:图片流PictureData\n"}, {"name": "parseDateToStr", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 格式化不同类型的日期对象\n\n @param dateFormat 日期格式\n @param val 被格式化的日期对象\n @return 格式化后的日期字符\n"}, {"name": "isSubList", "paramTypes": [], "doc": " 是否有对象的子列表\n"}, {"name": "isSubListValue", "paramTypes": ["java.lang.Object"], "doc": " 是否有对象的子列表，集合不为空\n"}, {"name": "getListCellValue", "paramTypes": ["java.lang.Object"], "doc": " 获取集合的值\n"}, {"name": "getSubMethod", "paramTypes": ["java.lang.String", "java.lang.Class"], "doc": " 获取对象的子列表方法\n\n @param name 名称\n @param pojoClass 类对象\n @return 子列表方法\n"}], "constructors": []}