{"doc": " 企业数字化应用Service业务层处理\n\n <AUTHOR>\n @date 2024-05-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSupplyApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询企业数字化应用\n\n @param id 企业数字化应用主键\n @return 企业数字化应用\n"}, {"name": "selectNekndSupplyApplicationList", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": " 查询企业数字化应用列表\n\n @param nekndSupplyApplication 企业数字化应用\n @return 企业数字化应用\n"}, {"name": "insertNekndSupplyApplication", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": " 新增企业数字化应用\n\n @param nekndSupplyApplication 企业数字化应用\n @return 结果\n"}, {"name": "updateNekndSupplyApplication", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": " 修改企业数字化应用\n\n @param nekndSupplyApplication 企业数字化应用\n @return 结果\n"}, {"name": "deleteNekndSupplyApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除企业数字化应用\n\n @param ids 需要删除的企业数字化应用主键\n @return 结果\n"}, {"name": "deleteNekndSupplyApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除企业数字化应用信息\n\n @param id 企业数字化应用主键\n @return 结果\n"}], "constructors": []}