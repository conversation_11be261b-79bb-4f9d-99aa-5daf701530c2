{"doc": " 【请填写功能名称】Mapper接口\n\n <AUTHOR>\n @date 2024-12-25\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryProfessionalGroupsAccountByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustryProfessionalGroupsAccountByIndustryIdCount", "paramTypes": ["java.lang.Long"], "doc": " 查询对应id记录数\n\n @param industryId 【请填写功能名称】主键\n @return 【记录数】\n"}, {"name": "selectNekndIndustryProfessionalGroupsAccountList", "paramTypes": ["org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\n @return 【请填写功能名称】集合\n"}, {"name": "insertNekndIndustryProfessionalGroupsAccount", "paramTypes": ["org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustryProfessionalGroupsAccount", "paramTypes": ["org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustryProfessionalGroupsAccountByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 结果\n"}, {"name": "deleteNekndIndustryProfessionalGroupsAccountByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industryIds 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}