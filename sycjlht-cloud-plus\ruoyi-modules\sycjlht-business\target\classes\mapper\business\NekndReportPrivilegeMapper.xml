<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndReportPrivilegeMapper">
    
    <resultMap type="NekndReportPrivilege" id="NekndReportPrivilegeResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="status"    column="status"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="phoneNumber"    column="phonenumber"    />
        <result property="email"    column="email"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNekndReportPrivilegeVo">
        select a.id, a.user_id,b.user_name,b.nick_name, b.phonenumber,b.email,a.create_time,a.update_time,a.status from neknd_report_privilege a left join sys_user b on a.user_id= b.user_id
    </sql>

    <select id="selectNekndReportPrivilegeList" parameterType="NekndReportPrivilege" resultMap="NekndReportPrivilegeResult">
        <include refid="selectNekndReportPrivilegeVo"/>
        <where>  
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="userId != null  and userId != ''"> and a.user_id = #{userId}</if>
        </where>
        order by a.create_time desc
    </select>
    
    <select id="selectNekndReportPrivilegeById" parameterType="Integer" resultMap="NekndReportPrivilegeResult">
        <include refid="selectNekndReportPrivilegeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndReportPrivilege" parameterType="NekndReportPrivilege" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_report_privilege
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateNekndReportPrivilege" parameterType="NekndReportPrivilege">
        update neknd_report_privilege
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndReportPrivilegeById" parameterType="Integer">
        delete from neknd_report_privilege where id = #{id}
    </delete>

    <delete id="deleteNekndReportPrivilegeByIds" parameterType="String">
        delete from neknd_report_privilege where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>