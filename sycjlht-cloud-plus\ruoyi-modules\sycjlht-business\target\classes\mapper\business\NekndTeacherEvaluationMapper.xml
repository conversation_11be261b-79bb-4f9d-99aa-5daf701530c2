<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndTeacherEvaluationMapper">
    
    <resultMap type="NekndTeacherEvaluation" id="NekndTeacherEvaluationResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="content"    column="content"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNekndTeacherEvaluationVo">
        select id, user_id, teacher_id, content, review_status, create_time from neknd_teacher_evaluation
    </sql>

    <select id="selectNekndTeacherEvaluationList" parameterType="NekndTeacherEvaluation" resultMap="NekndTeacherEvaluationResult">
        <include refid="selectNekndTeacherEvaluationVo"/>
        <where>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="teacherId != null  and teacherId != ''"> and teacher_id = #{teacherId}</if>
            <if test="reviewStatus != null  and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
        </where>
    </select>
    
    <select id="selectNekndTeacherEvaluationById" parameterType="Integer" resultMap="NekndTeacherEvaluationResult">
        <include refid="selectNekndTeacherEvaluationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndTeacherEvaluation" parameterType="NekndTeacherEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_teacher_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="content != null">content,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="content != null">#{content},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateNekndTeacherEvaluation" parameterType="NekndTeacherEvaluation">
        update neknd_teacher_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="upAuditNews" >
        update neknd_teacher_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="reviewStatus != null  ">review_status = #{reviewStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndTeacherEvaluationById" parameterType="Integer">
        delete from neknd_teacher_evaluation where id = #{id}
    </delete>

    <delete id="deleteNekndTeacherEvaluationByIds" parameterType="String">
        delete from neknd_teacher_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>