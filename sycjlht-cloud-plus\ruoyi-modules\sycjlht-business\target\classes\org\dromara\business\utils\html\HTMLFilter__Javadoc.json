{"doc": " HTML过滤器，用于去除XSS漏洞隐患。\n\n <AUTHOR>\n", "fields": [{"name": "REGEX_FLAGS_SI", "doc": " regex flag union representing /si modifiers in php\n"}, {"name": "vAllowed", "doc": " set of allowed html elements, along with allowed attributes for each element\n"}, {"name": "vTagCounts", "doc": " counts of open tags for each (allowable) html element\n"}, {"name": "vSelfClosingTags", "doc": " html elements which must always be self-closing (e.g. \"<img />\")\n"}, {"name": "vNeedClosingTags", "doc": " html elements which must always have separate opening and closing tags (e.g. \"<b></b>\")\n"}, {"name": "vDisallowed", "doc": " set of disallowed html elements\n"}, {"name": "vProtocolAtts", "doc": " attributes which should be checked for valid protocols\n"}, {"name": "vAllowedProtocols", "doc": " allowed protocols\n"}, {"name": "vRemoveBlanks", "doc": " tags which should be removed if they contain no content (e.g. \"<b></b>\" or \"<b />\")\n"}, {"name": "vAllowedEntities", "doc": " entities allowed within html markup\n"}, {"name": "stripComment", "doc": " flag determining whether comments are allowed in input String.\n"}, {"name": "alwaysMakeTags", "doc": " flag determining whether to try to make tags when presented with \"unbalanced\" angle brackets (e.g. \"<b text </b>\"\n becomes \"<b> text </b>\"). If set to false, unbalanced angle brackets will be html escaped.\n"}], "enumConstants": [], "methods": [{"name": "filter", "paramTypes": ["java.lang.String"], "doc": " given a user submitted input String, filter out any invalid or restricted html.\n\n @param input text (i.e. submitted by a user) than may contain html\n @return \"clean\" version of input, with only valid, whitelisted html elements allowed\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Default constructor.\n"}, {"name": "<init>", "paramTypes": ["java.util.Map"], "doc": " Map-parameter configurable constructor.\n\n @param conf map containing configuration. keys match field names.\n"}]}