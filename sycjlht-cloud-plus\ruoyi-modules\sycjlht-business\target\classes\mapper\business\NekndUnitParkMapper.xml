<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndUnitParkMapper">
    
    <resultMap type="NekndUnitPark" id="NekndUnitParkResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="parkName"    column="park_name"    />
        <result property="address"    column="address"    />
        <result property="region"    column="region"    />
        <result property="status"    column="status"    />
        <result property="parkType"    column="park_type"    />
        <result property="parkIndustrialOutput"    column="park_industrial_output"    />
        <result property="leadingIndustry"    column="leading_industry"    />
        <result property="staffEducationRatio"    column="staff_education_ratio"    />
        <result property="skillTrainingRatio"    column="skill_training_ratio"    />
        <result property="schoolUndertakeRatio"    column="school_undertake_ratio"    />
        <result property="compatibilityRatio"    column="compatibility_ratio"    />
        <result property="parkWorkAssessmentNumber"    column="park_work_assessment_number"    />
        <result property="vocationalEducationAssessmentNumber"    column="vocational_education_assessment_number"    />
        <result property="bylaw"    column="bylaw"    />
        <result property="deliberationRules"    column="deliberation_rules"    />
        <result property="responsibilitiesDivisionDocument"    column="responsibilities_division_document"    />
        <result property="amountInPlace"    column="amount_in_place"    />
        <result property="specialFundPlaning"    column="special_fund_planing"    />
        <result property="dailyExpensePlaning"    column="daily_expense_planing"    />
        <result property="localEmploymentRate"    column="local_employment_rate"    />
        <result property="exemptedEnterprisesCount"    column="exempted_enterprises_count"    />
        <result property="landConcessionsCompaniesNumber"    column="land_concessions_companies_number"    />
        <result property="employmentMatchingRate"    column="employment_matching_rate"    />
        <result property="isAdjustmentEstablish"    column="is_adjustment_establish"    />
        <result property="specialty"    column="specialty"    />
        <result property="specialGovernmentFunds"    column="special_government_funds"    />
        <result property="utilizationFund"    column="utilization_fund"    />
        <result property="vocationalToHigherRate"    column="vocational_to_higher_rate"    />
        <result property="higherToUndergraduateRate"    column="higher_to_undergraduate_rate"    />
        <result property="dominantIndustryCertCount"    column="dominant_industry_cert_count"    />
        <result property="dominantIndustryMatchRate"    column="dominant_industry_match_rate"    />
        <result property="newMajorsRequired"    column="new_majors_required"    />
        <result property="obsoleteMajorsRequired"    column="obsolete_majors_required"    />
        <result property="financialPolicyRate"    column="financial_policy_rate"    />
        <result property="fiscalPolicyRate"    column="fiscal_policy_rate"    />
        <result property="landPolicyRate"    column="land_policy_rate"    />
        <result property="enterpriseTaxBenefit"    column="enterprise_tax_benefit"    />
        <result property="equipmentDonation"    column="equipment_donation"    />
        <result property="highSkillTalentCount"    column="high_skill_talent_count"    />
        <result property="qualifiedOrderCount"    column="qualified_order_count"    />
        <result property="parkOutputRatio"    column="park_output_ratio"    />
        <result property="conversionFunds"    column="conversion_funds"    />
        <result property="schoolEnterpriseDepth"    column="school_enterprise_depth"    />
        <result property="employmentQuality"    column="employment_quality"    />
        <result property="industryEducationScore"    column="industry_education_score"    />
        <result property="governmentCoordination"    column="government_coordination"    />
        <result property="industryMatch"    column="industry_match"    />
        <result property="talentTrainingQuality"    column="talent_training_quality"    />
    </resultMap>

    <sql id="selectNekndUnitParkVo">
        select id, uid, park_name, address, region, status, park_type, park_industrial_output, leading_industry, staff_education_ratio, skill_training_ratio, school_undertake_ratio, compatibility_ratio, park_work_assessment_number, vocational_education_assessment_number, bylaw, deliberation_rules, responsibilities_division_document, amount_in_place, special_fund_planing, daily_expense_planing, local_employment_rate, exempted_enterprises_count, land_concessions_companies_number, employment_matching_rate, is_adjustment_establish, specialty, special_government_funds, utilization_fund, vocational_to_higher_rate, higher_to_undergraduate_rate, dominant_industry_cert_count, dominant_industry_match_rate, new_majors_required, obsolete_majors_required, financial_policy_rate, fiscal_policy_rate, land_policy_rate, enterprise_tax_benefit, equipment_donation, high_skill_talent_count, qualified_order_count, park_output_ratio, conversion_funds, school_enterprise_depth, employment_quality, industry_education_score, government_coordination, industry_match, talent_training_quality from neknd_unit_park
    </sql>

    <select id="selectNekndUnitParkList" parameterType="NekndUnitPark" resultMap="NekndUnitParkResult">
        <include refid="selectNekndUnitParkVo"/>
        <where>  
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="parkName != null and parkName != ''"> and park_name like concat('%', #{parkName}, '%')</if>
            <if test="address != null and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="region != null and region != ''"> and region like concat('%', #{region}, '%')</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="parkType != null  and parkType != ''"> and park_type = #{parkType}</if>
            <if test="parkIndustrialOutput != null  and parkIndustrialOutput != ''"> and park_industrial_output = #{parkIndustrialOutput}</if>
            <if test="leadingIndustry != null  and leadingIndustry != ''"> and leading_industry = #{leadingIndustry}</if>
            <if test="staffEducationRatio != null "> and staff_education_ratio = #{staffEducationRatio}</if>
            <if test="skillTrainingRatio != null "> and skill_training_ratio = #{skillTrainingRatio}</if>
            <if test="schoolUndertakeRatio != null "> and school_undertake_ratio = #{schoolUndertakeRatio}</if>
            <if test="compatibilityRatio != null "> and compatibility_ratio = #{compatibilityRatio}</if>
            <if test="parkWorkAssessmentNumber != null  and parkWorkAssessmentNumber != ''"> and park_work_assessment_number = #{parkWorkAssessmentNumber}</if>
            <if test="vocationalEducationAssessmentNumber != null  and vocationalEducationAssessmentNumber != ''"> and vocational_education_assessment_number = #{vocationalEducationAssessmentNumber}</if>
            <if test="bylaw != null  and bylaw != ''"> and bylaw = #{bylaw}</if>
            <if test="deliberationRules != null  and deliberationRules != ''"> and deliberation_rules = #{deliberationRules}</if>
            <if test="responsibilitiesDivisionDocument != null  and responsibilitiesDivisionDocument != ''"> and responsibilities_division_document = #{responsibilitiesDivisionDocument}</if>
            <if test="amountInPlace != null  and amountInPlace != ''"> and amount_in_place = #{amountInPlace}</if>
            <if test="specialFundPlaning != null  and specialFundPlaning != ''"> and special_fund_planing = #{specialFundPlaning}</if>
            <if test="dailyExpensePlaning != null  and dailyExpensePlaning != ''"> and daily_expense_planing = #{dailyExpensePlaning}</if>
            <if test="localEmploymentRate != null "> and local_employment_rate = #{localEmploymentRate}</if>
            <if test="exemptedEnterprisesCount != null  and exemptedEnterprisesCount != ''"> and exempted_enterprises_count = #{exemptedEnterprisesCount}</if>
            <if test="landConcessionsCompaniesNumber != null  and landConcessionsCompaniesNumber != ''"> and land_concessions_companies_number = #{landConcessionsCompaniesNumber}</if>
            <if test="employmentMatchingRate != null "> and employment_matching_rate = #{employmentMatchingRate}</if>
            <if test="isAdjustmentEstablish != null  and isAdjustmentEstablish != ''"> and is_adjustment_establish = #{isAdjustmentEstablish}</if>
            <if test="specialty != null  and specialty != ''"> and specialty = #{specialty}</if>
            <if test="specialGovernmentFunds != null  and specialGovernmentFunds != ''"> and special_government_funds = #{specialGovernmentFunds}</if>
            <if test="utilizationFund != null "> and utilization_fund = #{utilizationFund}</if>
            <if test="vocationalToHigherRate != null "> and vocational_to_higher_rate = #{vocationalToHigherRate}</if>
            <if test="higherToUndergraduateRate != null "> and higher_to_undergraduate_rate = #{higherToUndergraduateRate}</if>
            <if test="dominantIndustryCertCount != null  and dominantIndustryCertCount != ''"> and dominant_industry_cert_count = #{dominantIndustryCertCount}</if>
            <if test="dominantIndustryMatchRate != null  and dominantIndustryMatchRate != ''"> and dominant_industry_match_rate = #{dominantIndustryMatchRate}</if>
            <if test="newMajorsRequired != null  and newMajorsRequired != ''"> and new_majors_required = #{newMajorsRequired}</if>
            <if test="obsoleteMajorsRequired != null  and obsoleteMajorsRequired != ''"> and obsolete_majors_required = #{obsoleteMajorsRequired}</if>
            <if test="financialPolicyRate != null "> and financial_policy_rate = #{financialPolicyRate}</if>
            <if test="fiscalPolicyRate != null "> and fiscal_policy_rate = #{fiscalPolicyRate}</if>
            <if test="landPolicyRate != null "> and land_policy_rate = #{landPolicyRate}</if>
            <if test="enterpriseTaxBenefit != null "> and enterprise_tax_benefit = #{enterpriseTaxBenefit}</if>
            <if test="equipmentDonation != null  and equipmentDonation != ''"> and equipment_donation = #{equipmentDonation}</if>
            <if test="highSkillTalentCount != null  and highSkillTalentCount != ''"> and high_skill_talent_count = #{highSkillTalentCount}</if>
            <if test="qualifiedOrderCount != null  and qualifiedOrderCount != ''"> and qualified_order_count = #{qualifiedOrderCount}</if>
            <if test="parkOutputRatio != null  and parkOutputRatio != ''"> and park_output_ratio = #{parkOutputRatio}</if>
            <if test="conversionFunds != null  and conversionFunds != ''"> and conversion_funds = #{conversionFunds}</if>
            <if test="schoolEnterpriseDepth != null  and schoolEnterpriseDepth != ''"> and school_enterprise_depth = #{schoolEnterpriseDepth}</if>
            <if test="employmentQuality != null  and employmentQuality != ''"> and employment_quality = #{employmentQuality}</if>
            <if test="industryEducationScore != null  and industryEducationScore != ''"> and industry_education_score = #{industryEducationScore}</if>
            <if test="governmentCoordination != null  and governmentCoordination != ''"> and government_coordination = #{governmentCoordination}</if>
            <if test="industryMatch != null "> and industry_match = #{industryMatch}</if>
            <if test="talentTrainingQuality != null  and talentTrainingQuality != ''"> and talent_training_quality = #{talentTrainingQuality}</if>
        </where>
    </select>
    
    <select id="selectNekndUnitParkById" parameterType="Long" resultMap="NekndUnitParkResult">
        <include refid="selectNekndUnitParkVo"/>
        where id = #{id}
    </select>

    <select id="selectNekndUnitParkByUid" parameterType="Long" resultMap="NekndUnitParkResult">
        <include refid="selectNekndUnitParkVo"/>
        where uid = #{uid}
    </select>

    <insert id="insertNekndUnitPark" parameterType="NekndUnitPark" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_unit_park
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">uid,</if>
            <if test="parkName != null">park_name,</if>
            <if test="address != null">address,</if>
            <if test="region != null">region,</if>
            <if test="status != null">status,</if>
            <if test="parkType != null">park_type,</if>
            <if test="parkIndustrialOutput != null">park_industrial_output,</if>
            <if test="leadingIndustry != null">leading_industry,</if>
            <if test="staffEducationRatio != null">staff_education_ratio,</if>
            <if test="skillTrainingRatio != null">skill_training_ratio,</if>
            <if test="schoolUndertakeRatio != null">school_undertake_ratio,</if>
            <if test="compatibilityRatio != null">compatibility_ratio,</if>
            <if test="parkWorkAssessmentNumber != null">park_work_assessment_number,</if>
            <if test="vocationalEducationAssessmentNumber != null">vocational_education_assessment_number,</if>
            <if test="bylaw != null">bylaw,</if>
            <if test="deliberationRules != null">deliberation_rules,</if>
            <if test="responsibilitiesDivisionDocument != null">responsibilities_division_document,</if>
            <if test="amountInPlace != null">amount_in_place,</if>
            <if test="specialFundPlaning != null">special_fund_planing,</if>
            <if test="dailyExpensePlaning != null">daily_expense_planing,</if>
            <if test="localEmploymentRate != null">local_employment_rate,</if>
            <if test="exemptedEnterprisesCount != null">exempted_enterprises_count,</if>
            <if test="landConcessionsCompaniesNumber != null">land_concessions_companies_number,</if>
            <if test="employmentMatchingRate != null">employment_matching_rate,</if>
            <if test="isAdjustmentEstablish != null">is_adjustment_establish,</if>
            <if test="specialty != null">specialty,</if>
            <if test="specialGovernmentFunds != null">special_government_funds,</if>
            <if test="utilizationFund != null">utilization_fund,</if>
            <if test="vocationalToHigherRate != null">vocational_to_higher_rate,</if>
            <if test="higherToUndergraduateRate != null">higher_to_undergraduate_rate,</if>
            <if test="dominantIndustryCertCount != null">dominant_industry_cert_count,</if>
            <if test="dominantIndustryMatchRate != null">dominant_industry_match_rate,</if>
            <if test="newMajorsRequired != null">new_majors_required,</if>
            <if test="obsoleteMajorsRequired != null">obsolete_majors_required,</if>
            <if test="financialPolicyRate != null">financial_policy_rate,</if>
            <if test="fiscalPolicyRate != null">fiscal_policy_rate,</if>
            <if test="landPolicyRate != null">land_policy_rate,</if>
            <if test="enterpriseTaxBenefit != null">enterprise_tax_benefit,</if>
            <if test="equipmentDonation != null">equipment_donation,</if>
            <if test="highSkillTalentCount != null">high_skill_talent_count,</if>
            <if test="qualifiedOrderCount != null">qualified_order_count,</if>
            <if test="parkOutputRatio != null">park_output_ratio,</if>
            <if test="conversionFunds != null">conversion_funds,</if>
            <if test="schoolEnterpriseDepth != null">school_enterprise_depth,</if>
            <if test="employmentQuality != null">employment_quality,</if>
            <if test="industryEducationScore != null">industry_education_score,</if>
            <if test="governmentCoordination != null">government_coordination,</if>
            <if test="industryMatch != null">industry_match,</if>
            <if test="talentTrainingQuality != null">talent_training_quality,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">#{uid},</if>
            <if test="parkName != null">#{parkName},</if>
            <if test="address != null">#{address},</if>
            <if test="region != null">#{region},</if>
            <if test="status != null">#{status},</if>
            <if test="parkType != null">#{parkType},</if>
            <if test="parkIndustrialOutput != null">#{parkIndustrialOutput},</if>
            <if test="leadingIndustry != null">#{leadingIndustry},</if>
            <if test="staffEducationRatio != null">#{staffEducationRatio},</if>
            <if test="skillTrainingRatio != null">#{skillTrainingRatio},</if>
            <if test="schoolUndertakeRatio != null">#{schoolUndertakeRatio},</if>
            <if test="compatibilityRatio != null">#{compatibilityRatio},</if>
            <if test="parkWorkAssessmentNumber != null">#{parkWorkAssessmentNumber},</if>
            <if test="vocationalEducationAssessmentNumber != null">#{vocationalEducationAssessmentNumber},</if>
            <if test="bylaw != null">#{bylaw},</if>
            <if test="deliberationRules != null">#{deliberationRules},</if>
            <if test="responsibilitiesDivisionDocument != null">#{responsibilitiesDivisionDocument},</if>
            <if test="amountInPlace != null">#{amountInPlace},</if>
            <if test="specialFundPlaning != null">#{specialFundPlaning},</if>
            <if test="dailyExpensePlaning != null">#{dailyExpensePlaning},</if>
            <if test="localEmploymentRate != null">#{localEmploymentRate},</if>
            <if test="exemptedEnterprisesCount != null">#{exemptedEnterprisesCount},</if>
            <if test="landConcessionsCompaniesNumber != null">#{landConcessionsCompaniesNumber},</if>
            <if test="employmentMatchingRate != null">#{employmentMatchingRate},</if>
            <if test="isAdjustmentEstablish != null">#{isAdjustmentEstablish},</if>
            <if test="specialty != null">#{specialty},</if>
            <if test="specialGovernmentFunds != null">#{specialGovernmentFunds},</if>
            <if test="utilizationFund != null">#{utilizationFund},</if>
            <if test="vocationalToHigherRate != null">#{vocationalToHigherRate},</if>
            <if test="higherToUndergraduateRate != null">#{higherToUndergraduateRate},</if>
            <if test="dominantIndustryCertCount != null">#{dominantIndustryCertCount},</if>
            <if test="dominantIndustryMatchRate != null">#{dominantIndustryMatchRate},</if>
            <if test="newMajorsRequired != null">#{newMajorsRequired},</if>
            <if test="obsoleteMajorsRequired != null">#{obsoleteMajorsRequired},</if>
            <if test="financialPolicyRate != null">#{financialPolicyRate},</if>
            <if test="fiscalPolicyRate != null">#{fiscalPolicyRate},</if>
            <if test="landPolicyRate != null">#{landPolicyRate},</if>
            <if test="enterpriseTaxBenefit != null">#{enterpriseTaxBenefit},</if>
            <if test="equipmentDonation != null">#{equipmentDonation},</if>
            <if test="highSkillTalentCount != null">#{highSkillTalentCount},</if>
            <if test="qualifiedOrderCount != null">#{qualifiedOrderCount},</if>
            <if test="parkOutputRatio != null">#{parkOutputRatio},</if>
            <if test="conversionFunds != null">#{conversionFunds},</if>
            <if test="schoolEnterpriseDepth != null">#{schoolEnterpriseDepth},</if>
            <if test="employmentQuality != null">#{employmentQuality},</if>
            <if test="industryEducationScore != null">#{industryEducationScore},</if>
            <if test="governmentCoordination != null">#{governmentCoordination},</if>
            <if test="industryMatch != null">#{industryMatch},</if>
            <if test="talentTrainingQuality != null">#{talentTrainingQuality},</if>
         </trim>
    </insert>

    <update id="updateNekndUnitPark" parameterType="NekndUnitPark">
        update neknd_unit_park
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="parkName != null">park_name = #{parkName},</if>
            <if test="address != null">address = #{address},</if>
            <if test="region != null">region = #{region},</if>
            <if test="status != null">status = #{status},</if>
            <if test="parkType != null">park_type = #{parkType},</if>
            <if test="parkIndustrialOutput != null">park_industrial_output = #{parkIndustrialOutput},</if>
            <if test="leadingIndustry != null">leading_industry = #{leadingIndustry},</if>
            <if test="staffEducationRatio != null">staff_education_ratio = #{staffEducationRatio},</if>
            <if test="skillTrainingRatio != null">skill_training_ratio = #{skillTrainingRatio},</if>
            <if test="schoolUndertakeRatio != null">school_undertake_ratio = #{schoolUndertakeRatio},</if>
            <if test="compatibilityRatio != null">compatibility_ratio = #{compatibilityRatio},</if>
            <if test="parkWorkAssessmentNumber != null">park_work_assessment_number = #{parkWorkAssessmentNumber},</if>
            <if test="vocationalEducationAssessmentNumber != null">vocational_education_assessment_number = #{vocationalEducationAssessmentNumber},</if>
            <if test="bylaw != null">bylaw = #{bylaw},</if>
            <if test="deliberationRules != null">deliberation_rules = #{deliberationRules},</if>
            <if test="responsibilitiesDivisionDocument != null">responsibilities_division_document = #{responsibilitiesDivisionDocument},</if>
            <if test="amountInPlace != null">amount_in_place = #{amountInPlace},</if>
            <if test="specialFundPlaning != null">special_fund_planing = #{specialFundPlaning},</if>
            <if test="dailyExpensePlaning != null">daily_expense_planing = #{dailyExpensePlaning},</if>
            <if test="localEmploymentRate != null">local_employment_rate = #{localEmploymentRate},</if>
            <if test="exemptedEnterprisesCount != null">exempted_enterprises_count = #{exemptedEnterprisesCount},</if>
            <if test="landConcessionsCompaniesNumber != null">land_concessions_companies_number = #{landConcessionsCompaniesNumber},</if>
            <if test="employmentMatchingRate != null">employment_matching_rate = #{employmentMatchingRate},</if>
            <if test="isAdjustmentEstablish != null">is_adjustment_establish = #{isAdjustmentEstablish},</if>
            <if test="specialty != null">specialty = #{specialty},</if>
            <if test="specialGovernmentFunds != null">special_government_funds = #{specialGovernmentFunds},</if>
            <if test="utilizationFund != null">utilization_fund = #{utilizationFund},</if>
            <if test="vocationalToHigherRate != null">vocational_to_higher_rate = #{vocationalToHigherRate},</if>
            <if test="higherToUndergraduateRate != null">higher_to_undergraduate_rate = #{higherToUndergraduateRate},</if>
            <if test="dominantIndustryCertCount != null">dominant_industry_cert_count = #{dominantIndustryCertCount},</if>
            <if test="dominantIndustryMatchRate != null">dominant_industry_match_rate = #{dominantIndustryMatchRate},</if>
            <if test="newMajorsRequired != null">new_majors_required = #{newMajorsRequired},</if>
            <if test="obsoleteMajorsRequired != null">obsolete_majors_required = #{obsoleteMajorsRequired},</if>
            <if test="financialPolicyRate != null">financial_policy_rate = #{financialPolicyRate},</if>
            <if test="fiscalPolicyRate != null">fiscal_policy_rate = #{fiscalPolicyRate},</if>
            <if test="landPolicyRate != null">land_policy_rate = #{landPolicyRate},</if>
            <if test="enterpriseTaxBenefit != null">enterprise_tax_benefit = #{enterpriseTaxBenefit},</if>
            <if test="equipmentDonation != null">equipment_donation = #{equipmentDonation},</if>
            <if test="highSkillTalentCount != null">high_skill_talent_count = #{highSkillTalentCount},</if>
            <if test="qualifiedOrderCount != null">qualified_order_count = #{qualifiedOrderCount},</if>
            <if test="parkOutputRatio != null">park_output_ratio = #{parkOutputRatio},</if>
            <if test="conversionFunds != null">conversion_funds = #{conversionFunds},</if>
            <if test="schoolEnterpriseDepth != null">school_enterprise_depth = #{schoolEnterpriseDepth},</if>
            <if test="employmentQuality != null">employment_quality = #{employmentQuality},</if>
            <if test="industryEducationScore != null">industry_education_score = #{industryEducationScore},</if>
            <if test="governmentCoordination != null">government_coordination = #{governmentCoordination},</if>
            <if test="industryMatch != null">industry_match = #{industryMatch},</if>
            <if test="talentTrainingQuality != null">talent_training_quality = #{talentTrainingQuality},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndUnitParkById" parameterType="Long">
        delete from neknd_unit_park where id = #{id}
    </delete>

    <delete id="deleteNekndUnitParkByIds" parameterType="String">
        delete from neknd_unit_park where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>