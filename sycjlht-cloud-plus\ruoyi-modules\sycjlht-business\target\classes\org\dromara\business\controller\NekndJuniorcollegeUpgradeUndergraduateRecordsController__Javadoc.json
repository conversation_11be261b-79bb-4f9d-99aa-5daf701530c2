{"doc": " 继续教育报名记录Controller\n \n <AUTHOR>\n @date 2024-10-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 后台查询继续教育报名记录列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 门户查询继续教育报名记录列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 导出继续教育报名记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取继续教育报名记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 新增继续教育报名记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduateRecords"], "doc": " 修改继续教育报名记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除继续教育报名记录\n"}], "constructors": []}