{"doc": " 数字人才画像Controller\n \n <AUTHOR>\n @date 2024-11-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询数字人才画像列表\n"}, {"name": "lists", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询数字人才画像列表（带详细信息）\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPortraitTalent"], "doc": " 导出数字人才画像列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取数字人才画像详细信息\n"}, {"name": "getInfoById", "paramTypes": ["java.lang.Integer"], "doc": " 获取数字人才画像详细Info\n"}, {"name": "getArrayById", "paramTypes": ["java.lang.Integer"], "doc": " 获取数字人才画像战力数字的数组\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": " 新增数字人才画像\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": " 修改数字人才画像\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除数字人才画像\n"}], "constructors": []}