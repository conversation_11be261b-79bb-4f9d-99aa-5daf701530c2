{"doc": " 报告管理Mapper接口\n\n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryKnowledgeBaseById", "paramTypes": ["java.lang.Integer"], "doc": " 查询报告管理\n\n @param id 报告管理主键\n @return 报告管理\n"}, {"name": "selectNekndIndustryKnowledgeBaseList", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": " 查询报告管理列表\n\n @param nekndIndustryKnowledgeBase 报告管理\n @return 报告管理集合\n"}, {"name": "insertNekndIndustryKnowledgeBase", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": " 新增报告管理\n\n @param nekndIndustryKnowledgeBase 报告管理\n @return 结果\n"}, {"name": "updateNekndIndustryKnowledgeBase", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": " 修改报告管理\n\n @param nekndIndustryKnowledgeBase 报告管理\n @return 结果\n"}, {"name": "deleteNekndIndustryKnowledgeBaseById", "paramTypes": ["java.lang.Integer"], "doc": " 删除报告管理\n\n @param id 报告管理主键\n @return 结果\n"}, {"name": "deleteNekndIndustryKnowledgeBaseByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除报告管理\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}