<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndTrainMapper">
    
    <resultMap type="NekndTrain" id="NekndTrainResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="cover"    column="cover"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="viewCount"    column="view_count"    />
        <result property="address"    column="address"    />
        <result property="classHour"    column="class_hour"    />
        <result property="signupCount"    column="signup_count"    />
        <result property="state" column="state"/>
        <result property="price" column="price"/>
        <result property="file" column="file"/>
        <result property="sector" column="sector"/>
        <result property="issuingUnit" column="issuing_unit"/>
        <result property="organizer" column="organizer"/>
        <result property="reviewStatus" column="review_status"/>
    </resultMap>

    <sql id="selectNekndTrainVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, dept_id, cover, title, content, status, start_time, end_time, view_count, address, class_hour, signup_count,state,price ,sector,issuing_unit,organizer ,review_status ,file from neknd_train
    </sql>

    <select id="selectNekndTrainList" parameterType="NekndTrain" resultMap="NekndTrainResult">
        <include refid="selectNekndTrainVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="cover != null  and cover != ''"> and cover = #{cover}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%') </if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="viewCount != null "> and view_count = #{viewCount}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="classHour != null  and classHour != ''"> and class_hour = #{classHour}</if>
            <if test="signupCount != null "> and signup_count = #{signupCount}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="file != null "> and file = #{file}</if>
            <if test="sector != null "> and sector = #{sector}</if>
            <if test="issuingUnit != null "> and  issuing_unit= #{issuingUnit}</if>
            <if test="organizer != null "> and  organizer= #{organizer}</if>
            <if test="reviewStatus != null "> and  review_status= #{reviewStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndTrainById" parameterType="Integer" resultMap="NekndTrainResult">
        <include refid="selectNekndTrainVo"/>
        where id = #{id}
    </select>

    <select id="getCertificateList" resultMap="NekndTrainResult">
        select id,class_hour,sector,cover,state,price,issuing_unit,content,title,view_count from neknd_train where del_flag = '0' and status = '0';
    </select>
    <select id="getCertificateInfo" resultMap="NekndTrainResult">
        select id,content,start_time,end_time,view_count,address,class_hour,price from neknd_train where id = #{id} and del_flag = '0' and status = '0';
    </select>
    <select id="selectTrainDetail" resultMap="NekndTrainResult">
        <include refid="selectNekndTrainVo"/>
        <where>
        <if test="id != null "> and id = #{id}</if>
        <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectState" parameterType="Integer" resultType="Integer">
        select state from neknd_train where id = #{id}
    </select>


    <insert id="insertNekndTrain" parameterType="NekndTrain" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_train
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="cover != null">cover,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="address != null">address,</if>
            <if test="classHour != null">class_hour,</if>
            <if test="signupCount != null">signup_count,</if>
            <if test="state != null "> state,</if>
            <if test="price != null "> price,</if>
            <if test="file != null "> file,</if>
            <if test="sector != null "> sector,</if>
            <if test="issuingUnit != null ">issuing_unit,</if>
            <if test="organizer != null ">organizer,</if>
            <if test="reviewStatus != null ">review_status</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="cover != null">#{cover},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="address != null">#{address},</if>
            <if test="classHour != null">#{classHour},</if>
            <if test="signupCount != null">#{signupCount},</if>
            <if test="state != null ">#{state},</if>
            <if test="price != null ">#{price},</if>
            <if test="file != null ">#{file},</if>
            <if test="sector != null ">#{sector},</if>
            <if test="issuingUnit != null ">#{issuingUnit},</if>
            <if test="organizer != null ">#{organizer},</if>
            <if test="reviewStatus != null ">#{reviewStatus}</if>
         </trim>
    </insert>

    <update id="updateNekndTrain" parameterType="NekndTrain">
        update neknd_train
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="cover != null">cover = #{cover},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="address != null">address = #{address},</if>
            <if test="classHour != null">class_hour = #{classHour},</if>
            <if test="signupCount != null">signup_count = #{signupCount},</if>
            <if test="state != null "> state = #{state},</if>
            <if test="price != null ">price = #{price},</if>
            <if test="file != null "> file = #{file},</if>
            <if test="sector != null "> sector = #{sector},</if>
            <if test="issuingUnit != null "> issuing_unit= #{issuingUnit},</if>
            <if test="organizer != null "> organizer= #{organizer},</if>
            <if test="reviewStatus != null "> review_status= #{reviewStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndTrainById" parameterType="Integer">
        delete from neknd_train where id = #{id}
    </delete>

    <delete id="deleteNekndTrainByIds" parameterType="String">
        delete from neknd_train where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


</mapper>