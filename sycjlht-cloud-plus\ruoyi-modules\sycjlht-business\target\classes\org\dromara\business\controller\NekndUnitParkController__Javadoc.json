{"doc": " 成员单位-园区Controller\n \n <AUTHOR>\n @date 2025-05-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndUnitPark", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询成员单位-园区列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndUnitPark"], "doc": " 导出成员单位-园区列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取成员单位-园区详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": " 新增成员单位-园区\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": " 修改成员单位-园区\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除成员单位-园区\n"}, {"name": "getByUnitId", "paramTypes": ["java.lang.Long"], "doc": " 根据单位ID查询园区信息\n"}], "constructors": []}