{"doc": " 招聘岗位Service业务层处理\n\n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": " 查询招聘岗位\n\n @param id 招聘岗位主键\n @return 招聘岗位\n"}, {"name": "selectNekndEmployList", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 查询招聘岗位列表\n\n @param nekndEmploy 招聘岗位\n @return 招聘岗位\n"}, {"name": "selectNekndEmployListReview", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 审核查询招聘岗位列表\n\n @param nekndEmploy 招聘岗位\n @return 招聘岗位\n"}, {"name": "insertNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 新增招聘岗位\n\n @param nekndEmploy 招聘岗位\n @return 结果\n"}, {"name": "updateNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 修改招聘岗位\n\n @param nekndEmploy 招聘岗位\n @return 结果\n"}, {"name": "deleteNekndEmployByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除招聘岗位\n\n @param ids 需要删除的招聘岗位主键\n @return 结果\n"}, {"name": "deleteNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": " 删除招聘岗位信息\n\n @param id 招聘岗位主键\n @return 结果\n"}, {"name": "getAIJobContent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " AI智能填充岗位描述或要求\n\n @param jobName 岗位名称\n @param type 填充类型：1-职位描述，2-任职要求\n @return 生成的内容\n"}, {"name": "getEmployMatch", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 分析岗位与人才匹配度\n\n @param employId 岗位ID\n @param userId 用户ID\n @return 匹配结果\n"}], "constructors": []}