{"doc": " 报告管理Service业务层处理\n\n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndChartsById", "paramTypes": ["java.lang.Integer"], "doc": " 查询报告管理\n\n @param id 报告管理主键\n @return 报告管理\n"}, {"name": "selectNekndChartsList", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": " 查询报告管理列表\n\n @param nekndCharts 报告管理\n @return 报告管理\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": " 新增报告管理\n\n @param nekndCharts 报告管理\n @return 结果\n"}, {"name": "updateNekndCharts", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": " 修改报告管理\n\n @param nekndCharts 报告管理\n @return 结果\n"}, {"name": "deleteNekndChartsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除报告管理\n\n @param ids 需要删除的报告管理主键\n @return 结果\n"}, {"name": "deleteNekndChartsById", "paramTypes": ["java.lang.Integer"], "doc": " 删除报告管理信息\n\n @param id 报告管理主键\n @return 结果\n"}, {"name": "selectNekndChartsPage", "paramTypes": ["org.dromara.business.domain.NekndCharts", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询报告管理列表\n"}, {"name": "getChartsStatistics", "paramTypes": [], "doc": " 获取图表统计信息\n"}, {"name": "getChartData", "paramTypes": ["java.lang.Integer"], "doc": " 获取图表数据\n"}], "constructors": []}