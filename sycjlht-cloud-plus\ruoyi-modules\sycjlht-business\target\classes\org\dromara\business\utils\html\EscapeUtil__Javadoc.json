{"doc": " 转义和反转义工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "escape", "paramTypes": ["java.lang.String"], "doc": " 转义文本中的HTML字符为安全的字符\n\n @param text 被转义的文本\n @return 转义后的文本\n"}, {"name": "unescape", "paramTypes": ["java.lang.String"], "doc": " 还原被转义的HTML特殊字符\n\n @param content 包含转义符的HTML内容\n @return 转换后的字符串\n"}, {"name": "clean", "paramTypes": ["java.lang.String"], "doc": " 清除所有HTML标签，但是不删除标签内的内容\n\n @param content 文本\n @return 清除标签后的文本\n"}, {"name": "encode", "paramTypes": ["java.lang.String"], "doc": " Escape编码\n\n @param text 被编码的文本\n @return 编码后的字符\n"}, {"name": "decode", "paramTypes": ["java.lang.String"], "doc": " Escape解码\n\n @param content 被转义的内容\n @return 解码后的字符串\n"}], "constructors": []}