{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getBasicData", "paramTypes": [], "doc": " 获取基础数据\n @return\n"}, {"name": "parseSpecialtyCoverageMatchRateMap", "paramTypes": ["java.util.List"], "doc": " 获取专业覆盖率匹配率\n @param specialtyCoverageMatchRate\n @return\n"}, {"name": "getProvincialLocalEmploymentRateResult", "paramTypes": ["java.util.List"], "doc": " 获取各省园区本地就业率\n"}, {"name": "getProvincialCountryLevelParkResult", "paramTypes": ["java.util.List", "java.util.List", "java.util.List"], "doc": " 各省国家级数量\n"}, {"name": "getProvincialEnterpriseTypeResult", "paramTypes": ["java.util.List"], "doc": "  各省企业类型数量分布\n"}, {"name": "getProvincialComprehensiveRankingResult", "paramTypes": ["java.util.List"], "doc": " 各省综合排名\n"}, {"name": "getIndustryEducationBenchmarkResult", "paramTypes": ["java.util.List"], "doc": " 专项冠军 - 产教融合标杆\n"}, {"name": "getMaximumParkResult", "paramTypes": ["java.util.List"], "doc": " 专项冠军 - 资源投入最大园区\n"}, {"name": "getFinancialSupportResult", "paramTypes": ["java.util.List"], "doc": " 专项冠军 - 财政扶持\n"}, {"name": "getFeatureLeadingParkResult", "paramTypes": ["java.util.List"], "doc": " 专项冠军 - 特色产业园区\n"}, {"name": "getKeyLeadingEnterpriseAndSchoolResult", "paramTypes": ["java.util.List"], "doc": " 专项冠军 - 重点牵头企业和学校\n"}, {"name": "getMaxCountryLevelProvincial", "paramTypes": ["java.util.List"], "doc": " 专项冠军 - 国家级\n"}, {"name": "getSchoolEnterpriseCooperationResult", "paramTypes": ["java.util.List"], "doc": " 专项冠军 - 校企合作转化\n"}, {"name": "getWarningPanelResult", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 预警面板\n"}], "constructors": []}