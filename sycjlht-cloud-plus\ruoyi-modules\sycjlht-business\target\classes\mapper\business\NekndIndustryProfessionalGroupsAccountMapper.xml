<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryProfessionalGroupsAccountMapper">
    
    <resultMap type="NekndIndustryProfessionalGroupsAccount" id="NekndIndustryProfessionalGroupsAccountResult">
        <result property="industryId"    column="industry_id"    />
        <result property="industryName"    column="industry_name"    />
        <result property="intelligentAccount"    column="intelligent_account"    />
        <result property="informationAccount"    column="information_account"    />
        <result property="healthAccount"    column="health_account"    />
        <result property="commercialAccount"    column="commercial_account"    />
        <result property="otherAccount"    column="other_account"    />
    </resultMap>

    <sql id="selectNekndIndustryProfessionalGroupsAccountVo">
        select industry_id, industry_name, intelligent_account, information_account, health_account, commercial_account, other_account from neknd_industry_professional_groups_account
    </sql>

    <select id="selectNekndIndustryProfessionalGroupsAccountList" parameterType="NekndIndustryProfessionalGroupsAccount" resultMap="NekndIndustryProfessionalGroupsAccountResult">
        <include refid="selectNekndIndustryProfessionalGroupsAccountVo"/>
        <where>  
            <if test="industryName != null "> and industry_name like concat('%', #{industryName}, '%')</if>
            <if test="intelligentAccount != null "> and intelligent_account = #{intelligentAccount}</if>
            <if test="informationAccount != null "> and information_account = #{informationAccount}</if>
            <if test="healthAccount != null "> and health_account = #{healthAccount}</if>
            <if test="commercialAccount != null "> and commercial_account = #{commercialAccount}</if>
            <if test="otherAccount != null "> and other_account = #{otherAccount}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryProfessionalGroupsAccountByIndustryId" parameterType="Long" resultMap="NekndIndustryProfessionalGroupsAccountResult">
        <include refid="selectNekndIndustryProfessionalGroupsAccountVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustryProfessionalGroupsAccountByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_professional_groups_account
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustryProfessionalGroupsAccount" parameterType="NekndIndustryProfessionalGroupsAccount" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_professional_groups_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="industryName != null">industry_name,</if>
            <if test="intelligentAccount != null">intelligent_account,</if>
            <if test="informationAccount != null">information_account,</if>
            <if test="healthAccount != null">health_account,</if>
            <if test="commercialAccount != null">commercial_account,</if>
            <if test="otherAccount != null">other_account,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="industryName != null">#{industryName},</if>
            <if test="intelligentAccount != null">#{intelligentAccount},</if>
            <if test="informationAccount != null">#{informationAccount},</if>
            <if test="healthAccount != null">#{healthAccount},</if>
            <if test="commercialAccount != null">#{commercialAccount},</if>
            <if test="otherAccount != null">#{otherAccount},</if>
        </trim>
        On Duplicate Key Update industry_id = values(industry_id), industry_name = values(industry_name), intelligent_account = values(intelligent_account), information_account = values(information_account), health_account = values(health_account), commercial_account = values(commercial_account), other_account = values(other_account)
    </insert>

    <update id="updateNekndIndustryProfessionalGroupsAccount" parameterType="NekndIndustryProfessionalGroupsAccount">
        update neknd_industry_professional_groups_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="industryName != null">industry_name = #{industryName},</if>
            <if test="intelligentAccount != null">intelligent_account = #{intelligentAccount},</if>
            <if test="informationAccount != null">information_account = #{informationAccount},</if>
            <if test="healthAccount != null">health_account = #{healthAccount},</if>
            <if test="commercialAccount != null">commercial_account = #{commercialAccount},</if>
            <if test="otherAccount != null">other_account = #{otherAccount},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryProfessionalGroupsAccountByIndustryId" parameterType="Long">
        delete from neknd_industry_professional_groups_account where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryProfessionalGroupsAccountByIndustryIds" parameterType="String">
        delete from neknd_industry_professional_groups_account where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>