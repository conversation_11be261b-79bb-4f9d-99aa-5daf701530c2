<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryPolicyNumberMapper">
    
    <resultMap type="NekndIndustryPolicyNumber" id="NekndIndustryPolicyNumberResult">
        <result property="industryId"    column="industry_id"    />
        <result property="year"    column="year"    />
        <result property="number"    column="number"    />
    </resultMap>

    <sql id="selectNekndIndustryPolicyNumberVo">
        select industry_id, year, number from neknd_industry_policy_number
    </sql>

    <select id="selectNekndIndustryPolicyNumberList" parameterType="NekndIndustryPolicyNumber" resultMap="NekndIndustryPolicyNumberResult">
        <include refid="selectNekndIndustryPolicyNumberVo"/>
        <where>  
            <if test="year != null "> and year = #{year}</if>
            <if test="number != null "> and number = #{number}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryPolicyNumberByIndustryId" parameterType="Long" resultMap="NekndIndustryPolicyNumberResult">
        <include refid="selectNekndIndustryPolicyNumberVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustryPolicyNumberByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_policy_number
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustryPolicyNumber" parameterType="NekndIndustryPolicyNumber" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_policy_number
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="year != null">year,</if>
            <if test="number != null">number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="year != null">#{year},</if>
            <if test="number != null">#{number},</if>
        </trim>
        On Duplicate Key Update industry_id = values(industry_id), year = values(year), number = values(number)
    </insert>

    <update id="updateNekndIndustryPolicyNumber" parameterType="NekndIndustryPolicyNumber">
        update neknd_industry_policy_number
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="number != null">number = #{number},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryPolicyNumberByIndustryId" parameterType="Long">
        delete from neknd_industry_policy_number where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryPolicyNumberByIndustryIds" parameterType="String">
        delete from neknd_industry_policy_number where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>