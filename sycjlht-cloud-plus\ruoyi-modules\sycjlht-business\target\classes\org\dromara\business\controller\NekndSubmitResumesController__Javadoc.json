{"doc": " 查看投递简历Controller\n \n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询查看投递简历列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSubmitResumes"], "doc": " 导出查看投递简历列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取查看投递简历详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": " 新增查看投递简历\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": " 修改查看投递简历\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除查看投递简历\n"}, {"name": "getMyResumes", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前用户的投递简历列表\n"}, {"name": "withdraw", "paramTypes": ["java.lang.Integer"], "doc": " 撤回投递的简历\n"}], "constructors": []}