{"doc": " 课程视频Service接口\n\n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesVideosByVideoId", "paramTypes": ["java.lang.Integer"], "doc": " 查询课程视频\n\n @param videoId 课程视频主键\n @return 课程视频\n"}, {"name": "selectNekndCoursesVideosList", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": " 查询课程视频列表\n\n @param nekndCoursesVideos 课程视频\n @return 课程视频集合\n"}, {"name": "insertNekndCoursesVideos", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": " 新增课程视频\n\n @param nekndCoursesVideos 课程视频\n @return 结果\n"}, {"name": "updateNekndCoursesVideos", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": " 修改课程视频\n\n @param nekndCoursesVideos 课程视频\n @return 结果\n"}, {"name": "deleteNekndCoursesVideosByVideoIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除课程视频\n\n @param videoIds 需要删除的课程视频主键集合\n @return 结果\n"}, {"name": "deleteNekndCoursesVideosByVideoId", "paramTypes": ["java.lang.Integer"], "doc": " 删除课程视频信息\n\n @param videoId 课程视频主键\n @return 结果\n"}], "constructors": []}