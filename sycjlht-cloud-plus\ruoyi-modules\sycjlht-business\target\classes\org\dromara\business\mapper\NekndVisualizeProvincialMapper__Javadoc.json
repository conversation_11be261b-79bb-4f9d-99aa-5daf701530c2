{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getParticipateData", "paramTypes": ["java.lang.String"], "doc": " 获取参加园区、企业、高校数\n"}, {"name": "getProvincialDynamicMonitoring", "paramTypes": ["java.lang.String"], "doc": " 市域动态监测\n"}, {"name": "getProvincialParkDistribution", "paramTypes": ["java.lang.String"], "doc": " 园区分布及产值 - 数量和产值\n"}, {"name": "getProvincialParkDistributionProjectCount", "paramTypes": ["java.lang.String"], "doc": " 园区分布及产值 - 产教融合项目数\n"}, {"name": "getProvincialPolicyLanding", "paramTypes": ["java.lang.String"], "doc": " 政策落地\n"}, {"name": "getKeyLeadingEnterprise", "paramTypes": ["java.lang.String"], "doc": " 重点企业排名\n"}, {"name": "getProvincialNationalLevelSchool", "paramTypes": ["java.lang.String"], "doc": " 各省水平院校数量分布\n"}, {"name": "getProvincialTalentTrainingConversionRate", "paramTypes": ["java.lang.String"], "doc": " 贯通培养人数转化率\n"}, {"name": "getProvincialSkillCertificate", "paramTypes": ["java.lang.String"], "doc": " 技能证书 - 技能等级证书数量\n"}, {"name": "getProvincialProfessionalSkillCertificate", "paramTypes": ["java.lang.String"], "doc": " 技能证书 - 专业技能等级证书数量\n"}, {"name": "getKeyHelpList", "paramTypes": ["java.lang.String"], "doc": " 重点帮扶清单\n"}], "constructors": []}