{"doc": " 时间工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getNowDate", "paramTypes": [], "doc": " 获取当前Date型日期\n\n @return Date() 当前日期\n"}, {"name": "getDate", "paramTypes": [], "doc": " 获取当前日期, 默认格式为yyyy-MM-dd\n\n @return String\n"}, {"name": "datePath", "paramTypes": [], "doc": " 日期路径 即年/月/日 如2018/08/08\n"}, {"name": "dateTime", "paramTypes": [], "doc": " 日期路径 即年/月/日 如20180808\n"}, {"name": "parseDate", "paramTypes": ["java.lang.Object"], "doc": " 日期型字符串转化为日期 格式\n"}, {"name": "getServerStartDate", "paramTypes": [], "doc": " 获取服务器启动时间\n"}, {"name": "differentDaysByMillisecond", "paramTypes": ["java.util.Date", "java.util.Date"], "doc": " 计算相差天数\n"}, {"name": "timeDistance", "paramTypes": ["java.util.Date", "java.util.Date"], "doc": " 计算时间差\n\n @param endDate 最后时间\n @param startTime 开始时间\n @return 时间差（天/小时/分钟）\n"}, {"name": "toDate", "paramTypes": ["java.time.LocalDateTime"], "doc": " 增加 LocalDateTime ==> Date\n"}, {"name": "toDate", "paramTypes": ["java.time.LocalDate"], "doc": " 增加 LocalDate ==> Date\n"}], "constructors": []}