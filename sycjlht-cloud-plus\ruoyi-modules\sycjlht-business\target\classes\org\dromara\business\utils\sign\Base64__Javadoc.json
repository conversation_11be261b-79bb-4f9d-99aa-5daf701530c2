{"doc": " Base64工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "encode", "paramTypes": ["byte[]"], "doc": " Encodes hex octects into Base64\n\n @param binaryData Array containing binaryData\n @return Encoded Base64 array\n"}, {"name": "decode", "paramTypes": ["java.lang.String"], "doc": " Decodes Base64 data into octects\n\n @param encoded string containing Base64 data\n @return Array containind decoded data.\n"}, {"name": "removeWhiteSpace", "paramTypes": ["char[]"], "doc": " remove WhiteSpace from MIME containing encoded Base64 data.\n\n @param data the byte array of base64 data (with WS)\n @return the new length\n"}], "constructors": []}