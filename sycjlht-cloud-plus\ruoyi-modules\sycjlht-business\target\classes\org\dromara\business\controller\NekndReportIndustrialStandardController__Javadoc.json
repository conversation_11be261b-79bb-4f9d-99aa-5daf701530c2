{"doc": " 工业标准报告Controller\n \n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询工业标准报告列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户查询工业标准报告列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": " 导出工业标准报告列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取工业标准报告详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": " 新增工业标准报告\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndReportIndustrialStandard"], "doc": " 修改工业标准报告\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除工业标准报告\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新报告状态\n"}, {"name": "downloadReport", "paramTypes": ["java.lang.Integer"], "doc": " 下载报告文件\n"}], "constructors": []}