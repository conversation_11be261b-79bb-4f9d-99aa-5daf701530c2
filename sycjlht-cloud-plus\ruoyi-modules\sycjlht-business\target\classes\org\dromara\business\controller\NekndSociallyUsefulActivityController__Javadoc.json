{"doc": " 公益活动Controller\n \n <AUTHOR>\n @date 2024-11-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": " 查询公益活动列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": " 导出公益活动列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取公益活动详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": " 新增公益活动\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": " 修改公益活动\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除公益活动\n"}], "constructors": []}