package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import cn.dev33.satoken.annotation.SaIgnore;
import org.dromara.business.annotation.Log;
import org.dromara.common.core.constant.HttpStatus;
import org.dromara.business.enums.BusinessType;
import org.dromara.business.utils.StringUtils;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.*;
import org.dromara.business.domain.vo.DockingRecordsVo;
import org.dromara.business.domain.vo.NekndSupplyDemandVo;
import org.dromara.business.service.*;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.api.RemoteDictService;
import org.dromara.system.api.RemoteDeptService;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.RemoteCompanyService;
import org.dromara.system.api.model.LoginUser;
import org.dromara.system.api.domain.vo.RemoteDictDataVo;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 供需管理控制器
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@RestController
@RequestMapping("/supplyDemand")
@RequiredArgsConstructor
public class NekndSupplyDemandController extends BaseController {

    private final INekndSupplyDemandService nekndSupplyDemandService;
    private final INekndServiceRequirementsService serviceRequirementsService;
    private final INekndScientificResearchService scientificResearchService;
    private final INekndEmployService nekndEmployService;
    private final INekndResearchAchievementService researchAchievementService;
    private final INekndCertificatesService researchCertificatesService;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteCompanyService remoteCompanyService;

    /**
     * 查询项目需求列表
     */
    @SaCheckPermission("business:supplyDemand:list")
    @GetMapping("/list")
    public TableDataInfo<NekndSupplyDemand> list(NekndSupplyDemand nekndSupplyDemand, PageQuery pageQuery) {
        // 获取当前用户的角色
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return TableDataInfo.build();
        }
        // TODO: 需要通过RemoteUserService获取角色组信息
        String roleGroup = "超级管理员";
        // 判断是不是服务商,如果是服务商则只显示审核通过的项目需求
        if (roleGroup.equals("企业端(服务商)") || roleGroup.equals("超级管理员")) {
            nekndSupplyDemand.setReviewStatus("1");
        }

        // TODO: 待实现分页查询业务逻辑 - 需要Service层添加queryPageList方法并填充企业名称
        // return nekndSupplyDemandService.queryPageList(nekndSupplyDemand, pageQuery);
        return TableDataInfo.build(); // 暂时返回空结果，待业务逻辑实现
    }

    /**
     * 导出项目需求列表
     */
    @SaCheckPermission("business:supplyDemand:export")
    @Log(title = "项目需求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NekndSupplyDemand nekndSupplyDemand) {
        List<NekndSupplyDemand> list = nekndSupplyDemandService.selectNekndSupplyDemandList(nekndSupplyDemand);
        ExcelUtil.exportExcel(list, "项目需求", NekndSupplyDemand.class, response);
    }

    @Log(title = "导入需求", businessType = BusinessType.IMPORT)
    @SaCheckPermission("business:supplyDemand:import")
    @PostMapping("/importData")
    public R<String> importData(MultipartFile file, String deptId) throws Exception {
        List<NekndSupplyDemand> nekndSupplyDemandList = ExcelUtil
                .importExcel(file.getInputStream(), NekndSupplyDemand.class, null).getList();
        String operName = LoginHelper.getUsername();
        String message = nekndSupplyDemandService.importSupplyDemand(nekndSupplyDemandList, operName, deptId);
        return R.ok(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<NekndSupplyDemand>(), "项目需求模板", NekndSupplyDemand.class, response);
    }

    /**
     * 获取项目需求信息
     */
    @SaCheckPermission("business:supplyDemand:query")
    @GetMapping(value = "/{id}")
    public R<NekndSupplyDemand> getInfo(@PathVariable("id") Integer id) {
        return R.ok(nekndSupplyDemandService.selectNekndSupplyDemandById(id));
    }

    /**
     * 新增项目需求
     */
    @SaCheckPermission("business:supplyDemand:add")
    @Log(title = "项目需求", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@RequestBody NekndSupplyDemand nekndSupplyDemand) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null)
            return R.fail("获取当前用户失败！");
        nekndSupplyDemand.setDeptId(loginUser.getDeptId());
        // 通过Remote服务获取部门名称
        String deptName = remoteDeptService.selectDeptNameByIds(loginUser.getDeptId().toString());
        nekndSupplyDemand.setDeptName(deptName);
        // 创建项目需求时不能选择已对接，固定为0，表示待对接
        nekndSupplyDemand.setDockingStatus("0");
        String provincialName = nekndSupplyDemand.getProvincialName();
        String cityName = nekndSupplyDemand.getCityName();
        if (StringUtils.isEmpty(provincialName) || StringUtils.isEmpty(cityName)) {
            return toAjax(nekndSupplyDemandService.insertNekndSupplyDemand(nekndSupplyDemand));
        } else {
            if (provincialName.equals(cityName)) {
                nekndSupplyDemand.setAddress(provincialName);
            } else {
                nekndSupplyDemand.setAddress(provincialName + cityName);
            }
            return toAjax(nekndSupplyDemandService.insertNekndSupplyDemand(nekndSupplyDemand));
        }
    }

    /**
     * 修改项目需求
     */
    @SaCheckPermission("business:supplyDemand:edit")
    @Log(title = "项目需求", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@RequestBody NekndSupplyDemand nekndSupplyDemand) {
        String provincialName = nekndSupplyDemand.getProvincialName();
        String cityName = nekndSupplyDemand.getCityName();
        // 修改项目需求时不能选择已对接，固定为0，表示待对接
        nekndSupplyDemand.setDockingStatus("0");
        if (StringUtils.isEmpty(provincialName) || StringUtils.isEmpty(cityName)) {
            return toAjax(nekndSupplyDemandService.updateNekndSupplyDemand(nekndSupplyDemand));
        } else {
            if (provincialName.equals(cityName)) {
                nekndSupplyDemand.setAddress(provincialName);
            } else {
                nekndSupplyDemand.setAddress(provincialName + cityName);
            }
            return toAjax(nekndSupplyDemandService.updateNekndSupplyDemand(nekndSupplyDemand));
        }
    }

    /**
     * 删除项目需求
     */
    @SaCheckPermission("business:supplyDemand:remove")
    @Log(title = "项目需求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Integer[] ids) {
        return toAjax(nekndSupplyDemandService.deleteNekndSupplyDemandByIds(ids));
    }

    /**
     * 门户网站查看对接状态
     */
    @GetMapping("/dockingStatus/{id}")
    @SaIgnore
    public R<List<String>> dockingStatus(@PathVariable("id") Integer id) {
        return R.ok(nekndSupplyDemandService.dockingStatus(id));
    }

    /**
     * 企业后台项目对接记录
     */
    @Deprecated
    @GetMapping("/dockingRecord/{id}")
    @SaIgnore
    public TableDataInfo<DockingRecordsVo> dockingRecord(@PathVariable("id") Integer id, PageQuery pageQuery) {
        // TODO: 需要Service层实现queryPageList方法
        // return nekndSupplyDemandService.selectDockingRecordPage(id, pageQuery);
        List<DockingRecordsVo> list = nekndSupplyDemandService.selectDockingRecord(id);
        return TableDataInfo.build(list); // 临时使用，待实现分页
    }

    /**
     * 获取项目需求详细信息
     */
    @SaIgnore
    @GetMapping(value = "detail/{id}")
    public R<NekndSupplyDemandVo> getInfoDetail(@PathVariable("id") Integer id) {
        NekndSupplyDemandVo nekndSupplyDemandVo = nekndSupplyDemandService.getInfoDetail(id);
        if (nekndSupplyDemandVo == null) {
            return R.fail("未找到对应数据！");
        }
        // 通过Remote服务查询字典数据进行翻译
        List<RemoteDictDataVo> dictDataList = remoteDictService.selectDictDataByType("sys_abutment");
        for (RemoteDictDataVo dictData : dictDataList) {
            if (dictData.getDictValue().equals(nekndSupplyDemandVo.getDockingStatus())) {
                nekndSupplyDemandVo.setDockingStatus(dictData.getDictLabel());
                break;
            }
        }
        // 根据企业查询企业logo
        Long deptId = nekndSupplyDemandVo.getDeptId();
        if (!StringUtils.hasText(deptId.toString())) {
            return R.fail("未找到对应企业！");
        }
        // TODO: 需要通过RemoteCompanyService获取企业logo,暂时注释掉
        // nekndSupplyDemandVo.getParams().put("company_logo_uri",
        // remoteCompanyService.getCompanyLogoByCompanyId(deptId.intValue()));
        return R.ok(nekndSupplyDemandVo);
    }

    /**
     * 查询项目推荐列表
     */
    @SaIgnore
    @GetMapping("getDemand")
    public TableDataInfo<NekndSupplyDemand> getDemand(NekndSupplyDemand nekndSupplyDemand, PageQuery pageQuery) {
        // TODO: 需要Service层实现queryPageList方法
        // return nekndSupplyDemandService.queryPageList(nekndSupplyDemand, pageQuery);
        List<NekndSupplyDemand> list = nekndSupplyDemandService.selectNekndSupplyDemandList(nekndSupplyDemand);
        return TableDataInfo.build(list); // 临时使用，待实现分页
    }

    /**
     * 查询项目需求列表,并排序
     */
    @SaIgnore
    @GetMapping("/sortList")
    public TableDataInfo<NekndSupplyDemand> sortList(NekndSupplyDemand nekndSupplyDemand, PageQuery pageQuery) {
        // TODO: 需要Service层实现queryPageList方法
        // return nekndSupplyDemandService.selectSortListPage(nekndSupplyDemand,
        // pageQuery);
        List<NekndSupplyDemand> list = nekndSupplyDemandService.selectSortList(nekndSupplyDemand);
        if (list == null) {
            return TableDataInfo.build(new ArrayList<>());
        }
        for (NekndSupplyDemand supplyDemand : list) {
            if (supplyDemand != null) {
                Long deptId = supplyDemand.getDeptId();
                if (deptId != null) {
                    // TODO: 需要通过RemoteCompanyService获取企业信息
                    // NekndCompany company =
                    // remoteCompanyService.getCompanyInfoByCompanyId(deptId.intValue());
                    // if(company!=null&&company.getCompanyName()!=null){
                    // supplyDemand.setDeptName(company.getCompanyName());
                    // }
                }
            }

            // 获取字典数据进行翻译
            List<RemoteDictDataVo> dictDataList = remoteDictService.selectDictDataByType("sys_demand");
            for (RemoteDictDataVo sysDictData : dictDataList) {
                if (sysDictData.getDictValue().equals(supplyDemand.getDemandType())) {
                    String dictLabel = sysDictData.getDictLabel();
                    supplyDemand.getParams().put("demandType", dictLabel);
                }
            }
        }
        return TableDataInfo.build(list);
    }

    /**
     * 企业查询自己的项目需求列表
     */
    @SaCheckPermission("business:supplyDemand:list")
    @GetMapping("/getListDemand")
    public TableDataInfo<NekndSupplyDemand> getlistDemand(NekndSupplyDemand nekndSupplyDemand, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            TableDataInfo<NekndSupplyDemand> rspData = new TableDataInfo<>();
            rspData.setCode(HttpStatus.ERROR);
            rspData.setMsg("获取当前用户失败");
            return rspData;
        }
        // TODO: 需要通过RemoteUserService获取角色组信息
        String roleGroup = "超级管理员";
        if (roleGroup.equals("超级管理员")) {
            // 如果是管理员，则显示所有部门
        } else if (roleGroup.contains("企业端(需求方)")) {
            nekndSupplyDemand.setDeptId(loginUser.getDeptId());
        }
        // TODO: 需要Service层实现queryPageList方法
        // return nekndSupplyDemandService.selectDemandListPage(nekndSupplyDemand,
        // pageQuery);
        List<NekndSupplyDemand> list = nekndSupplyDemandService.selectDemandList(nekndSupplyDemand);
        selectAndSetCompanyName(list);
        return TableDataInfo.build(list); // 临时使用，待实现分页
    }

    /**
     * 审核项目需求
     * 1.审核通过 2.审核不通过
     */
    @SaCheckPermission("business:supplyDemand:audit")
    @Log(title = "项目需求", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{id}/{reviewStatus}")
    public R<Void> audit(@PathVariable("id") Integer id, @PathVariable("reviewStatus") String reviewStatus) {
        // TODO 发送一条消息给项目需求所属的公司，审核的状态
        return toAjax(nekndSupplyDemandService.auditSupplyDemand(id, reviewStatus));
    }

    /**
     * 查看项目审核列表
     */
    @SaCheckPermission("business:supplyDemand:list")
    @GetMapping("/getListAudit")
    public TableDataInfo<NekndSupplyDemand> getListAudit(NekndSupplyDemand nekndSupplyDemand, PageQuery pageQuery) {
        // TODO: 需要Service层实现queryPageList方法
        // return nekndSupplyDemandService.selectAuditDemandListPage(nekndSupplyDemand,
        // pageQuery);
        List<NekndSupplyDemand> list = nekndSupplyDemandService.selectAuditDemandList(nekndSupplyDemand);
        selectAndSetCompanyName(list);
        return TableDataInfo.build(list); // 临时使用，待实现分页
    }

    private void selectAndSetCompanyName(List<NekndSupplyDemand> list) {
        for (NekndSupplyDemand supplyDemand : list) {
            if (supplyDemand != null) {
                Long deptId = supplyDemand.getDeptId();
                if (deptId != null) {
                    // TODO: 需要通过RemoteCompanyService获取企业信息
                    // NekndCompany company =
                    // remoteCompanyService.getCompanyInfoByCompanyId(deptId.intValue());
                    // if(company!=null&&company.getCompanyName()!=null){
                    // supplyDemand.setDeptName(company.getCompanyName());
                    // }
                }
            }
        }
    }

    /**
     * 根据项目需求id查询项目需求的对接状态
     */
    @SaIgnore
    @Deprecated
    @GetMapping("/getDemandDockingStatus/{id}")
    public R<Integer> getDemandStatus(@PathVariable("id") Integer id) {
        return R.ok(Integer.parseInt(nekndSupplyDemandService.getDemandDockingStatus(id)));
    }

    /**
     * 首页企业需求数据获取
     */
    @SaIgnore
    @GetMapping("/enterpriseNeedsDataAcquisition")
    public R<HashMap<String, Object>> getData() {
        // 服务商需求数量
        List<NekndServiceRequirements> nekndServiceRequirementsList = serviceRequirementsService
                .getNekndServiceRequirementsList(null);
        Integer countServiceRequirements = nekndServiceRequirementsList.size();
        // 科普研学数量
        int countScientificResearches = scientificResearchService.getCountScientificResearches();
        // 入驻企业数量 - TODO: 需要通过RemoteCompanyService获取
        Integer countCompany = 0; // remoteCompanyService.getCountCompanyNew();
        // 需求总数
        Integer countSupplyDemand = nekndSupplyDemandService.getCountSupplyDemand();
        // 待对接
        Integer countNotDocked = nekndSupplyDemandService.getCountNotDocked();
        // 已对接
        Integer countDocked = nekndSupplyDemandService.getCountDocked();
        // 已对接需求金额
        int TotalMoney = nekndSupplyDemandService.getTotalMoney();
        // 岗位数量
        int countEmploys = nekndEmployService.selectNekndEmployCount();
        // 入驻人才 - TODO: 需要确认正确的方法名
        int countExpert = 0; // personService.getCountExpert();
        // 入驻教育机构 - TODO: 需要通过RemoteCompanyService获取
        int countEducation = 0; // remoteCompanyService.getCountEducation();
        // 证书培训
        int countCertification = researchCertificatesService.getCountCertification();
        // 入驻服务商 - TODO: 需要通过RemoteCompanyService获取
        int countCompanyService = 0; // remoteCompanyService.getCompanyService();
        // 科技成果
        int countAchievement = researchAchievementService.getCountAchievement();

        HashMap<String, Object> map = new HashMap<>();
        map.put("countCompany", countCompany);
        map.put("countSupplyDemand", countSupplyDemand);
        map.put("TotalMoney", TotalMoney);
        map.put("countExpert", countExpert);
        map.put("countEducation", countEducation);
        map.put("countCertification", countCertification);
        map.put("countCompanyService", countCompanyService);
        map.put("countAchievement", countAchievement);
        map.put("countEmploys", countEmploys);
        map.put("countNotDocked", countNotDocked);
        map.put("countDocked", countDocked);
        map.put("countServiceRequirements", countServiceRequirements);
        map.put("countScientificResearches", countScientificResearches);
        return R.ok(map);
    }
}