{"doc": " 数据大屏Mapper接口\n\n <AUTHOR>\n @date 2025-04-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndLargeDataScreenById", "paramTypes": ["java.lang.Long"], "doc": " 查询数据大屏\n\n @param id 数据大屏主键\n @return 数据大屏\n"}, {"name": "selectNekndLargeDataScreenList", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 查询数据大屏列表\n\n @param nekndLargeDataScreen 数据大屏\n @return 数据大屏集合\n"}, {"name": "insertNekndLargeDataScreen", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 新增数据大屏\n\n @param nekndLargeDataScreen 数据大屏\n @return 结果\n"}, {"name": "updateNekndLargeDataScreen", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 修改数据大屏\n\n @param nekndLargeDataScreen 数据大屏\n @return 结果\n"}, {"name": "deleteNekndLargeDataScreenById", "paramTypes": ["java.lang.Long"], "doc": " 删除数据大屏\n\n @param id 数据大屏主键\n @return 结果\n"}, {"name": "deleteNekndLargeDataScreenByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除数据大屏\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}