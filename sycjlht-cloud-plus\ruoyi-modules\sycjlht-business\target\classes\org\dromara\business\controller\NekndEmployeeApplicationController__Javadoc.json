{"doc": " 公司和学校的员工申请记录Controller\n\n <AUTHOR>\n @date 2024-09-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询公司和学校的员工申请记录列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 导出公司和学校的员工申请记录列表\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取公司和学校的员工申请记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 新增公司和学校的员工申请记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 修改公司和学校的员工申请记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除公司和学校的员工申请记录\n"}], "constructors": []}