{"doc": " 订单班报名记录Service接口\n\n <AUTHOR>\n @date 2024-08-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndOrderClassRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 查询订单班报名记录\n\n @param id 订单班报名记录主键\n @return 订单班报名记录\n"}, {"name": "selectNekndOrderClassRegisterRecordsList", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 查询订单班报名记录列表\n\n @param nekndOrderClassRegisterRecords 订单班报名记录\n @return 订单班报名记录集合\n"}, {"name": "insertNekndOrderClassRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 新增订单班报名记录\n\n @param nekndOrderClassRegisterRecords 订单班报名记录\n @return 结果\n"}, {"name": "updateNekndOrderClassRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 修改订单班报名记录\n\n @param nekndOrderClassRegisterRecords 订单班报名记录\n @return 结果\n"}, {"name": "deleteNekndOrderClassRegisterRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除订单班报名记录\n\n @param ids 需要删除的订单班报名记录主键集合\n @return 结果\n"}, {"name": "deleteNekndOrderClassRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": " 删除订单班报名记录信息\n\n @param id 订单班报名记录主键\n @return 结果\n"}, {"name": "selectNekndOrderClassRegisterRecordsListForExport", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": " 查询导出用的订单班报名记录列表\n\n @param entity 查询条件\n @return 订单班报名记录集合\n"}], "constructors": []}