<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndJuniorcollegeUpgradeUndergraduateMapper">
    
    <resultMap type="NekndJuniorcollegeUpgradeUndergraduate" id="NekndJuniorcollegeUpgradeUndergraduateResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="title"    column="title"    />
        <result property="contentTitle"    column="content_title"    />
        <result property="content"    column="content"    />
        <result property="address"    column="address"    />
        <result property="timeNode"    column="time_node"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sourceUri"    column="source_uri"    />
        <result property="sourceTitle"    column="source_title"    />
        <result property="provincialId"    column="provincial_id"    />
        <result property="provincialName"    column="provincial_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="furtherEducation"    column="further_education"    />
    </resultMap>

    <sql id="selectNekndJuniorcollegeUpgradeUndergraduateVo">
        select id, cover_uri, title, content_title, content, address, time_node, del_flag, create_time, update_time, source_uri, source_title, provincial_id, provincial_name, city_id, city_name,further_education from neknd_juniorcollege_upgrade_undergraduate
    </sql>

    <select id="selectNekndJuniorcollegeUpgradeUndergraduateList" parameterType="NekndJuniorcollegeUpgradeUndergraduate" resultMap="NekndJuniorcollegeUpgradeUndergraduateResult">
        <include refid="selectNekndJuniorcollegeUpgradeUndergraduateVo"/>
        <where>
            del_flag =0
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="contentTitle != null  and contentTitle != ''"> and content_title like concat('%', #{contentTitle}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="sourceTitle != null  and sourceTitle != ''"> and source_title like concat('%', #{sourceTitle}, '%')</if>
            <if test="provincialId != null "> and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''"> and provincial_name = #{provincialName}</if>
            <if test="cityId != null "> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name = #{cityName}</if>
            <if test="furtherEducation != null  and furtherEducation != ''"> and further_education = #{furtherEducation}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndJuniorcollegeUpgradeUndergraduateById" parameterType="Integer" resultMap="NekndJuniorcollegeUpgradeUndergraduateResult">
        <include refid="selectNekndJuniorcollegeUpgradeUndergraduateVo"/>
        where del_flag = 0 and id = #{id}
    </select>
        
    <insert id="insertNekndJuniorcollegeUpgradeUndergraduate" parameterType="NekndJuniorcollegeUpgradeUndergraduate" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_juniorcollege_upgrade_undergraduate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">cover_uri,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="contentTitle != null and contentTitle != ''">content_title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="timeNode != null">time_node,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sourceUri != null">source_uri,</if>
            <if test="sourceTitle != null">source_title,</if>
            <if test="provincialId != null">provincial_id,</if>
            <if test="provincialName != null and provincialName != ''">provincial_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="furtherEducation != null and furtherEducation != ''">further_education,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">#{coverUri},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="contentTitle != null and contentTitle != ''">#{contentTitle},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="timeNode != null">#{timeNode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sourceUri != null">#{sourceUri},</if>
            <if test="sourceTitle != null">#{sourceTitle},</if>
            <if test="provincialId != null">#{provincialId},</if>
            <if test="provincialName != null and provincialName != ''">#{provincialName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="furtherEducation != null and furtherEducation != ''">#{furtherEducation},</if>
         </trim>
    </insert>

    <update id="updateNekndJuniorcollegeUpgradeUndergraduate" parameterType="NekndJuniorcollegeUpgradeUndergraduate">
        update neknd_juniorcollege_upgrade_undergraduate
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="contentTitle != null and contentTitle != ''">content_title = #{contentTitle},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="timeNode != null">time_node = #{timeNode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sourceUri != null">source_uri = #{sourceUri},</if>
            <if test="sourceTitle != null">source_title = #{sourceTitle},</if>
            <if test="provincialId != null">provincial_id = #{provincialId},</if>
            <if test="provincialName != null and provincialName != ''">provincial_name = #{provincialName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="furtherEducation != null and furtherEducation != ''">further_education = #{furtherEducation},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteNekndJuniorcollegeUpgradeUndergraduateById" parameterType="Integer">
        update neknd_juniorcollege_upgrade_undergraduate set del_flag=2 where id = #{id}
    </update>

    <update id="deleteNekndJuniorcollegeUpgradeUndergraduateByIds" parameterType="String">
        update neknd_juniorcollege_upgrade_undergraduate set del_flag =2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>