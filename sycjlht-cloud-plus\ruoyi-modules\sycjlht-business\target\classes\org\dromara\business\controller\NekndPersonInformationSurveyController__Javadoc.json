{"doc": " 学生调查信息控制器\n \n <AUTHOR>\n @date 2024-05-21\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询学生调查列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 导出学生调查列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取学生调查详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 新增学生调查\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 修改学生调查\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除学生调查\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 获取学生调查列表（无权限）\n"}, {"name": "selectSpeciality", "paramTypes": [], "doc": " 按专业筛选\n"}, {"name": "selecty", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取学生后台未填写调查\n"}, {"name": "exportstu", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": " 导出未填写学生调查列表\n"}, {"name": "selectN", "paramTypes": [], "doc": " 获取学生已填写调查\n"}, {"name": "countY", "paramTypes": [], "doc": " 统计未填写问卷人数\n"}, {"name": "countN", "paramTypes": [], "doc": " 统计已填写问卷人数\n"}, {"name": "statistics1", "paramTypes": [], "doc": " 统计信息1 - 就业意向\n"}, {"name": "employmentInfo", "paramTypes": [], "doc": " 获取就业信息统计\n"}, {"name": "getChartIndustry", "paramTypes": [], "doc": " 获取选择工作时，最想进入的行业\n"}, {"name": "getChartSalary", "paramTypes": [], "doc": " 获取对第一份工作的理想薪资\n"}, {"name": "getChartIdea", "paramTypes": [], "doc": " 获取择业观念\n"}, {"name": "getChartSatisfaction", "paramTypes": [], "doc": " 获取对目前工作或就业状态的满意程度\n"}], "constructors": []}