{"doc": " 平台迁移Mapper接口\n\n <AUTHOR>\n @date 2025-02-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPlatformConfigById", "paramTypes": ["java.lang.Integer"], "doc": " 查询平台迁移\n\n @param id 平台迁移主键\n @return 平台迁移\n"}, {"name": "selectNekndPlatformConfigList", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": " 查询平台迁移列表\n\n @param nekndPlatformConfig 平台迁移\n @return 平台迁移集合\n"}, {"name": "insertNekndPlatformConfig", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": " 新增平台迁移\n\n @param nekndPlatformConfig 平台迁移\n @return 结果\n"}, {"name": "updateNekndPlatformConfig", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": " 修改平台迁移\n\n @param nekndPlatformConfig 平台迁移\n @return 结果\n"}, {"name": "deleteNekndPlatformConfigById", "paramTypes": ["java.lang.Integer"], "doc": " 删除平台迁移\n\n @param id 平台迁移主键\n @return 结果\n"}, {"name": "deleteNekndPlatformConfigByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除平台迁移\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}