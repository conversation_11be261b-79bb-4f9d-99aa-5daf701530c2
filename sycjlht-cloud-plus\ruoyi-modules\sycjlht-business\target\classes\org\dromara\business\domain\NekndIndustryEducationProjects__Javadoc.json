{"doc": " 项目库对象 neknd_industry_education_projects\n\n <AUTHOR>\n @date 2025-02-27\n", "fields": [{"name": "id", "doc": "项目库d "}, {"name": "cover<PERSON>ri", "doc": "封面图 "}, {"name": "title", "doc": "项目名称 "}, {"name": "contentTitle", "doc": "简介 "}, {"name": "content", "doc": "项目详情 "}, {"name": "economicBenefits", "doc": "合作经济效益 "}, {"name": "socialBenefits", "doc": "社会效益 "}, {"name": "cooperationPeriod", "doc": "合作期限 "}, {"name": "timeNode", "doc": "合作时间 "}, {"name": "collegeName", "doc": "学院名称 "}, {"name": "schoolDeptId", "doc": "所属学校id "}, {"name": "cooperationType", "doc": "项目类型 "}, {"name": "collaborator", "doc": "合作企业名称 "}, {"name": "professionalTypes", "doc": "专业类型 "}, {"name": "cooperativeEnterprisesNumber", "doc": "合作企业数量 "}, {"name": "professionalGroupIndustryName", "doc": "专业群对应产业名称 "}, {"name": "cooperationAboveScaleNumber", "doc": "合作规模以上企业数量 "}, {"name": "isCooperationAboveScale", "doc": "是否合作规模以上企业 "}, {"name": "isRunningProject", "doc": "是否实质性运行的产教融合、校企合作项目 "}, {"name": "delFlag", "doc": "删除标志（0代表存在 2代表删除） "}, {"name": "classNumber", "doc": "班级数量 "}, {"name": "studentNumber", "doc": "学生人数 "}, {"name": "orderClassName", "doc": "订单班名称"}, {"name": "fieldEngineerProjectLevel", "doc": "项目级别 "}, {"name": "isPhysicalOperation", "doc": "是否实体化运行 "}, {"name": "communityName", "doc": "共同体名称 "}, {"name": "frontLinePostsNumber", "doc": "合作企业接收学校专业课教师到一线挂职数（人） "}, {"name": "partTimeClassesNumber", "doc": "合作企业员工到学校兼课数量（人） "}, {"name": "taskVolume", "doc": "任务量（课时）\n"}, {"name": "acceptingInternStudent", "doc": "合作企业接收实习学生 "}, {"name": "acceptingRecentGraduates", "doc": "合作企业接收应届毕业生\n"}, {"name": "provideAnnualEmployees", "doc": "学院为企业年培训员工数 "}, {"name": "coDevelopCoursesName", "doc": "校企共同开发课程名称 "}, {"name": "coDevelopCoursesNumber", "doc": "校企共同开发课程数量 "}, {"name": "commonTextbookName", "doc": "共同开发教材名称 "}, {"name": "commonTextbookNumber", "doc": "共同开发教材数量 "}, {"name": "trainingBaseName", "doc": "共建产教融合实训基地名称 "}, {"name": "trainingBaseNumber", "doc": "共建产教融合实训基地数量 "}, {"name": "practiceCenterName", "doc": "共建开放型区域产教融合实践中心名称 "}, {"name": "practiceCenterNumber", "doc": "共建开放型区域产教融合实践中心数量 "}, {"name": "annualContractIncome", "doc": "技术合同年收入(万元) "}, {"name": "jointMajorCount", "doc": "共建专业数 "}, {"name": "featured<PERSON><PERSON>or<PERSON><PERSON><PERSON>", "doc": "特色专业方向数 "}, {"name": "trainingWorkstationCount", "doc": "实训基地工位数 "}, {"name": "workstationUtilizationRate", "doc": "工位利用率(%) "}, {"name": "nationalCertificateCount", "doc": "国家级技能证书获取量 "}, {"name": "provincialCertificateCount", "doc": "省级技能证书获取量 "}, {"name": "inventionPatentCount", "doc": "发明专利转化数量 "}, {"name": "utilityPatentCount", "doc": "实用新型专利转化数量 "}, {"name": "masterStudioCount", "doc": "技能大师工作室数 "}, {"name": "professionalGroupMatching", "doc": "专业群匹配度 "}, {"name": "integratedInvestment", "doc": "产教融合投入 "}, {"name": "belongingPark", "doc": "所属园区 "}, {"name": "currentYearDonationValue", "doc": "本学年捐赠学校设备值(万元) "}, {"name": "currentYearPlannedDonationValue", "doc": "本学年准捐赠学校设备值(万元) "}, {"name": "currentYearScholarshipTotal", "doc": "本学年在学校设立的奖学金总额(万元) "}, {"name": "pastGraduatesCount", "doc": "接收近三届毕业生(除当年)总数(人) "}, {"name": "currentGraduatesCount", "doc": "接收应届毕业生人数(人) "}, {"name": "courseFullName", "doc": "课程名称(全称) "}, {"name": "textbookFullName", "doc": "教材名称(全称) "}, {"name": "skillCompetitionName", "doc": "参与指导或组织技能学生竞赛名称 "}, {"name": "industryCollabProjectCount", "doc": "校企合作典型生产实践项目数量 "}, {"name": "trainingBaseCollaboration", "doc": "共建产教融合实训基地(可多选) "}, {"name": "orderTrainedStudents", "doc": "订单培养学生数(人) "}, {"name": "apprenticeshipStudents", "doc": "学徒制培养学生数(人) "}, {"name": "designPatents", "doc": "外观设计专利 "}, {"name": "softwareCopyrights", "doc": "软件著作权 "}, {"name": "verticalProjectsCount", "doc": "纵向项目数量 "}, {"name": "dataStatus", "doc": "信息状态（0未完善1已完善）（咸宁平台导入的状态字段） "}], "enumConstants": [], "methods": [], "constructors": []}