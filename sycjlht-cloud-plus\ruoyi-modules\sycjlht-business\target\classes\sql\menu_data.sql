-- 新闻模块菜单权限配置
-- 业务管理菜单
INSERT INTO sys_menu (menu_id,  menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES (2000,  '业务管理', 0, 4, 'business', NULL, '', 1, 0, 'M', '0', '0', '', 'business', 'admin', NOW(), 'admin', NOW(), '业务管理目录');

-- 新闻管理菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES (2001, '新闻管理', 2000, 1, 'news', 'business/news/index', '', 1, 0, 'C', '0', '0', 'business:news:list', 'news', 'admin', NOW(), 'admin', NOW(), '新闻管理菜单');

-- 新闻管理按钮权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
(2002, '新闻查询', 2001, 1, '', '', '', 1, 0, 'F', '0', '0', 'business:news:query', '#', 'admin', NOW(), 'admin', NOW(), ''),
(2003, '新闻新增', 2001, 2, '', '', '', 1, 0, 'F', '0', '0', 'business:news:add', '#', 'admin', NOW(), 'admin', NOW(), ''),
(2004, '新闻修改', 2001, 3, '', '', '', 1, 0, 'F', '0', '0', 'business:news:edit', '#', 'admin', NOW(), 'admin', NOW(), ''),
(2005, '新闻删除', 2001, 4, '', '', '', 1, 0, 'F', '0', '0', 'business:news:remove', '#', 'admin', NOW(), 'admin', NOW(), ''),
(2006, '新闻导出', 2001, 5, '', '', '', 1, 0, 'F', '0', '0', 'business:news:export', '#', 'admin', NOW(), 'admin', NOW(), '');

-- 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1075040, 2000),
(1075040, 2001),
(1075040, 2002),
(1075040, 2003),
(1075040, 2004),
(1075040, 2005),
(1075040, 2006);
