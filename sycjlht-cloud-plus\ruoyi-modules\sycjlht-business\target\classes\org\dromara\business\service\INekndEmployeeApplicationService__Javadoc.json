{"doc": " 公司和学校的员工申请记录Service接口\n\n <AUTHOR>\n @date 2024-09-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询公司和学校的员工申请记录\n\n @param id 公司和学校的员工申请记录主键\n @return 公司和学校的员工申请记录\n"}, {"name": "selectNekndEmployeeApplicationList", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 查询公司和学校的员工申请记录列表\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 公司和学校的员工申请记录集合\n"}, {"name": "insertNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 新增公司和学校的员工申请记录\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 结果\n"}, {"name": "updateNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 修改公司和学校的员工申请记录\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 结果\n"}, {"name": "deleteNekndEmployeeApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除公司和学校的员工申请记录\n\n @param ids 需要删除的公司和学校的员工申请记录主键集合\n @return 结果\n"}, {"name": "deleteNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除公司和学校的员工申请记录信息\n\n @param id 公司和学校的员工申请记录主键\n @return 结果\n"}, {"name": "insertCompanyEmployeeApplication", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 添加申请加入企业的记录，如果存在就更新成最新的，如果没有就添加\n"}, {"name": "insertSchoolEmployeeApplication", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 添加申请加入学校的记录，如果存在就更新成最新的，如果没有就添加\n"}, {"name": "selectIsEmployeeApplication", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 根据用户id、企业id和是学校或企业的状态查询申请记录是否存在\n @param userId\n @return Boolean true存在，false不存在\n"}, {"name": "updateEmployeeApplicationReviewStatus", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 学校或企业更新申请记录的审核状态\n @param deptId\n @param userId\n @param isApprove 审核状态(0未审核1审核通过2未通过)\n @param status 状态(0企业，1学校)\n @return\n"}, {"name": "importUserApplication", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an", "java.lang.Long", "java.lang.Long"], "doc": " 导入用户数据\n\n\n @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据\n @return 结果\n"}, {"name": "insertUserAuth", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": " 用户授权角色\n\n @param userId 用户ID\n @param roleIds 角色组\n"}, {"name": "checkUserAllowed", "paramTypes": ["java.lang.Object"], "doc": " 校验用户是否允许操作\n\n @param user 用户信息\n"}, {"name": "checkUserDataScope", "paramTypes": ["java.lang.Long"], "doc": " 校验用户是否有数据权限\n\n @param userId 用户id\n"}, {"name": "selectUserList", "paramTypes": ["java.lang.Object"], "doc": " 根据条件分页查询用户列表\n\n @param user 用户信息\n @return 用户信息集合信息\n"}, {"name": "getReviewedTraineesCountBySchoolId", "paramTypes": ["java.lang.Long"], "doc": " 获取学校审核学员总数\n\n @param schoolId 学校ID\n @return 审核学员总数\n"}], "constructors": []}