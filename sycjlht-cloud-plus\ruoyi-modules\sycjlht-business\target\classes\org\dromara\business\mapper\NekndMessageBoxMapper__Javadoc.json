{"doc": " 留言箱Mapper接口\n\n <AUTHOR>\n @date 2024-06-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": " 查询留言箱\n\n @param id 留言箱主键\n @return 留言箱\n"}, {"name": "selectNekndMessageBoxList", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 查询留言箱列表\n\n @param nekndMessageBox 留言箱\n @return 留言箱集合\n"}, {"name": "insertNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 新增留言箱\n\n @param nekndMessageBox 留言箱\n @return 结果\n"}, {"name": "updateNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 修改留言箱\n\n @param nekndMessageBox 留言箱\n @return 结果\n"}, {"name": "deleteNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": " 删除留言箱\n\n @param id 留言箱主键\n @return 结果\n"}, {"name": "deleteNekndMessageBoxByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除留言箱\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}