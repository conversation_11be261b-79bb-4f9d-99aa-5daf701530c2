{"doc": " 文件类型工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getFileType", "paramTypes": ["java.io.File"], "doc": " 获取文件类型\n <p>\n 例如: ruoyi.txt, 返回: txt\n\n @param file 文件名\n @return 后缀（不含\".\")\n"}, {"name": "getFileType", "paramTypes": ["java.lang.String"], "doc": " 获取文件类型\n <p>\n 例如: ruoyi.txt, 返回: txt\n\n @param fileName 文件名\n @return 后缀（不含\".\")\n"}, {"name": "getFileExtendName", "paramTypes": ["byte[]"], "doc": " 获取文件类型\n\n @param photoByte 文件字节码\n @return 后缀（不含\".\")\n"}], "constructors": []}