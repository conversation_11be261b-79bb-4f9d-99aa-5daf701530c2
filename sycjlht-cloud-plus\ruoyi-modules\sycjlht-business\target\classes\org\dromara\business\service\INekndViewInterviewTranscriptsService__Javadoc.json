{"doc": " 面试记录查看Service接口\n\n <AUTHOR>\n @date 2024-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndViewInterviewTranscriptsById", "paramTypes": ["java.lang.Long"], "doc": " 查询面试记录查看\n\n @param id 面试记录查看主键\n @return 面试记录查看\n"}, {"name": "selectNekndViewInterviewTranscriptsList", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 查询面试记录查看列表\n\n @param nekndViewInterviewTranscripts 面试记录查看\n @return 面试记录查看集合\n"}, {"name": "selectPageNekndViewInterviewTranscriptsList", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询面试记录查看列表\n\n @param nekndViewInterviewTranscripts 面试记录查看\n @param pageQuery 分页查询参数\n @return 面试记录查看分页集合\n"}, {"name": "insertNekndViewInterviewTranscripts", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 新增面试记录查看\n\n @return 结果\n"}, {"name": "updateNekndViewInterviewTranscripts", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 修改面试记录查看\n\n @param nekndViewInterviewTranscripts 面试记录查看\n @return 结果\n"}, {"name": "deleteNekndViewInterviewTranscriptsByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除面试记录查看\n\n @param ids 需要删除的面试记录查看主键集合\n @return 结果\n"}, {"name": "deleteNekndViewInterviewTranscriptsById", "paramTypes": ["java.lang.Long"], "doc": " 删除面试记录查看信息\n\n @param id 面试记录查看主键\n @return 结果\n"}, {"name": "getCountByUserId", "paramTypes": ["java.lang.Long"], "doc": " 获取用户投简历数量（所有状态）\n\n @param userId 用户ID\n @return 投简历数量\n"}], "constructors": []}