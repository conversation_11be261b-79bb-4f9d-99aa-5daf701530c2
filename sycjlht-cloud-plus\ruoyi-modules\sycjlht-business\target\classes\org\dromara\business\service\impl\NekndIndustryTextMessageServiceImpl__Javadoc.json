{"doc": " 【请填写功能名称】Service业务层处理\n\n <AUTHOR>\n @date 2024-12-25\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryTextMessageByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustryTextMessageList", "paramTypes": ["org.dromara.business.domain.NekndIndustryTextMessage"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustryTextMessage 【请填写功能名称】\n @return 【请填写功能名称】\n"}, {"name": "insertNekndIndustryTextMessage", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "org.dromara.business.domain.NekndIndustryTextMessage"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustryTextMessage 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustryTextMessage", "paramTypes": ["org.dromara.business.domain.NekndIndustryTextMessage"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustryTextMessage 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustryTextMessageByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industryIds 需要删除的【请填写功能名称】主键\n @return 结果\n"}, {"name": "deleteNekndIndustryTextMessageByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】信息\n\n @param industryId 【请填写功能名称】主键\n @return 结果\n"}], "constructors": []}