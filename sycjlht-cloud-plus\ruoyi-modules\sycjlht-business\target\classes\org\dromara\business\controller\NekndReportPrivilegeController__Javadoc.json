{"doc": " 报告权限管理Controller\n \n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询报告权限管理列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndReportPrivilege"], "doc": " 导出报告权限管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取报告权限管理详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 申请查看和下载报告权限\n"}, {"name": "audit", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 审核报告权限申请\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 修改报告权限管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除报告权限管理\n"}, {"name": "checkPermission", "paramTypes": [], "doc": " 检查当前用户是否有报告查看权限\n"}, {"name": "getMyStatus", "paramTypes": [], "doc": " 获取当前用户的权限申请状态\n"}], "constructors": []}