{"doc": " 培训(证书/活动/项目)Mapper接口\n\n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": " 查询培训(证书/活动/项目)\n\n @param id 培训(证书/活动/项目)主键\n @return 培训(证书/活动/项目)\n"}, {"name": "selectNekndTrainList", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 查询培训(证书/活动/项目)列表\n\n @param nekndTrain 培训(证书/活动/项目)\n @return 培训(证书/活动/项目)集合\n"}, {"name": "insertNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 新增培训(证书/活动/项目)\n\n @param nekndTrain 培训(证书/活动/项目)\n @return 结果\n"}, {"name": "updateNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": " 修改培训(证书/活动/项目)\n\n @param nekndTrain 培训(证书/活动/项目)\n @return 结果\n"}, {"name": "deleteNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": " 删除培训(证书/活动/项目)\n\n @param id 培训(证书/活动/项目)主键\n @return 结果\n"}, {"name": "deleteNekndTrainByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除培训(证书/活动/项目)\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}, {"name": "selectState", "paramTypes": ["java.lang.Integer"], "doc": " 查询是否为线上课程\n @param id\n @return\n"}, {"name": "selectTrainDetail", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 查询培训详情\n @param id\n @param status\n @return\n"}], "constructors": []}