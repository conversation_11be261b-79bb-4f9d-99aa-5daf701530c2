{"doc": " 成员单位信息Mapper接口\n\n <AUTHOR>\n @date 2024-10-22\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": " 查询成员单位信息\n\n @param id 成员单位信息主键\n @return 成员单位信息\n"}, {"name": "selectNekndUnitList", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 查询成员单位信息列表\n\n @param nekndUnit 成员单位信息\n @return 成员单位信息集合\n"}, {"name": "insertNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 新增成员单位信息\n\n @param nekndUnit 成员单位信息\n @return 结果\n"}, {"name": "updateNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 修改成员单位信息\n\n @param nekndUnit 成员单位信息\n @return 结果\n"}, {"name": "deleteNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": " 删除成员单位信息\n\n @param id 成员单位信息主键\n @return 结果\n"}, {"name": "deleteNekndUnitByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除成员单位信息\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}, {"name": "getVisualizeDataOfPark", "paramTypes": ["java.lang.String"], "doc": " 获取检测大屏-市区数据\n @param cityId\n @return\n"}], "constructors": []}