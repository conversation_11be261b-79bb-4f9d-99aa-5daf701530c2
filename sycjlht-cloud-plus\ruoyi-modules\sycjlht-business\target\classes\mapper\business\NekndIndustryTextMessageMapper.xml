<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryTextMessageMapper">
    
    <resultMap type="NekndIndustryTextMessage" id="NekndIndustryTextMessageResult">
        <result property="industryId"    column="industry_id"    />
        <result property="companyAccount"    column="company_account"    />
        <result property="highTechIndustry"    column="high_tech_industry"    />
        <result property="specializedTechnologyIndustry"    column="specialized_technology_industry"    />
        <result property="studentAccount"    column="student_account"    />
        <result property="graduateAccount"    column="graduate_account"    />
        <result property="localEmploymentAccount"    column="local_employment_account"    />
    </resultMap>

    <sql id="selectNekndIndustryTextMessageVo">
        select industry_id, company_account, high_tech_industry, specialized_technology_industry, student_account, graduate_account, local_employment_account from neknd_industry_text_message
    </sql>

    <select id="selectNekndIndustryTextMessageList" parameterType="NekndIndustryTextMessage" resultMap="NekndIndustryTextMessageResult">
        <include refid="selectNekndIndustryTextMessageVo"/>
        <where>  
            <if test="companyAccount != null "> and company_account = #{companyAccount}</if>
            <if test="highTechIndustry != null "> and high_tech_industry = #{highTechIndustry}</if>
            <if test="specializedTechnologyIndustry != null "> and specialized_technology_industry = #{specializedTechnologyIndustry}</if>
            <if test="studentAccount != null "> and student_account = #{studentAccount}</if>
            <if test="graduateAccount != null "> and graduate_account = #{graduateAccount}</if>
            <if test="localEmploymentAccount != null "> and local_employment_account = #{localEmploymentAccount}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryTextMessageByIndustryId" parameterType="Long" resultMap="NekndIndustryTextMessageResult">
        <include refid="selectNekndIndustryTextMessageVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustryTextMessageByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_text_message
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustryTextMessage" parameterType="NekndIndustryTextMessage" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_text_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="companyAccount != null">company_account,</if>
            <if test="highTechIndustry != null">high_tech_industry,</if>
            <if test="specializedTechnologyIndustry != null">specialized_technology_industry,</if>
            <if test="studentAccount != null">student_account,</if>
            <if test="graduateAccount != null">graduate_account,</if>
            <if test="localEmploymentAccount != null">local_employment_account,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="companyAccount != null">#{companyAccount},</if>
            <if test="highTechIndustry != null">#{highTechIndustry},</if>
            <if test="specializedTechnologyIndustry != null">#{specializedTechnologyIndustry},</if>
            <if test="studentAccount != null">#{studentAccount},</if>
            <if test="graduateAccount != null">#{graduateAccount},</if>
            <if test="localEmploymentAccount != null">#{localEmploymentAccount},</if>
        </trim>
        On Duplicate Key Update industry_id = values(industry_id), company_account = values(company_account),high_tech_industry = values(high_tech_industry), specialized_technology_industry = values(specialized_technology_industry), student_account = values(student_account), graduate_account = values(graduate_account), local_employment_account = values(local_employment_account)
    </insert>

    <update id="updateNekndIndustryTextMessage" parameterType="NekndIndustryTextMessage">
        update neknd_industry_text_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyAccount != null">company_account = #{companyAccount},</if>
            <if test="highTechIndustry != null">high_tech_industry = #{highTechIndustry},</if>
            <if test="specializedTechnologyIndustry != null">specialized_technology_industry = #{specializedTechnologyIndustry},</if>
            <if test="studentAccount != null">student_account = #{studentAccount},</if>
            <if test="graduateAccount != null">graduate_account = #{graduateAccount},</if>
            <if test="localEmploymentAccount != null">local_employment_account = #{localEmploymentAccount},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryTextMessageByIndustryId" parameterType="Long">
        delete from neknd_industry_text_message where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryTextMessageByIndustryIds" parameterType="String">
        delete from neknd_industry_text_message where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>