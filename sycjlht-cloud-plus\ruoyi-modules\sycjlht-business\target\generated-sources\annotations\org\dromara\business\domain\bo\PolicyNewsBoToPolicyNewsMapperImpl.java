package org.dromara.business.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.business.domain.PolicyNews;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-10T20:08:18+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class PolicyNewsBoToPolicyNewsMapperImpl implements PolicyNewsBoToPolicyNewsMapper {

    @Override
    public PolicyNews convert(PolicyNewsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PolicyNews policyNews = new PolicyNews();

        policyNews.setSearchValue( arg0.getSearchValue() );
        policyNews.setCreateDept( arg0.getCreateDept() );
        policyNews.setCreateBy( arg0.getCreateBy() );
        policyNews.setCreateTime( arg0.getCreateTime() );
        policyNews.setUpdateBy( arg0.getUpdateBy() );
        policyNews.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            policyNews.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        policyNews.setId( arg0.getId() );
        policyNews.setCoverUri( arg0.getCoverUri() );
        policyNews.setNewsTitle( arg0.getNewsTitle() );
        policyNews.setNewsType( arg0.getNewsType() );
        policyNews.setNewsContent( arg0.getNewsContent() );
        policyNews.setStatus( arg0.getStatus() );
        policyNews.setSourceTitle( arg0.getSourceTitle() );
        policyNews.setPolicyCategory( arg0.getPolicyCategory() );
        policyNews.setPolicy2category( arg0.getPolicy2category() );
        policyNews.setFileType( arg0.getFileType() );
        policyNews.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        policyNews.setPlanType( arg0.getPlanType() );
        policyNews.setBelongPark( arg0.getBelongPark() );
        policyNews.setNewsPosition( arg0.getNewsPosition() );
        policyNews.setFileTypeFunds( arg0.getFileTypeFunds() );
        policyNews.setBelongDistrict( arg0.getBelongDistrict() );

        return policyNews;
    }

    @Override
    public PolicyNews convert(PolicyNewsBo arg0, PolicyNews arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setCoverUri( arg0.getCoverUri() );
        arg1.setNewsTitle( arg0.getNewsTitle() );
        arg1.setNewsType( arg0.getNewsType() );
        arg1.setNewsContent( arg0.getNewsContent() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSourceTitle( arg0.getSourceTitle() );
        arg1.setPolicyCategory( arg0.getPolicyCategory() );
        arg1.setPolicy2category( arg0.getPolicy2category() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        arg1.setPlanType( arg0.getPlanType() );
        arg1.setBelongPark( arg0.getBelongPark() );
        arg1.setNewsPosition( arg0.getNewsPosition() );
        arg1.setFileTypeFunds( arg0.getFileTypeFunds() );
        arg1.setBelongDistrict( arg0.getBelongDistrict() );

        return arg1;
    }
}
