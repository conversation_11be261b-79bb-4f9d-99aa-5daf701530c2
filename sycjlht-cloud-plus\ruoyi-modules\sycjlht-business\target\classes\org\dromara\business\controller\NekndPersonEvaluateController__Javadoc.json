{"doc": " 企业/学校评价Controller\n \n <AUTHOR>\n @date 2024-05-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询企业/学校评价列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPersonEvaluate"], "doc": " 导出企业/学校评价列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取企业/学校评价详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": " 新增企业/学校评价\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": " 修改企业/学校评价\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除企业/学校评价\n"}, {"name": "deptquery", "paramTypes": [], "doc": " 获取当前企业的人员列表\n"}, {"name": "deptquerySchool", "paramTypes": [], "doc": " 获取当前学校的人员列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询个人职业生涯/查询学校或企业评价列表\n"}, {"name": "getUserEvaluateStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取用户的评价统计\n"}, {"name": "getRecentEvaluates", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取最近的评价记录\n"}], "constructors": []}