{"doc": " 证书发布Service业务层处理\n\n <AUTHOR>\n @date 2024-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCertificatesById", "paramTypes": ["java.lang.Integer"], "doc": " 查询证书发布\n\n @param id 证书发布主键\n @return 证书发布\n"}, {"name": "selectNekndCertificatesList", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": " 查询证书发布列表\n\n @param nekndCertificates 证书发布\n @return 证书发布\n"}, {"name": "selectPageNekndCertificatesList", "paramTypes": ["org.dromara.business.domain.NekndCertificates", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询证书发布列表\n\n @param nekndCertificates 证书发布\n @param pageQuery 分页参数\n @return 证书发布分页集合\n"}, {"name": "insertNekndCertificates", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": " 新增证书发布\n\n @param nekndCertificates 证书发布\n @return 结果\n"}, {"name": "updateNekndCertificates", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": " 修改证书发布\n\n @param nekndCertificates 证书发布\n @return 结果\n"}, {"name": "deleteNekndCertificatesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除证书发布\n\n @param ids 需要删除的证书发布主键\n @return 结果\n"}, {"name": "deleteNekndCertificatesById", "paramTypes": ["java.lang.Integer"], "doc": " 删除证书发布信息\n\n @param id 证书发布主键\n @return 结果\n"}], "constructors": []}