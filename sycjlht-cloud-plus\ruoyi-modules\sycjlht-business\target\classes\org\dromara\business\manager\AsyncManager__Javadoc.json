{"doc": " 异步任务管理器\n\n <AUTHOR>\n", "fields": [{"name": "OPERATE_DELAY_TIME", "doc": " 操作延迟10毫秒\n"}, {"name": "executor", "doc": " 异步操作任务调度线程池\n"}], "enumConstants": [], "methods": [{"name": "execute", "paramTypes": ["java.util.TimerTask"], "doc": " 执行任务\n\n @param task 任务\n"}, {"name": "shutdown", "paramTypes": [], "doc": " 停止任务线程池\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " 单例模式\n"}]}