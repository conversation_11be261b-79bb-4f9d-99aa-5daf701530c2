{"doc": " 课程收藏记录Service接口\n\n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesCollectionById", "paramTypes": ["java.lang.Integer"], "doc": " 查询课程收藏记录\n\n @param id 课程收藏记录主键\n @return 课程收藏记录\n"}, {"name": "selectNekndCoursesCollectionList", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": " 查询课程收藏记录列表\n\n @param nekndCoursesCollection 课程收藏记录\n @return 课程收藏记录集合\n"}, {"name": "insertNekndCoursesCollection", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": " 新增课程收藏记录\n\n @param nekndCoursesCollection 课程收藏记录\n @return 结果\n"}, {"name": "updateNekndCoursesCollection", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": " 修改课程收藏记录\n\n @param nekndCoursesCollection 课程收藏记录\n @return 结果\n"}, {"name": "deleteNekndCoursesCollectionByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除课程收藏记录\n\n @param ids 需要删除的课程收藏记录主键集合\n @return 结果\n"}, {"name": "deleteNekndCoursesCollectionById", "paramTypes": ["java.lang.Integer"], "doc": " 删除课程收藏记录信息\n\n @param id 课程收藏记录主键\n @return 结果\n"}, {"name": "selectFavoriteCountByCourseId", "paramTypes": ["java.lang.Integer"], "doc": " 根据课程id查询收藏数量\n @param courseId\n @return 收藏数量\n"}], "constructors": []}