{"doc": " 报告权限管理Service接口\n\n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": " 查询报告权限管理\n\n @param id 报告权限管理主键\n @return 报告权限管理\n"}, {"name": "selectNekndReportPrivilegeList", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 查询报告权限管理列表\n\n @param nekndReportPrivilege 报告权限管理\n @return 报告权限管理集合\n"}, {"name": "insertNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 新增报告权限管理\n\n @param nekndReportPrivilege 报告权限管理\n @return 结果\n"}, {"name": "updateNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 修改报告权限管理\n\n @param nekndReportPrivilege 报告权限管理\n @return 结果\n"}, {"name": "deleteNekndReportPrivilegeByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除报告权限管理\n\n @param ids 需要删除的报告权限管理主键集合\n @return 结果\n"}, {"name": "deleteNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": " 删除报告权限管理信息\n\n @param id 报告权限管理主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询报告权限管理列表\n\n @param nekndReportPrivilege 报告权限管理\n @param pageQuery 分页查询\n @return 报告权限管理集合\n"}, {"name": "queryUserPrivileges", "paramTypes": ["int", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询用户权限\n\n @param reportId 报告ID\n @param pageQuery 分页查询\n @return 用户权限集合\n"}, {"name": "checkReportAccess", "paramTypes": ["int", "java.lang.Integer"], "doc": " 检查报告访问权限\n\n @param userId 用户ID\n @param reportId 报告ID\n @return 是否有访问权限\n"}, {"name": "applyReportAccess", "paramTypes": ["int", "java.lang.Integer", "java.lang.String"], "doc": " 申请报告访问权限\n\n @param userId 用户ID\n @param reportId 报告ID\n @param reason 申请原因\n @return 结果\n"}, {"name": "auditReportAccess", "paramTypes": ["java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 审核报告访问权限\n\n @param privilegeId 权限申请ID\n @param status 审核状态\n @param auditRemark 审核备注\n @return 结果\n"}], "constructors": []}