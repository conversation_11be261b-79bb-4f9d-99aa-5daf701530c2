{"doc": " 科普研学信息Controller\n \n <AUTHOR>\n @date 2024-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询科普研学信息列表\n"}, {"name": "adminList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 管理端查询科普研学信息列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndScientificResearch"], "doc": " 导出科普研学信息列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 导入科普研学信息\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取科普研学信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": " 新增科普研学信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": " 修改科普研学信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除科普研学信息\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 审核科普研学信息\n"}, {"name": "filterByGraduateStage", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按毕业学段筛选科普研学\n"}, {"name": "getPopularResearches", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取热门科普研学\n"}, {"name": "getMyResearches", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前用户创建的科普研学\n"}], "constructors": []}