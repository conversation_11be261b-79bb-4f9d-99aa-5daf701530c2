{"doc": " 继续教育信息Controller\n \n <AUTHOR>\n @date 2024-10-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 查询继续教育信息列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 匿名查询继续教育信息列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 导出继续教育信息列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入继续教育信息\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取继续教育信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 新增继续教育信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": " 修改继续教育信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除继续教育信息\n"}], "constructors": []}