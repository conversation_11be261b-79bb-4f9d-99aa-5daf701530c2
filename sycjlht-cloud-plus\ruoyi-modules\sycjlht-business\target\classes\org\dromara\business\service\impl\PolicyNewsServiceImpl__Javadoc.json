{"doc": " 政策新闻信息Service业务层处理\n 整合了现代化架构和单体业务逻辑\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询政策新闻信息\n\n @param id 新闻ID\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询政策新闻信息列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 查询政策新闻信息列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 新增政策新闻信息\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 修改政策新闻信息\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.business.domain.PolicyNews"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除政策新闻信息\n"}, {"name": "auditNews", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 审核政策新闻\n"}, {"name": "getData", "paramTypes": [], "doc": " 获取政策新闻统计数据\n"}, {"name": "importUser", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 导入政策新闻数据\n"}], "constructors": []}