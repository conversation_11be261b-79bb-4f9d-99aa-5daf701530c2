{"doc": " 证书发布Controller\n\n <AUTHOR>\n @date 2024-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCertificates", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询证书发布列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCertificates"], "doc": " 导出证书发布列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入证书发布\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取证书发布详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": " 新增证书发布\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": " 修改证书发布\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除证书发布\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 修改审核状态\n"}, {"name": "getPopularCertificates", "paramTypes": [], "doc": " 大屏接口\n 热门证书展示\n"}], "constructors": []}