{"doc": " 政策新闻信息Mapper接口\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPreviousNews", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 查询上一条新闻\n\n @param newsType 新闻类型\n @param currentId 当前新闻ID\n @return 上一条新闻\n"}, {"name": "selectNextNews", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 查询下一条新闻\n\n @param newsType 新闻类型\n @param currentId 当前新闻ID\n @return 下一条新闻\n"}, {"name": "selectMeetingNumbers", "paramTypes": [], "doc": " 查询会议数量统计（按年份）\n\n @return 会议数量统计\n"}, {"name": "selectFileNumbers", "paramTypes": [], "doc": " 查询文件数量统计（按类型）\n\n @return 文件数量统计\n"}, {"name": "selectUnitNumbers", "paramTypes": [], "doc": " 查询单位数量统计\n\n @return 单位数量\n"}, {"name": "selectThematicMeetingNumbers", "paramTypes": [], "doc": " 查询主题会议数量统计（按年份）\n\n @return 主题会议数量统计\n"}, {"name": "selectPlanTypeNumbers", "paramTypes": [], "doc": " 查询计划类型数量统计（按类型）\n\n @return 计划类型数量统计\n"}], "constructors": []}