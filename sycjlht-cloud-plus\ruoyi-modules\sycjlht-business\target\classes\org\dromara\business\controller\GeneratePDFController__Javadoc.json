{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典值获取字典标签\n"}, {"name": "renderHtmlContentToCell", "paramTypes": ["com.itextpdf.layout.element.Cell", "java.lang.String", "com.itextpdf.kernel.font.PdfFont"], "doc": " 渲染 HTML 内容到表格单元格中\n\n @param cell       表格单元格\n @param htmlContent HTML 内容\n @param font       字体对象\n @return 包含渲染内容的 Cell 对象\n"}], "constructors": []}