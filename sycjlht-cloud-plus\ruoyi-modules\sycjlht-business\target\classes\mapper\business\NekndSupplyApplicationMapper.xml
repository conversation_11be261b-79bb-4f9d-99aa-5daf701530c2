<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndSupplyApplicationMapper">
    
    <resultMap type="NekndSupplyApplication" id="NekndSupplyApplicationResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="flag"    column="flag"    />
        <result property="address"    column="address"    />
        <result property="money"    column="money"    />
        <result property="useScenarios"    column="use_scenarios"    />
        <result property="useSpce"    column="use_spce"    />
        <result property="useTime"    column="use_time"    />
        <result property="useCount"    column="use_count"    />
        <result property="deliveryMethod"    column="delivery_method"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="sellableRange"    column="sellable_range"    />
        <result property="phone"    column="phone"    />
        <result property="productIntroduction"    column="product_introduction"    />
        <result property="productValue"    column="product_value"    />
        <result property="productScene"    column="product_scene"    />
    </resultMap>

    <sql id="selectNekndSupplyApplicationVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, dept_id, dept_name, cover_uri, title, content, flag, address, money, use_scenarios, use_spce, use_time, use_count, delivery_method, payment_method, sellable_range, phone, product_introduction, product_value, product_scene from neknd_supply_application
    </sql>

    <select id="selectNekndSupplyApplicationList" parameterType="NekndSupplyApplication" resultMap="NekndSupplyApplicationResult">
        <include refid="selectNekndSupplyApplicationVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="coverUri != null  and coverUri != ''"> and cover_uri = #{coverUri}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="money != null  and money != ''"> and money = #{money}</if>
            <if test="useScenarios != null  and useScenarios != ''"> and use_scenarios = #{useScenarios}</if>
            <if test="useSpce != null  and useSpce != ''"> and use_spce = #{useSpce}</if>
            <if test="useTime != null  and useTime != ''"> and use_time = #{useTime}</if>
            <if test="useCount != null  and useCount != ''"> and use_count = #{useCount}</if>
            <if test="deliveryMethod != null  and deliveryMethod != ''"> and delivery_method = #{deliveryMethod}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="sellableRange != null  and sellableRange != ''"> and sellable_range = #{sellableRange}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="productIntroduction != null  and productIntroduction != ''"> and product_introduction = #{productIntroduction}</if>
            <if test="productValue != null  and productValue != ''"> and product_value = #{productValue}</if>
            <if test="productScene != null  and productScene != ''"> and product_scene = #{productScene}</if>
        </where>
    </select>
    
    <select id="selectNekndSupplyApplicationById" parameterType="Integer" resultMap="NekndSupplyApplicationResult">
        <include refid="selectNekndSupplyApplicationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndSupplyApplication" parameterType="NekndSupplyApplication" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_supply_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="coverUri != null">cover_uri,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="flag != null">flag,</if>
            <if test="address != null">address,</if>
            <if test="money != null">money,</if>
            <if test="useScenarios != null">use_scenarios,</if>
            <if test="useSpce != null">use_spce,</if>
            <if test="useTime != null">use_time,</if>
            <if test="useCount != null">use_count,</if>
            <if test="deliveryMethod != null">delivery_method,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="sellableRange != null">sellable_range,</if>
            <if test="phone != null">phone,</if>
            <if test="productIntroduction != null">product_introduction,</if>
            <if test="productValue != null">product_value,</if>
            <if test="productScene != null">product_scene,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="coverUri != null">#{coverUri},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="flag != null">#{flag},</if>
            <if test="address != null">#{address},</if>
            <if test="money != null">#{money},</if>
            <if test="useScenarios != null">#{useScenarios},</if>
            <if test="useSpce != null">#{useSpce},</if>
            <if test="useTime != null">#{useTime},</if>
            <if test="useCount != null">#{useCount},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="sellableRange != null">#{sellableRange},</if>
            <if test="phone != null">#{phone},</if>
            <if test="productIntroduction != null">#{productIntroduction},</if>
            <if test="productValue != null">#{productValue},</if>
            <if test="productScene != null">#{productScene},</if>
         </trim>
    </insert>

    <update id="updateNekndSupplyApplication" parameterType="NekndSupplyApplication">
        update neknd_supply_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="address != null">address = #{address},</if>
            <if test="money != null">money = #{money},</if>
            <if test="useScenarios != null">use_scenarios = #{useScenarios},</if>
            <if test="useSpce != null">use_spce = #{useSpce},</if>
            <if test="useTime != null">use_time = #{useTime},</if>
            <if test="useCount != null">use_count = #{useCount},</if>
            <if test="deliveryMethod != null">delivery_method = #{deliveryMethod},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="sellableRange != null">sellable_range = #{sellableRange},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="productIntroduction != null">product_introduction = #{productIntroduction},</if>
            <if test="productValue != null">product_value = #{productValue},</if>
            <if test="productScene != null">product_scene = #{productScene},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndSupplyApplicationById" parameterType="Integer">
        delete from neknd_supply_application where id = #{id}
    </delete>

    <delete id="deleteNekndSupplyApplicationByIds" parameterType="String">
        delete from neknd_supply_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>