{"doc": " 课程视频Controller\n \n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询课程视频列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesVideos"], "doc": " 导出课程视频列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入课程视频数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取课程视频详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": " 新增课程视频\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": " 修改课程视频\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除课程视频\n"}], "constructors": []}