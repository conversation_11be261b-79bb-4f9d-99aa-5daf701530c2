{"doc": " 课程评价记录Mapper接口\n\n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询课程评价记录\n\n @param id 课程评价记录主键\n @return 课程评价记录\n"}, {"name": "selectNekndCoursesEvaluationList", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": " 查询课程评价记录列表\n\n @param nekndCoursesEvaluation 课程评价记录\n @return 课程评价记录集合\n"}, {"name": "insertNekndCoursesEvaluation", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": " 新增课程评价记录\n\n @param nekndCoursesEvaluation 课程评价记录\n @return 结果\n"}, {"name": "updateNekndCoursesEvaluation", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": " 修改课程评价记录\n\n @param nekndCoursesEvaluation 课程评价记录\n @return 结果\n"}, {"name": "deleteNekndCoursesEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除课程评价记录\n\n @param id 课程评价记录主键\n @return 结果\n"}, {"name": "deleteNekndCoursesEvaluationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除课程评价记录\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}