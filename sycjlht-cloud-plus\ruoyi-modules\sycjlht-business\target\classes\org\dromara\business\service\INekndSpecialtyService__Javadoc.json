{"doc": " 专业信息Service接口\n\n <AUTHOR>\n @date 2024-11-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": " 查询专业信息\n\n @param id 专业信息主键\n @return 专业信息\n"}, {"name": "selectNekndSpecialtyList", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 查询专业信息列表\n\n @param nekndSpecialty 专业信息\n @return 专业信息集合\n"}, {"name": "insertNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 新增专业信息\n\n @param nekndSpecialty 专业信息\n @return 结果\n"}, {"name": "updateNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 修改专业信息\n\n @param nekndSpecialty 专业信息\n @return 结果\n"}, {"name": "deleteNekndSpecialtyByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除专业信息\n\n @param ids 需要删除的专业信息主键集合\n @return 结果\n"}, {"name": "deleteNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": " 删除专业信息信息\n\n @param id 专业信息主键\n @return 结果\n"}], "constructors": []}