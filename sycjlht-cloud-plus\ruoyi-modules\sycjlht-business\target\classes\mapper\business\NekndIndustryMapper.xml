<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryMapper">
    
    <resultMap type="NekndIndustry" id="NekndIndustryResult">
        <result property="industryId"    column="industry_id"    />
        <result property="industryName"    column="industry_name"    />
        <result property="filename"    column="filename"    />
        <result property="fileUri"    column="file_uri"    />
    </resultMap>

    <sql id="selectNekndIndustryVo">
        select industry_id, industry_name, filename, file_uri from neknd_industry
    </sql>

    <select id="selectNekndIndustryList" parameterType="NekndIndustry" resultMap="NekndIndustryResult">
        <include refid="selectNekndIndustryVo"/>
        <where>  
            <if test="industryName != null  and industryName != ''"> and industry_name like concat('%', #{industryName}, '%')</if>
            <if test="filename != null  and filename != ''"> and filename like concat('%', #{filename}, '%')</if>
            <if test="fileUri != null  and fileUri != ''"> and file_uri like concat('%', #{fileUri}, '%')</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryByIndustryId" parameterType="Long" resultMap="NekndIndustryResult">
        <include refid="selectNekndIndustryVo"/>
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustry" parameterType="NekndIndustry" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryName != null">industry_name,</if>
            <if test="filename != null">filename,</if>
            <if test="fileUri != null">file_uri,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryName != null">#{industryName},</if>
            <if test="filename != null">#{filename},</if>
            <if test="fileUri != null">#{fileUri},</if>
         </trim>
    </insert>

    <update id="updateNekndIndustry" parameterType="NekndIndustry">
        update neknd_industry
        <trim prefix="SET" suffixOverrides=",">
            <if test="industryName != null">industry_name = #{industryName},</if>
            <if test="filename != null">filename = #{filename},</if>
            <if test="fileUri != null">file_uri = #{fileUri},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryByIndustryId" parameterType="Long">
        delete from neknd_industry where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryByIndustryIds" parameterType="String">
        delete from neknd_industry where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>