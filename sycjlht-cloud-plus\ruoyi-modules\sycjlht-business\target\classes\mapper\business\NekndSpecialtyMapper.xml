<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndSpecialtyMapper">
    
    <resultMap type="NekndSpecialty" id="NekndSpecialtyResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="specialty"    column="specialty"    />
        <result property="contentTitle"    column="content_title"    />
        <result property="content"    column="content"    />
        <result property="addTime"    column="add_time"    />
        <result property="sourceUri"    column="source_uri"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNekndSpecialtyVo">
        select id, cover_uri, specialty, content_title, content, add_time, source_uri, del_flag, create_time, update_time from neknd_specialty
    </sql>

    <select id="selectNekndSpecialtyList" parameterType="NekndSpecialty" resultMap="NekndSpecialtyResult">
        <include refid="selectNekndSpecialtyVo"/>
        <where>  
            <if test="coverUri != null  and coverUri != ''"> and cover_uri = #{coverUri}</if>
            <if test="specialty != null  and specialty != ''"> and specialty like concat('%', #{specialty}, '%')</if>
            <if test="contentTitle != null  and contentTitle != ''"> and content_title = #{contentTitle}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="addTime != null  and addTime != ''"> and add_time = #{addTime}</if>
            <if test="sourceUri != null  and sourceUri != ''"> and source_uri = #{sourceUri}</if>
        </where>
    </select>
    
    <select id="selectNekndSpecialtyById" parameterType="Integer" resultMap="NekndSpecialtyResult">
        <include refid="selectNekndSpecialtyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndSpecialty" parameterType="NekndSpecialty" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_specialty
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">cover_uri,</if>
            <if test="specialty != null and specialty != ''">specialty,</if>
            <if test="contentTitle != null and contentTitle != ''">content_title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="addTime != null and addTime != ''">add_time,</if>
            <if test="sourceUri != null">source_uri,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">#{coverUri},</if>
            <if test="specialty != null and specialty != ''">#{specialty},</if>
            <if test="contentTitle != null and contentTitle != ''">#{contentTitle},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="addTime != null and addTime != ''">#{addTime},</if>
            <if test="sourceUri != null">#{sourceUri},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNekndSpecialty" parameterType="NekndSpecialty">
        update neknd_specialty
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">cover_uri = #{coverUri},</if>
            <if test="specialty != null and specialty != ''">specialty = #{specialty},</if>
            <if test="contentTitle != null and contentTitle != ''">content_title = #{contentTitle},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="addTime != null and addTime != ''">add_time = #{addTime},</if>
            <if test="sourceUri != null">source_uri = #{sourceUri},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndSpecialtyById" parameterType="Integer">
        delete from neknd_specialty where id = #{id}
    </delete>

    <delete id="deleteNekndSpecialtyByIds" parameterType="String">
        delete from neknd_specialty where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>