{"doc": " 留言主题Controller\n \n <AUTHOR>\n @date 2024-08-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询留言主题列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndMessageTopic"], "doc": " 导出留言主题列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取留言主题详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 新增留言主题\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 修改留言主题\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除留言主题（逻辑删除）\n"}, {"name": "initiateTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 发起新的留言主题\n"}, {"name": "getTopicMessages", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取主题下的消息列表\n"}, {"name": "markTopicAsRead", "paramTypes": ["java.lang.Long"], "doc": " 标记主题为已读\n"}, {"name": "getUnreadTopicCount", "paramTypes": [], "doc": " 获取用户的未读主题数量\n"}, {"name": "searchTopics", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 搜索主题\n"}], "constructors": []}