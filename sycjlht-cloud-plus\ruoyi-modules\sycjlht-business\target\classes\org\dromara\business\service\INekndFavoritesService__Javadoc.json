{"doc": " 企业人才收藏关系（人才收藏岗位，企业收藏人才）Service接口\n\n <AUTHOR>\n @date 2025-04-14\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": " 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n"}, {"name": "selectNekndFavoritesList", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）列表\n\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）集合\n"}, {"name": "insertNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 新增企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n @return 结果\n"}, {"name": "updateNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 修改企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n @return 结果\n"}, {"name": "deleteNekndFavoritesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param ids 需要删除的企业人才收藏关系（人才收藏岗位，企业收藏人才）主键集合\n @return 结果\n"}, {"name": "deleteNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": " 删除企业人才收藏关系（人才收藏岗位，企业收藏人才）信息\n\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\n @return 结果\n"}, {"name": "toggleFavorite", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 新增收藏和修改收藏\n @return\n"}, {"name": "isFavorite", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 检查是否收藏\n"}, {"name": "getFavoritesCountByUserIdAndType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 获取用户收藏数量（按类型）\n\n @param userId 用户ID\n @param targetType 目标类型：1-岗位，2-人才\n @return 收藏数量\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndFavorites", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询收藏列表\n\n @param nekndFavorites 查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}], "constructors": []}