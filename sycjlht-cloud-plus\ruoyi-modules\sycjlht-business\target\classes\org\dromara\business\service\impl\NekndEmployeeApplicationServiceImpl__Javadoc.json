{"doc": " 公司和学校的员工申请记录Service业务层处理\n\n <AUTHOR>\n @date 2024-09-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "initProvincialNameToIdMap", "paramTypes": [], "doc": " 初始化省份名称到 ID 的映射\n"}, {"name": "selectNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询公司和学校的员工申请记录\n\n @param id 公司和学校的员工申请记录主键\n @return 公司和学校的员工申请记录\n"}, {"name": "selectNekndEmployeeApplicationList", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 查询公司和学校的员工申请记录列表\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 公司和学校的员工申请记录\n"}, {"name": "insertNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 新增公司和学校的员工申请记录\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 结果\n"}, {"name": "updateNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": " 修改公司和学校的员工申请记录\n\n @param nekndEmployeeApplication 公司和学校的员工申请记录\n @return 结果\n"}, {"name": "deleteNekndEmployeeApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除公司和学校的员工申请记录\n\n @param ids 需要删除的公司和学校的员工申请记录主键\n @return 结果\n"}, {"name": "deleteNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除公司和学校的员工申请记录信息\n\n @param id 公司和学校的员工申请记录主键\n @return 结果\n"}, {"name": "validateUserApplication", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 校验用户申请\n\n @param userId 用户ID\n @param companyId 公司ID\n @param reviewStatus 审核状态\n @return 结果\n"}], "constructors": []}