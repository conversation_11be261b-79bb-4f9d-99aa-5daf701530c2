{"doc": " 图片URL转换工具类\n 用于处理单体项目迁移过来的图片路径转换\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertImageUrl", "paramTypes": ["java.lang.String"], "doc": " 转换图片URL\n 新数据已经是完整URL，只需要处理历史数据\n\n @param imageUrl 原始图片URL\n @return 转换后的可访问URL\n"}, {"name": "convertOssId", "paramTypes": ["java.lang.String"], "doc": " 通过OSS ID获取图片URL\n 注意：如果OSS配置为PRIVATE，返回的URL会包含签名参数，有效期120秒\n"}, {"name": "convertOssIds", "paramTypes": ["java.lang.String"], "doc": " 批量处理OSS ID（逗号分隔），返回第一个有效URL\n"}, {"name": "constructDefaultUrl", "paramTypes": ["java.lang.String"], "doc": " 构造默认URL（降级处理）\n"}, {"name": "convertLegacyPath", "paramTypes": ["java.lang.String"], "doc": " 转换历史路径为OSS URL\n"}, {"name": "convertImageUrls", "paramTypes": ["java.lang.String"], "doc": " 批量转换图片URL\n"}, {"name": "isLegacyImagePath", "paramTypes": ["java.lang.String"], "doc": " 检查是否为历史图片路径\n"}, {"name": "get<PERSON>s<PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 获取图片的OSS路径\n"}], "constructors": []}