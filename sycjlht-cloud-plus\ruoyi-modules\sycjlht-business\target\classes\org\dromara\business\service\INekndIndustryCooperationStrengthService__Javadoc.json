{"doc": " 【请填写功能名称】Service接口\n\n <AUTHOR>\n @date 2024-12-25\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryCooperationStrengthByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustryCooperationStrengthList", "paramTypes": ["org.dromara.business.domain.NekndIndustryCooperationStrength"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustryCooperationStrength 【请填写功能名称】\n @return 【请填写功能名称】集合\n"}, {"name": "insertNekndIndustryCooperationStrength", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "org.dromara.business.domain.NekndIndustryCooperationStrength"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustryCooperationStrength 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustryCooperationStrength", "paramTypes": ["org.dromara.business.domain.NekndIndustryCooperationStrength"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustryCooperationStrength 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustryCooperationStrengthByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industryIds 需要删除的【请填写功能名称】主键集合\n @return 结果\n"}, {"name": "deleteNekndIndustryCooperationStrengthByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】信息\n\n @param industryId 【请填写功能名称】主键\n @return 结果\n"}], "constructors": []}