{"doc": " 市域产教融合项目库Mapper接口\n\n <AUTHOR>\n @date 2025-02-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": " 查询市域产教融合项目库\n\n @param id 市域产教融合项目库主键\n @return 市域产教融合项目库\n"}, {"name": "selectNekndIndustryEducationProjectsList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 查询市域产教融合项目库列表\n\n @param nekndIndustryEducationProjects 市域产教融合项目库\n @return 市域产教融合项目库集合\n"}, {"name": "insertNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 新增市域产教融合项目库\n\n @param nekndIndustryEducationProjects 市域产教融合项目库\n @return 结果\n"}, {"name": "updateNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 修改市域产教融合项目库\n\n @param nekndIndustryEducationProjects 市域产教融合项目库\n @return 结果\n"}, {"name": "deleteNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": " 删除市域产教融合项目库\n\n @param id 市域产教融合项目库主键\n @return 结果\n"}, {"name": "deleteNekndIndustryEducationProjectsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除市域产教融合项目库\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}