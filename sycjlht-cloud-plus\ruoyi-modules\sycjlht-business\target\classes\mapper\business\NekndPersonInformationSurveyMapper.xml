<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndPersonInformationSurveyMapper">

    <resultMap type="NekndPersonInformationSurvey" id="NekndPersonInformationSurveyResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="schoolName"    column="school_name"    />
        <result property="schoolCode"    column="school_code"    />
        <result property="studentNo"    column="student_no"    />
        <result property="studentName"    column="student_name"    />
        <result property="speciality"    column="speciality"    />
        <result property="className"    column="class_name"    />
        <result property="birthplaceName"    column="birthplace_name"    />
        <result property="birthplaceCode"    column="birthplace_code"    />
        <result property="formContent"    column="form_content"    />
    </resultMap>

    <sql id="selectNekndPersonInformationSurveyVo">
        select id, del_flag, create_by, create_time, update_by, update_time, school_name, school_code, student_no, student_name, speciality, class_name, birthplace_name, birthplace_code, form_content from neknd_person_information_survey
    </sql>

    <select id="selectNekndPersonInformationSurveyList" parameterType="NekndPersonInformationSurvey" resultMap="NekndPersonInformationSurveyResult">
        <include refid="selectNekndPersonInformationSurveyVo"/>
        <where>
            <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
            <if test="schoolCode != null  and schoolCode != ''"> and school_code = #{schoolCode}</if>
            <if test="studentNo != null  and studentNo != ''"> and student_no = #{studentNo}</if>
            <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
            <if test="speciality != null  and speciality != ''"> and speciality = #{speciality}</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="birthplaceName != null  and birthplaceName != ''"> and birthplace_name like concat('%', #{birthplaceName}, '%')</if>
            <if test="birthplaceCode != null  and birthplaceCode != ''"> and birthplace_code = #{birthplaceCode}</if>
            <if test="formContent != null  and formContent != ''"> and form_content = #{formContent}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectNekndPersonInformationSurveyById" parameterType="Integer" resultMap="NekndPersonInformationSurveyResult">
        <include refid="selectNekndPersonInformationSurveyVo"/>
        where id = #{id}
    </select>

<!--    选择工作时，最想进入的行业-->
    <select id="getItem1Counts" resultType="map">
        SELECT
            itemName,
            COUNT(*) AS itemCount
        FROM (
                 SELECT
                     COALESCE(
                             NULLIF(
                                     JSON_UNQUOTE(
                                             JSON_EXTRACT(
                                                     CASE
                                                         WHEN form_content = '' OR form_content IS NULL THEN '[]'
                                                         ELSE form_content
                                                         END,
                                                     '$.item5'
                                             )
                                     ), ''
                             ),
                             '其他'
                     ) AS itemName
                 FROM
                     neknd_person_information_survey
                 WHERE
                     del_flag = 0
             ) t
        WHERE
            itemName IN ('信息技术行业', '金融服务行业', '传媒行业', '物流行业', '建筑行业', '医药行业', '制造行业', '服务行业', '政府机关', '教育机构', '其他')
        GROUP BY
            itemName
        ORDER BY
            itemCount DESC;
    </select>

    <!--    期望薪资-->
    <select id="getItem2Counts" resultType="map">
        SELECT
            itemName,
            COUNT(*) AS itemCount
        FROM (
                 SELECT
                     COALESCE(
                             NULLIF(
                                     JSON_UNQUOTE(
                                             JSON_EXTRACT(
                                                     CASE
                                                         WHEN form_content = '' OR form_content IS NULL THEN '[]'
                                                         ELSE form_content
                                                         END,
                                                     "$[0].item7"
                                             )
                                     ), ''
                             ),
                             '其他'
                     ) AS itemName
                 FROM
                     neknd_person_information_survey
                 WHERE
                     del_flag = 0
             ) t
        WHERE
            itemName IN ('2001元 - 4000元', '4001元 - 6000元', '6001元 - 8000元', '8001元 - 10000元', '10000元以上')
        GROUP BY
            itemName
        ORDER BY
            itemCount DESC;
    </select>

    <!--    您的择业观念-->
    <select id="getItem3Counts" resultType="map">
        SELECT
            itemName,
            COUNT(*) AS itemCount
        FROM (
                 SELECT
                     COALESCE(
                             NULLIF(
                                     JSON_UNQUOTE(
                                             JSON_EXTRACT(
                                                     CASE
                                                         WHEN form_content = '' OR form_content IS NULL THEN '[]'
                                                         ELSE form_content
                                                         END,
                                                     "$[0].item3"
                                             )
                                     ), ''
                             ),
                             '其他'
                     ) AS itemName
                 FROM
                     neknd_person_information_survey
                 WHERE
                     del_flag = 0
             ) t
        WHERE
            itemName IN ('先就业，后择业', '先择业，后就业', '不就业，继续深造', '自主创业')
        GROUP BY
            itemName
        ORDER BY
            itemCount DESC;
    </select>

    <!--    您对目前工作或就业状态的满意程度-->
    <select id="getItem4Counts" resultType="map">
        SELECT
            itemName,
            COUNT(*) AS itemCount
        FROM (
                 SELECT
                     COALESCE(
                             NULLIF(
                                     JSON_UNQUOTE(
                                             JSON_EXTRACT(
                                                     CASE
                                                         WHEN form_content = '' OR form_content IS NULL THEN '[]'
                                                         ELSE form_content
                                                         END,
                                                     "$[0].item11"
                                             )
                                     ), ''
                             ),
                             '其他'
                     ) AS itemName
                 FROM
                     neknd_person_information_survey
                 WHERE
                     del_flag = 0
             ) t
        WHERE
            itemName IN ('非常满意', '比较满意', '一般', '不满意', '非常不满意')
        GROUP BY
            itemName
        ORDER BY
            itemCount DESC;
    </select>

    <insert id="insertNekndPersonInformationSurvey" parameterType="NekndPersonInformationSurvey" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_person_information_survey
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="schoolName != null">school_name,</if>
            <if test="schoolCode != null">school_code,</if>
            <if test="studentNo != null">student_no,</if>
            <if test="studentName != null">student_name,</if>
            <if test="speciality != null">speciality,</if>
            <if test="className != null">class_name,</if>
            <if test="birthplaceName != null">birthplace_name,</if>
            <if test="birthplaceCode != null">birthplace_code,</if>
            <if test="formContent != null">form_content,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="schoolName != null">#{schoolName},</if>
            <if test="schoolCode != null">#{schoolCode},</if>
            <if test="studentNo != null">#{studentNo},</if>
            <if test="studentName != null">#{studentName},</if>
            <if test="speciality != null">#{speciality},</if>
            <if test="className != null">#{className},</if>
            <if test="birthplaceName != null">#{birthplaceName},</if>
            <if test="birthplaceCode != null">#{birthplaceCode},</if>
            <if test="formContent != null">#{formContent},</if>
         </trim>
    </insert>

    <update id="updateNekndPersonInformationSurvey" parameterType="NekndPersonInformationSurvey">
        update neknd_person_information_survey
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="schoolName != null">school_name = #{schoolName},</if>
            <if test="schoolCode != null">school_code = #{schoolCode},</if>
            <if test="studentNo != null">student_no = #{studentNo},</if>
            <if test="studentName != null">student_name = #{studentName},</if>
            <if test="speciality != null">speciality = #{speciality},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="birthplaceName != null">birthplace_name = #{birthplaceName},</if>
            <if test="birthplaceCode != null">birthplace_code = #{birthplaceCode},</if>
            <if test="formContent != null">form_content = #{formContent},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndPersonInformationSurveyById" parameterType="Integer">
        delete from neknd_person_information_survey where id = #{id}
    </delete>

    <delete id="deleteNekndPersonInformationSurveyByIds" parameterType="String">
        delete from neknd_person_information_survey where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>



    <select id="selectFormY" resultMap="NekndPersonInformationSurveyResult">
        <include refid="selectNekndPersonInformationSurveyVo"/>
        <where>
            <if test="schoolCode != null  and schoolCode != ''">  school_code = #{schoolCode} and</if>
            form_content IS NULL ORDER BY speciality
        </where>
    </select>
    <select id="selectFormN" resultMap="NekndPersonInformationSurveyResult">
        <include refid="selectNekndPersonInformationSurveyVo"/>
        where school_code=#{schoolcode} and form_content is not null ORDER BY speciality
    </select>

    <select id="countY" resultType="integer">
        select count(*) from neknd_person_information_survey  where  form_content is not null;
    </select>
    <select id="countN" resultType="integer">
        select count(*) from neknd_person_information_survey  where  form_content is null;
    </select>

    <select id="statistics1" resultType="integer">
        SELECT COUNT(*) FROM neknd_person_information_survey WHERE JSON_UNQUOTE(JSON_EXTRACT(neknd_person_information_survey.form_content,#{item})) = #{formContent}
    </select>
    <select id="statistics2" resultType="integer">
    SELECT COUNT(*)
    FROM neknd_person_information_survey
    WHERE JSON_CONTAINS(form_content->#{item},
                        ${formContent})
</select>

    <select id="selectSpeciality" resultType="string">
        SELECT speciality FROM neknd_person_information_survey where form_content is null  GROUP BY speciality
    </select>



    <select id="selectFormY1" parameterType="string" resultMap="NekndPersonInformationSurveyResult">
        <include refid="selectNekndPersonInformationSurveyVo"/>
        WHERE speciality=#{speciality} and  form_content IS not NULL
    </select>


    <select id="selectTest" parameterType="string" resultMap="NekndPersonInformationSurveyResult">
        <include refid="selectNekndPersonInformationSurveyVo"/>
        where speciality=#{TYPESTYPE} and form_content is null
    </select>
</mapper>
