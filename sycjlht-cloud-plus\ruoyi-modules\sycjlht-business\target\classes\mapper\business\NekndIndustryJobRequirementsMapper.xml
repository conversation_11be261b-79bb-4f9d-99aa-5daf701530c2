<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryJobRequirementsMapper">
    
    <resultMap type="NekndIndustryJobRequirements" id="NekndIndustryJobRequirementsResult">
        <result property="industryId"    column="industry_id"    />
        <result property="jobName"    column="job_name"    />
        <result property="majorRequirement"    column="major_requirement"    />
        <result property="degreeRequirement"    column="degree_requirement"    />
        <result property="experienceRequirement"    column="experience_requirement"    />
        <result property="demandNumber"    column="demand_number"    />
    </resultMap>

    <sql id="selectNekndIndustryJobRequirementsVo">
        select industry_id, job_name, major_requirement, degree_requirement, experience_requirement, demand_number from neknd_industry_job_requirements
    </sql>

    <select id="selectNekndIndustryJobRequirementsList" parameterType="NekndIndustryJobRequirements" resultMap="NekndIndustryJobRequirementsResult">
        <include refid="selectNekndIndustryJobRequirementsVo"/>
        <where>  
            <if test="jobName != null  and jobName != ''"> and job_name like concat('%', #{jobName}, '%')</if>
            <if test="majorRequirement != null  and majorRequirement != ''"> and major_requirement = #{majorRequirement}</if>
            <if test="degreeRequirement != null  and degreeRequirement != ''"> and degree_requirement = #{degreeRequirement}</if>
            <if test="experienceRequirement != null "> and experience_requirement = #{experienceRequirement}</if>
            <if test="demandNumber != null "> and demand_number = #{demandNumber}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryJobRequirementsByIndustryId" parameterType="Long" resultMap="NekndIndustryJobRequirementsResult">
        <include refid="selectNekndIndustryJobRequirementsVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustryJobRequirementsByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_job_requirements
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustryJobRequirements" parameterType="NekndIndustryJobRequirements" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_job_requirements
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="jobName != null">job_name,</if>
            <if test="majorRequirement != null">major_requirement,</if>
            <if test="degreeRequirement != null">degree_requirement,</if>
            <if test="experienceRequirement != null">experience_requirement,</if>
            <if test="demandNumber != null">demand_number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="jobName != null">#{jobName},</if>
            <if test="majorRequirement != null">#{majorRequirement},</if>
            <if test="degreeRequirement != null">#{degreeRequirement},</if>
            <if test="experienceRequirement != null">#{experienceRequirement},</if>
            <if test="demandNumber != null">#{demandNumber},</if>
        </trim>
        On Duplicate Key Update industry_id = values(industry_id),job_name = values(job_name),major_requirement = values(major_requirement),degree_requirement = values(degree_requirement),experience_requirement = values(experience_requirement),demand_number = values(demand_number)
    </insert>

    <update id="updateNekndIndustryJobRequirements" parameterType="NekndIndustryJobRequirements">
        update neknd_industry_job_requirements
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobName != null">job_name = #{jobName},</if>
            <if test="majorRequirement != null">major_requirement = #{majorRequirement},</if>
            <if test="degreeRequirement != null">degree_requirement = #{degreeRequirement},</if>
            <if test="experienceRequirement != null">experience_requirement = #{experienceRequirement},</if>
            <if test="demandNumber != null">demand_number = #{demandNumber},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryJobRequirementsByIndustryId" parameterType="Long">
        delete from neknd_industry_job_requirements where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryJobRequirementsByIndustryIds" parameterType="String">
        delete from neknd_industry_job_requirements where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>