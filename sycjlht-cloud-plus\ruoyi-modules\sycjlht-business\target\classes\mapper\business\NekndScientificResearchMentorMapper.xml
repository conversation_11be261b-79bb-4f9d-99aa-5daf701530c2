<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndScientificResearchMentorMapper">
    
    <resultMap type="NekndScientificResearchMentor" id="NekndScientificResearchMentorResult">
        <result property="id"    column="id"    />
        <result property="cost"    column="cost"    />
        <result property="scientificResearchId"    column="scientific_research_id"    />
        <result property="mentorId"    column="mentor_id"    />
    </resultMap>

    <sql id="selectNekndScientificResearchMentorVo">
        select id, cost, scientific_research_id, mentor_id from neknd_scientific_research_mentor
    </sql>

    <select id="selectNekndScientificResearchMentorList" parameterType="NekndScientificResearchMentor" resultMap="NekndScientificResearchMentorResult">
        <include refid="selectNekndScientificResearchMentorVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="cost != null "> and cost = #{cost}</if>
            <if test="scientificResearchId != null "> and scientific_research_id = #{scientificResearchId}</if>
            <if test="mentorId != null "> and mentor_id = #{mentorId}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectNekndScientificResearchMentorById" parameterType="Integer" resultMap="NekndScientificResearchMentorResult">
        <include refid="selectNekndScientificResearchMentorVo"/>
        where id = #{id}
    </select>
    <select id="selectResearchMentorByIdAndResearchId" resultType="java.lang.Integer">
        select count(1) from neknd_scientific_research_mentor where mentor_id=#{mentorId} and scientific_research_id=#{researchId}
    </select>

    <insert id="insertNekndScientificResearchMentor" parameterType="NekndScientificResearchMentor" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_scientific_research_mentor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cost != null">cost,</if>
            <if test="scientificResearchId != null">scientific_research_id,</if>
            <if test="mentorId != null">mentor_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cost != null">#{cost},</if>
            <if test="scientificResearchId != null">#{scientificResearchId},</if>
            <if test="mentorId != null">#{mentorId},</if>
         </trim>
    </insert>

    <update id="updateNekndScientificResearchMentor" parameterType="NekndScientificResearchMentor">
        update neknd_scientific_research_mentor
        <trim prefix="SET" suffixOverrides=",">
            <if test="cost != null">cost = #{cost},</if>
            <if test="scientificResearchId != null">scientific_research_id = #{scientificResearchId},</if>
            <if test="mentorId != null">mentor_id = #{mentorId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndScientificResearchMentorById" parameterType="Integer">
        delete from neknd_scientific_research_mentor where id = #{id}
    </delete>

    <delete id="deleteNekndScientificResearchMentorByIds" parameterType="String">
        delete from neknd_scientific_research_mentor where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>