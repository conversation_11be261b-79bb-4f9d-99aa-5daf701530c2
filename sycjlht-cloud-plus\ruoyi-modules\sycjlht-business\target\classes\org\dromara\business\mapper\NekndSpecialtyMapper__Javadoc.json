{"doc": " 专业Mapper接口\n\n <AUTHOR>\n @date 2024-09-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSpecialtyList", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 查询专业列表\n\n @param nekndSpecialty 专业\n @return 专业集合\n"}, {"name": "selectNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": " 查询专业\n\n @param id 专业主键\n @return 专业\n"}, {"name": "insertNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 新增专业\n\n @param nekndSpecialty 专业\n @return 结果\n"}, {"name": "updateNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 修改专业\n\n @param nekndSpecialty 专业\n @return 结果\n"}, {"name": "deleteNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": " 删除专业\n\n @param id 专业主键\n @return 结果\n"}, {"name": "deleteNekndSpecialtyByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除专业\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}