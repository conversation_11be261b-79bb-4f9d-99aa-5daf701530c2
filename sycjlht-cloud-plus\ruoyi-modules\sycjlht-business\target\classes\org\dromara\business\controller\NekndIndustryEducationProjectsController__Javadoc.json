{"doc": " 市域产教融合项目库Controller\n \n <AUTHOR>\n @date 2025-02-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询市域产教融合项目库列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 导出市域产教融合项目库列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入市域产教融合项目库\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取市域产教融合项目库详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 新增市域产教融合项目库\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": " 修改市域产教融合项目库\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除市域产教融合项目库\n"}, {"name": "getList", "paramTypes": [], "doc": " 获取当前部门的项目统计（图表数据）\n"}, {"name": "getSortList", "paramTypes": [], "doc": " 获取当前部门的项目排序列表\n"}, {"name": "getProjectStatisticsByType", "paramTypes": [], "doc": " 按项目类型统计\n"}, {"name": "getProjectStatisticsByStatus", "paramTypes": [], "doc": " 按项目状态统计\n"}, {"name": "getPopularProjects", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取热门项目\n"}], "constructors": []}