<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndSupplyDemandMapper">

    <resultMap type="NekndSupplyDemand" id="NekndSupplyDemandResult">
        <result property="id" column="id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="flag" column="flag"/>
        <result property="address" column="address"/>
        <result property="status" column="status"/>
        <result property="demandType" column="demand_type"/>
        <result property="money" column="money"/>
        <result property="dockingStatus" column="docking_status"/>
        <result property="industry" column="industry"/>
        <result property="projectTime" column="project_time"/>
        <result property="reviewStatus" column="review_status"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
    </resultMap>
    <resultMap  type="com.neknd.system.domain.vo.DockingRecordsVo" id="DockingRecordsVoResult">
        <result property="id" column="id"/>
        <result property="name" column="title"/>
        <result property="content" column="content"/>
        <result property="dockingStatus" column="docking_status"/>
        <result property="serviceProviderId" column="service_provider_id"/>
        <result property="serviceProviderName" column="service_provider_name"/>
    </resultMap>

    <sql id="selectNekndSupplyDemandVo">
        select id,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               dept_id,
               dept_name,
               title,
               content,
               flag,
               address,
               status,
               demand_type,
               industry,
               money,
               docking_status,
               project_time,
               review_status,
               provincial_id,
               provincial_name,
               city_id,
               city_name
        from neknd_supply_demand
    </sql>

    <!--后台-->
    <select id="selectDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="title != null  and title != ''">and title  like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''">and content  like concat('%', #{content}, '%')</if>
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
            <if test="reviewStatus != null  and reviewStatus != ''">and review_status = #{reviewStatus}</if>
        </where>
        order by create_time desc
    </select>

    <!--前台-->
    <select id="selectNekndSupplyDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            review_status = 1 and status = 0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
<!--            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">and title  like concat('%', #{searchValue}, '%')</if>
<!--            <if test="content != null  and content != ''">and content  like concat('%', #{searchValue}, '%')</if>-->
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
        </where>
        order by create_time desc
    </select>

    <!--前台根据项目时间-->
    <select id="selectprojectTimeSortDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            review_status = 1 and status = 0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <!--            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">and title  like concat('%', #{searchValue}, '%')</if>
            <!--            <if test="content != null  and content != ''">and content  like concat('%', #{searchValue}, '%')</if>-->
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
        </where>
        order by project_time desc
    </select>


    <!--前台根据项目时间-->
    <select id="selectprojectTimeASCSortDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            review_status = 1 and status = 0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <!--            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">and title  like concat('%', #{searchValue}, '%')</if>
            <!--            <if test="content != null  and content != ''">and content  like concat('%', #{searchValue}, '%')</if>-->
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
        </where>
        order by project_time asc
    </select>

    <select id="selectNekndSupplyDemandById" parameterType="Integer" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        where id = #{id}
    </select>
    <select id="selectNekndSupplyDemandByrequirementId" resultType="com.neknd.system.domain.NekndSupplyDemand">
        select review_status, docking_status
        from neknd_supply_demand
        where id = #{id}
          and status = 0
    </select>
    <select id="selectDockingRecord" parameterType="Integer" resultMap="DockingRecordsVoResult">
        SELECT a.id as id,
               a.title as title,
               a.content as content,
               b.docking_status as docking_status,
               b.service_provider_id as service_provider_id,
               b.service_provider_name as service_provider_name
        FROM neknd_supply_demand AS a
                 LEFT JOIN neknd_self_recommendation AS b ON a.id = b.requirement_id
        WHERE a.review_status = 1
--           AND a.docking_status = 1
--           AND b.docking_status = 1
          AND a.STATUS = 0
          AND dept_id = #{id}

    </select>



    <select id="selectMoneySortDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            review_status = 1 and status = 0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
<!--            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">and title  like concat('%', #{searchValue}, '%')</if>
<!--            <if test="content != null  and content != ''">and content like concat('%', #{searchValue}, '%')</if>-->
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
        </where>
        ORDER BY
        CASE
        WHEN LOWER(money) = '面议' THEN 999999999 -- 假设一个足够大的数字，使“面议”排在最后
        WHEN money REGEXP '^[0-9]+[Ww]?$' THEN CAST(REPLACE(LOWER(money), 'w', '') AS UNSIGNED) -- 匹配类似“10W”或“100w”的格式，并去除W后转为数字
        ELSE CAST(REPLACE(money, CONCAT(REPLACE(money, '[0-9]', ''), ''), '') AS UNSIGNED) -- 去除所有非数字字符后转为数字
        END DESC,
        money DESC-- 如果数字部分相同，则按原始字符串排序

    </select>

    <select id="selectMoneyASCSortDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            review_status = 1 and status = 0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
<!--            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">and title  like concat('%', #{searchValue}, '%')</if>
<!--            <if test="content != null  and content != ''">and content like concat('%', #{searchValue}, '%')</if>-->
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
        </where>
        ORDER BY
        CASE
        WHEN LOWER(money) = '面议' THEN 999999999 -- 假设一个足够大的数字，使“面议”排在最后
        WHEN money REGEXP '^[0-9]+[Ww]?$' THEN CAST(REPLACE(LOWER(money), 'w', '') AS UNSIGNED) -- 匹配类似“10W”或“100w”的格式，并去除W后转为数字
        ELSE CAST(REPLACE(money, CONCAT(REPLACE(money, '[0-9]', ''), ''), '') AS UNSIGNED) -- 去除所有非数字字符后转为数字
        END ,
        money -- 如果数字部分相同，则按原始字符串排序

    </select>


    <select id="selectCreateTimeSortDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            review_status = 1 and status = 0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
<!--            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">and title  like concat('%', #{searchValue}, '%')</if>
<!--            <if test="content != null  and content != ''">and content like concat('%', #{searchValue}, '%')</if>-->
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <!--<if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>-->
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
        </where>
        ORDER BY
        create_time DESC

    </select>

    <select id="selectCreateTimeASCSortDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            review_status = 1 and status = 0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
<!--            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">and title  like concat('%', #{searchValue}, '%')</if>
<!--            <if test="content != null  and content != ''">and content like concat('%', #{searchValue}, '%')</if>-->
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
        </where>
        ORDER BY
        create_time ASC

    </select>
<!--    查询审核列表-->
    <select id="selectAuditDemandList" parameterType="NekndSupplyDemand" resultMap="NekndSupplyDemandResult">
        <include refid="selectNekndSupplyDemandVo"/>
        <where>
            del_flag=0
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''">and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="title != null  and title != ''">and title  like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''">and content  like concat('%', #{content}, '%')</if>
            <if test="flag != null  and flag != ''">and flag = #{flag}</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>

            <if test="demandType != null  and demandType != ''">and demand_type = #{demandType}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="industry != null  and industry != ''">and industry = #{industry}</if>
            <if test="projectTime != null  and projectTime != ''">and project_time = #{projectTime}</if>
            <if test="dockingStatus != null  and dockingStatus != ''">and docking_status = #{dockingStatus}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''">and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''">and city_name = #{cityName}</if>
            <if test="reviewStatus != null  and reviewStatus != ''">and review_status = #{reviewStatus}</if>
        </where>
        order by create_time desc

    </select>
    <select id="getDemandDockingStatus" resultType="java.lang.String">
        select docking_status from neknd_supply_demand where id = #{id}
    </select>
    <select id="getCountSupplyDemand" resultType="java.lang.Integer">
        select count(1) from neknd_supply_demand where del_flag=0 and review_status = 1 and status=0
    </select>
    <select id="getCountNotDocked" resultType="java.lang.Integer">
        select count(1) from neknd_supply_demand where del_flag=0 and docking_status = 0 and status= 0
    </select>
    <select id="getCountDocked" resultType="java.lang.Integer">
        select count(1) from neknd_supply_demand where del_flag=0 and docking_status = 1 and status= 0
    </select>

    <select id="getTotalMoney" resultType="java.lang.Integer">
        SELECT SUM(money) FROM neknd_self_recommendation where docking_status IN (0, 1) and status= 1
    </select>



    <insert id="insertNekndSupplyDemand" parameterType="NekndSupplyDemand" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_supply_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="flag != null">flag,</if>
            <if test="address != null">address,</if>
            <if test="status != null">status,</if>
            <if test="demandType != null">demand_type,</if>
            <if test="money != null">money,</if>
            <if test="dockingStatus != null">docking_status,</if>
            <if test="industry != null">industry,</if>
            <if test="projectTime != null">project_time,</if>
            <if test="provincialId != null">provincial_id,</if>
            <if test="provincialName != null">provincial_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="flag != null">#{flag},</if>
            <if test="address != null">#{address},</if>
            <if test="status != null">#{status},</if>
            <if test="demandType != null">#{demandType},</if>
            <if test="money != null">#{money},</if>
            <if test="dockingStatus != null">#{dockingStatus},</if>
            <if test="industry != null">#{industry},</if>
            <if test="projectTime != null">#{projectTime},</if>
            <if test="provincialId != null">#{provincialId},</if>
            <if test="provincialName != null">#{provincialName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>

        </trim>
    </insert>

    <update id="updateNekndSupplyDemand" parameterType="NekndSupplyDemand">
        update neknd_supply_demand
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="address != null">address = #{address},</if>
            <if test="status != null">status = #{status},</if>
            <if test="demandType != null">demand_type = #{demandType},</if>
            <if test="money != null">money = #{money},</if>
            <if test="dockingStatus != null">docking_status = #{dockingStatus},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="projectTime != null">project_time = #{projectTime},</if>
            <if test="provincialId != null ">provincial_id = #{provincialId},</if>
            <if test="provincialName != null "> provincial_name = #{provincialName},</if>
            <if test="cityId != null  "> city_id = #{cityId},</if>
            <if test="cityName != null  "> city_name = #{cityName},</if>
            <if test="reviewStatus != null  "> review_status = #{reviewStatus},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="upAuditSupplyDemand" parameterType="Integer">
        update neknd_supply_demand
        <trim prefix="SET" suffixOverrides=",">
            <if test="reviewStatus != null  ">review_status = #{reviewStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndSupplyDemandById" >
        delete
        from neknd_supply_demand
        where id = #{id}
    </delete>

    <delete id="deleteNekndSupplyDemandByIds" parameterType="String">
        delete from neknd_supply_demand where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectIndustryDictValueByDictLabel" resultType="String">
        SELECT dict_value
        FROM sys_dict_data
        WHERE dict_type = "sys_industry"
          AND dict_label= #{dictLabel}
    </select>
</mapper>