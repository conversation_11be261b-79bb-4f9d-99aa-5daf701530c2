{"doc": " AI面试记录Controller\n \n <AUTHOR>\n @date 2024-05-29\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询AI面试记录列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": " 导出AI面试记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取AI面试记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": " 新增AI面试记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": " 修改AI面试记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除AI面试记录\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取用户的面试记录统计信息\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.lang.Integer[]", "java.lang.String"], "doc": " 批量更新面试记录状态\n"}], "constructors": []}