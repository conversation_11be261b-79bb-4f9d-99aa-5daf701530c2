{"doc": " 数据源切换处理\n\n <AUTHOR>\n", "fields": [{"name": "CONTEXT_HOLDER", "doc": " 使用ThreadLocal维护变量，ThreadLocal为每个使用该变量的线程提供独立的变量副本，\n 所以每一个线程都可以独立地改变自己的副本，而不会影响其它线程所对应的副本。\n"}], "enumConstants": [], "methods": [{"name": "setDataSourceType", "paramTypes": ["java.lang.String"], "doc": " 设置数据源的变量\n"}, {"name": "getDataSourceType", "paramTypes": [], "doc": " 获得数据源的变量\n"}, {"name": "clearDataSourceType", "paramTypes": [], "doc": " 清空数据源变量\n"}], "constructors": []}