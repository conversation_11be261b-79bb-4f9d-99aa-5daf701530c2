{"doc": " 企业人才收藏关系（人才收藏岗位，企业收藏人才）Mapper接口\n\n <AUTHOR>\n @date 2025-04-14\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": " 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n"}, {"name": "selectNekndFavoritesList", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 查询企业人才收藏关系（人才收藏岗位，企业收藏人才）列表\n\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n @return 企业人才收藏关系（人才收藏岗位，企业收藏人才）集合\n"}, {"name": "insertNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 新增企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n @return 结果\n"}, {"name": "updateNekndFavorites", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 修改企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param nekndFavorites 企业人才收藏关系（人才收藏岗位，企业收藏人才）\n @return 结果\n"}, {"name": "deleteNekndFavoritesById", "paramTypes": ["java.lang.Integer"], "doc": " 删除企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param id 企业人才收藏关系（人才收藏岗位，企业收藏人才）主键\n @return 结果\n"}, {"name": "deleteNekndFavoritesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除企业人才收藏关系（人才收藏岗位，企业收藏人才）\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}, {"name": "getCountFavorite", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 根据用户id和目标id和目标类型查询收藏记录数量\n @param userId\n @param targetId\n @param targetType\n @return\n"}, {"name": "isFavorite", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 检查用户是否收藏了指定目标\n @param userId 用户ID\n @param targetId 目标ID\n @param targetType 目标类型\n @return 是否收藏\n"}], "constructors": []}