<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndMessageBoxMapper">
    
    <resultMap type="NekndMessageBox" id="NekndMessageBoxResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="recipientUserId"    column="recipient_user_id"    />
        <result property="recipientUserName"    column="recipient_user_name"    />
        <result property="recipientDeptId"    column="recipient_dept_id"    />
        <result property="content"    column="content"    />
        <result property="avatar"    column="avatar"    />
        <result property="title"    column="title"    />
        <result property="readStatus"    column="read_status"    />
        <result property="senderUserId"    column="sender_user_id"    />
        <result property="senderUserName"    column="sender_user_name"    />
        <result property="senderDeptId"    column="sender_dept_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="recipientAvatar"    column="recipient_avatar"    />
    </resultMap>

    <sql id="selectNekndMessageBoxVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, status, recipient_user_id, recipient_user_name, recipient_dept_id, content, avatar, title, read_status, sender_user_id, sender_user_name, sender_dept_id,parent_id,recipient_avatar from neknd_message_box
    </sql>

    <select id="selectNekndMessageBoxList" parameterType="NekndMessageBox" resultMap="NekndMessageBoxResult">
        <include refid="selectNekndMessageBoxVo"/>
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="recipientUserId != null "> and recipient_user_id = #{recipientUserId}</if>
            <if test="recipientUserName != null  and recipientUserName != ''"> and recipient_user_name like concat('%', #{recipientUserName}, '%')</if>
            <if test="recipientDeptId != null "> and recipient_dept_id = #{recipientDeptId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')  </if>
            <if test="readStatus != null  and readStatus != ''"> and read_status = #{readStatus}</if>
            <if test="senderUserId != null "> and sender_user_id = #{senderUserId}</if>
            <if test="senderUserName != null  and senderUserName != ''"> and sender_user_name like concat('%', #{senderUserName}, '%')</if>
            <if test="senderDeptId != null "> and sender_dept_id = #{senderDeptId}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
        order by create_time asc
    </select>
    
    <select id="selectNekndMessageBoxById" parameterType="Integer" resultMap="NekndMessageBoxResult">
        <include refid="selectNekndMessageBoxVo"/>
        where id = #{id}
    </select>
    <select id="getCountUnReadCStatus" resultType="java.lang.Integer">
        select count(1) from neknd_message_box where recipient_user_id = #{userId} and read_status = 0
        <if test="topicId != null "> and parent_id = #{topicId}</if>
    </select>
    <select id="selectAdminMessageBoxList" parameterType="NekndMessageBox" resultMap="NekndMessageBoxResult">
        <include refid="selectNekndMessageBoxVo"/>
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="recipientUserId != null "> and recipient_user_id = #{recipientUserId}</if>
            <if test="recipientUserName != null  and recipientUserName != ''"> and recipient_user_name like concat('%', #{recipientUserName}, '%')</if>
            <if test="recipientDeptId != null "> and recipient_dept_id = #{recipientDeptId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')  </if>
            <if test="readStatus != null  and readStatus != ''"> and read_status = #{readStatus}</if>
            <if test="senderUserId != null "> and sender_user_id = #{senderUserId}</if>
            <if test="senderUserName != null  and senderUserName != ''"> and sender_user_name like concat('%', #{senderUserName}, '%')</if>
            <if test="senderDeptId != null "> and sender_dept_id = #{senderDeptId}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertNekndMessageBox" parameterType="NekndMessageBox" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_message_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="recipientUserId != null">recipient_user_id,</if>
            <if test="recipientUserName != null">recipient_user_name,</if>
            <if test="recipientDeptId != null">recipient_dept_id,</if>
            <if test="content != null">content,</if>
            <if test="avatar != null">avatar,</if>
            <if test="title != null">title,</if>
            <if test="readStatus != null">read_status,</if>
            <if test="senderUserId != null">sender_user_id,</if>
            <if test="senderUserName != null">sender_user_name,</if>
            <if test="senderDeptId != null">sender_dept_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="recipientAvatar != null">recipient_avatar,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="recipientUserId != null">#{recipientUserId},</if>
            <if test="recipientUserName != null">#{recipientUserName},</if>
            <if test="recipientDeptId != null">#{recipientDeptId},</if>
            <if test="content != null">#{content},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="title != null">#{title},</if>
            <if test="readStatus != null">#{readStatus},</if>
            <if test="senderUserId != null">#{senderUserId},</if>
            <if test="senderUserName != null">#{senderUserName},</if>
            <if test="senderDeptId != null">#{senderDeptId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="recipientAvatar != null">#{recipientAvatar},</if>
         </trim>
    </insert>

    <update id="updateNekndMessageBox" parameterType="NekndMessageBox">
        update neknd_message_box
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="recipientUserId != null">recipient_user_id = #{recipientUserId},</if>
            <if test="recipientUserName != null">recipient_user_name = #{recipientUserName},</if>
            <if test="recipientDeptId != null">recipient_dept_id = #{recipientDeptId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="title != null">title = #{title},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="senderUserId != null">sender_user_id = #{senderUserId},</if>
            <if test="senderUserName != null">sender_user_name = #{senderUserName},</if>
            <if test="senderDeptId != null">sender_dept_id = #{senderDeptId},</if>
            <if test="recipientAvatar != null">recipient_avatar = #{recipientAvatar},</if>
        </trim>
        where id = #{id}
    </update>
<!--    <update id="updateReadStatusByParentId">-->
<!--        update neknd_message_box set-->
<!--        read_status = 1 where parent_id = #{parentId} and recipient_user_id = #{recipientUserId}-->
<!--    </update>-->
    <update id="updateReadStatusByParentId">
        update neknd_message_box set
            read_status = 1 where parent_id = #{parentId}
    </update>
    <update id="updateSenderReadStatusByParentId">
        update neknd_message_box set
        sender_read_status = 1 where parent_id = #{parentId} and sender_user_id = #{senderUserId}
    </update>
    <!--    <update id="updateReadStatusByIds" parameterType="String">-->
<!--        update neknd_message_box set-->
<!--        read_status = 1-->
<!--        where id in-->
<!--        <foreach item="id" collection="array" open="(" separator="," close=")">#{id}</foreach>-->
<!--    </update>-->

    <delete id="deleteNekndMessageBoxById" parameterType="Integer">
        delete from neknd_message_box where id = #{id}
    </delete>

    <delete id="deleteNekndMessageBoxByIds" parameterType="String">
        delete from neknd_message_box where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteNekndMessageBoxByParentId" parameterType="Integer">
        delete from neknd_message_box where parent_Id = #{id}
    </delete>

</mapper>