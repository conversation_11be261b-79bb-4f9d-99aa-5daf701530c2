{"doc": " 专业信息Controller\n \n <AUTHOR>\n @date 2024-11-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSpecialty", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询专业信息列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSpecialty"], "doc": " 导出专业信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取专业信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 新增专业信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": " 修改专业信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除专业信息\n"}, {"name": "getByType", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按专业类型查询\n"}, {"name": "search", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 搜索专业信息\n"}, {"name": "getPopular", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取热门专业\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取专业统计信息\n"}], "constructors": []}