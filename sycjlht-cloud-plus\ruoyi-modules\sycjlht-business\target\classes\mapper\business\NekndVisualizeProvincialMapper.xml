<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndVisualizeProvincialMapper">
    <resultMap id="participateData" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="unitType" column="unit_type"/>
        <result property="unitTypeCount" column="unit_type_count"/>
    </resultMap>
    <select id="getParticipateData" parameterType="String" resultMap="participateData">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.unit_type,
            count(nu.unit_type) as unit_type_count
        from
            neknd_unit_park nup
        inner join
            neknd_unit nu on
            nup.uid = nu.id
        where
            nu.provincial_id = #{pid}
        group by
            nu.unit_type;
    </select>

    <resultMap id="provincialDynamicMonitoring" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="governmentCoordination" column="government_coordination"/>
        <result property="industryMatch" column="industry_match"/>
        <result property="talentTrainingQuality" column="talent_training_quality"/>
    </resultMap>
    <select id="getProvincialDynamicMonitoring" parameterType="String" resultMap="provincialDynamicMonitoring">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            round(avg(nup.government_coordination), 2)  as government_coordination,
            round(avg(nup.industry_match), 2)  as industry_match,
            round(avg(nup.talent_training_quality), 2) as talent_training_quality
        from
            neknd_unit_park nup
        inner join
            neknd_unit nu on
            nup.uid = nu.id
        where
            nu.provincial_id = #{pid}
        group by
            nu.city_id;
    </select>

    <resultMap id="provincialParkDistribution" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="parkCount" column="park_count"/>
        <result property="parkIndustrialOutput" column="park_industrial_output"/>
    </resultMap>
    <select id="getProvincialParkDistribution" parameterType="String" resultMap="provincialParkDistribution">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            count(nup.park_type) as park_count,
            json_arrayagg(nup.park_industrial_output) as park_industrial_output
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.provincial_id = #{pid}
        group by
            nu.city_id;
    </select>

    <resultMap id="provincialParkDistributionProjectCount" type="hashmap">
        <result property="provincialId" column="provincial_id" />
        <result property="provincialName" column="provincial_name" />
        <result property="cityId" column="city_id" />
        <result property="cityName" column="city_name" />
        <result property="projectCount" column="project_count" />
    </resultMap>
    <select id="getProvincialParkDistributionProjectCount" parameterType="String" resultMap="provincialParkDistributionProjectCount">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            count(niep.belonging_park) as project_count
        from
            neknd_unit nu
        inner join
            neknd_industry_education_projects niep on
            nu.id = niep.belonging_park
        where
            nu.provincial_id = #{pid}
        group by
            nu.city_id;
    </select>

    <resultMap id="provincialPolicyLanding" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="utilizationFund" column="utilization_fund"/>
        <result property="staffEducationRatio" column="staff_education_ratio"/>
    </resultMap>
    <select id="getProvincialPolicyLanding" parameterType="String" resultMap="provincialPolicyLanding">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            round(avg(nup.utilization_fund), 2) as utilization_fund,
            round(avg(nup.staff_education_ratio), 2) as staff_education_ratio
        from
            neknd_unit_park nup
        inner join
            neknd_unit nu on
            nup.uid = nu.id
        where
            nu.provincial_id = #{pid}
        group by
            nu.city_id;
    </select>

    <resultMap id="keyLeadingEnterprise" type="hashmap">
        <id property="companyName" column="company_name"/>
        <result property="companyRanking" column="company_ranking"/>
    </resultMap>
    <select id="getKeyLeadingEnterprise" parameterType="String" resultMap="keyLeadingEnterprise">
        select
            nc.company_name,
            nc.company_ranking
        from
            neknd_company nc
        inner join
            neknd_unit nu on
            nc.belonging_park = nu.id
        where
            nc.status = '0' and nu.provincial_id = #{pid}
        order by
            nc.company_ranking asc;
    </select>

    <resultMap id="provincialNationalLevelSchool" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="companyType" column="company_type"/>
        <result property="companyTypeCount" column="company_type_count"/>
    </resultMap>
    <select id="getProvincialNationalLevelSchool" parameterType="String" resultMap="provincialNationalLevelSchool">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            nc.company_type,
            count(nc.company_type) as company_type_count
        from
            neknd_unit nu
        inner join
            neknd_company nc on
            nu.id = nc.belonging_park
        where
            nc.status = '1' and nu.provincial_id = #{pid}
        group by
            nu.city_id, nc.company_type;
    </select>

    <resultMap id="provincialTalentTrainingConversionRate" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="vocationalToHigherRate" column="vocational_to_higher_rate"/>
        <result property="higherToUndergraduateRate" column="higher_to_undergraduate_rate"/>
    </resultMap>
    <select id="getProvincialTalentTrainingConversionRate" parameterType="String" resultMap="provincialTalentTrainingConversionRate">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            round(avg(nup.vocational_to_higher_rate), 2) as vocational_to_higher_rate,
            round(avg(nup.higher_to_undergraduate_rate), 2) as higher_to_undergraduate_rate
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.provincial_id = #{pid}
        group by
            nu.city_id;
    </select>

    <resultMap id="provincialSkillCertificate" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="nationalCertificateCount" column="national_certificate_count"/>
        <result property="provincialCertificateCount" column="provincial_certificate_count"/>
    </resultMap>
    <select id="getProvincialSkillCertificate" parameterType="String" resultMap="provincialSkillCertificate">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            sum(niep.national_certificate_count) as national_certificate_count,
            sum(niep.provincial_certificate_count) as provincial_certificate_count
        from
            neknd_unit nu
        inner join
            neknd_industry_education_projects niep on
            nu.id = niep.belonging_park
        where
            nu.provincial_id = #{pid}
        group by
            nu.city_id;
    </select>

    <resultMap id="provincialProfessionalSkillCertificate" type="hashmap">
        <result property="provincialId" column="provincial_id" />
        <result property="provincialName" column="provincial_name" />
        <result property="cityId" column="city_id" />
        <result property="cityName" column="city_name" />
        <result property="dominantIndustryCertCount" column="dominant_industry_cert_count" />
    </resultMap>
    <select id="getProvincialProfessionalSkillCertificate" parameterType="String" resultMap="provincialProfessionalSkillCertificate">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            json_arrayagg(nup.dominant_industry_cert_count) as dominant_industry_cert_count
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.provincial_id = #{pid}
        group by
            nu.city_id;
    </select>

    <resultMap id="keyHelpList" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="governmentCoordination" column="government_coordination"/>
        <result property="industryMatch" column="industry_match"/>
        <result property="talentTrainingQuality" column="talent_training_quality"/>
        <result property="comprehensiveScore" column="comprehensive_score"/>
    </resultMap>
    <select id="getKeyHelpList" parameterType="String" resultMap="keyHelpList">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            round(avg(nup.government_coordination), 2)  as government_coordination,
            round(avg(nup.industry_match), 2)  as industry_match,
            round(avg(nup.talent_training_quality), 2) as talent_training_quality,
            round((government_coordination/100*3 + industry_match/100*3 + talent_training_quality/100*4), 2) as comprehensive_score
        from
            neknd_unit_park nup
        inner join
            neknd_unit nu on
            nup.uid = nu.id
        where
            nu.provincial_id = #{pid} and (industry_match &lt; 50 or (government_coordination/100*3 + industry_match/100*3 + talent_training_quality/100*4) &lt; 4)
        group by
            nu.city_id;
    </select>
</mapper>