{"doc": " 脱敏类型\n\n <AUTHOR>\n", "fields": [], "enumConstants": [{"name": "USERNAME", "doc": " 姓名，第2位星号替换\n"}, {"name": "PASSWORD", "doc": " 密码，全部字符都用*代替\n"}, {"name": "ID_CARD", "doc": " 身份证，中间10位星号替换\n"}, {"name": "PHONE", "doc": " 手机号，中间4位星号替换\n"}, {"name": "EMAIL", "doc": " 电子邮箱，仅显示第一个字母和@后面的地址显示，其他星号替换\n"}, {"name": "BANK_CARD", "doc": " 银行卡号，保留最后4位，其他星号替换\n"}, {"name": "CAR_LICENSE", "doc": " 车牌号码，包含普通车辆、新能源车辆\n"}], "methods": [], "constructors": []}