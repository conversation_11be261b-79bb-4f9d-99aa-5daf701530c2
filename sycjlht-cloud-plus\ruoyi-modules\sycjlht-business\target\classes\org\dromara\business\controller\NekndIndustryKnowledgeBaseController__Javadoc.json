{"doc": " 行业知识库管理Controller\n \n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询行业知识库管理列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户查询行业知识库列表（不显示文件链接）\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": " 导出行业知识库管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取行业知识库详细信息（带权限检查）\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": " 新增行业知识库管理\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndIndustryKnowledgeBase"], "doc": " 修改行业知识库管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除行业知识库管理\n"}, {"name": "checkAccess", "paramTypes": ["java.lang.Integer"], "doc": " 检查当前用户对指定知识库的访问权限\n"}, {"name": "getByCategory", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按分类查询知识库\n"}, {"name": "getPopular", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取热门知识库\n"}], "constructors": []}