{"doc": " Bean 工具类\n\n <AUTHOR>\n", "fields": [{"name": "BEAN_METHOD_PROP_INDEX", "doc": "Bean方法名中属性名开始的下标 "}, {"name": "GET_PATTERN", "doc": " 匹配getter方法的正则表达式 "}, {"name": "SET_PATTERN", "doc": " 匹配setter方法的正则表达式 "}], "enumConstants": [], "methods": [{"name": "copyBeanProp", "paramTypes": ["java.lang.Object", "java.lang.Object"], "doc": " Bean属性复制工具方法。\n\n @param dest 目标对象\n @param src 源对象\n"}, {"name": "getSetterMethods", "paramTypes": ["java.lang.Object"], "doc": " 获取对象的setter方法。\n\n @param obj 对象\n @return 对象的setter方法列表\n"}, {"name": "getGetterMethods", "paramTypes": ["java.lang.Object"], "doc": " 获取对象的getter方法。\n\n @param obj 对象\n @return 对象的getter方法列表\n"}, {"name": "isMethodPropEquals", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 检查Bean方法名中的属性名是否相等。<br>\n 如getName()和setName()属性名一样，getName()和setAge()属性名不一样。\n\n @param m1 方法名1\n @param m2 方法名2\n @return 属性名一样返回true，否则返回false\n"}, {"name": "copyProperties", "paramTypes": ["java.util.Map", "java.lang.Object"], "doc": " 将 Map 数据拷贝到 Java Bean 中，并支持驼峰命名与下划线命名自动转换\n\n @param map 数据源（Map）\n @param bean 目标对象（Java Bean）\n @throws BeansException 如果拷贝失败\n"}, {"name": "copyPropertiesWithUnderline", "paramTypes": ["java.util.Map", "java.lang.Object"], "doc": " 支持驼峰转下划线的 Map → Bean 拷贝\n @param map 数据源\n @param bean 目标对象\n @throws BeansException\n"}, {"name": "toCamelCase", "paramTypes": ["java.lang.String"], "doc": " 下划线命名转驼峰命名\n"}, {"name": "toUnderlineName", "paramTypes": ["java.lang.String"], "doc": " 驼峰命名转下划线命名\n"}], "constructors": []}