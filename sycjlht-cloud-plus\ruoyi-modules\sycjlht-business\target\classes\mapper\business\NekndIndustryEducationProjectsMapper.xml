<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryEducationProjectsMapper">
    
    <resultMap type="NekndIndustryEducationProjects" id="NekndIndustryEducationProjectsResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="title"    column="title"    />
        <result property="contentTitle"    column="content_title"    />
        <result property="content"    column="content"    />
        <result property="economicBenefits"    column="economic_benefits"    />
        <result property="socialBenefits"    column="social_benefits"    />
        <result property="cooperationPeriod"    column="cooperation_period"    />
        <result property="timeNode"    column="time_node"    />
        <result property="collegeName"    column="college_name"    />
        <result property="schoolDeptId"    column="school_dept_id"    />
        <result property="cooperationType"    column="cooperation_type"    />
        <result property="collaborator"    column="collaborator"    />
        <result property="professionalTypes"    column="professional_types"    />
        <result property="cooperativeEnterprisesNumber"    column="cooperative_enterprises_number"    />
        <result property="professionalGroupIndustryName"    column="professional_group_industry_name"    />
        <result property="cooperationAboveScaleNumber"    column="cooperation_above_scale_number"    />
        <result property="isCooperationAboveScale"    column="is_cooperation_above_scale"    />
        <result property="isRunningProject"    column="is_running_project"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="classNumber"    column="class_number"    />
        <result property="studentNumber"    column="student_number"    />
        <result property="orderClassName"    column="order_class_name"    />
        <result property="fieldEngineerProjectLevel"    column="field_engineer_project_level"    />
        <result property="isPhysicalOperation"    column="is_physical_operation"    />
        <result property="communityName"    column="community_name"    />
        <result property="frontLinePostsNumber"    column="front_line_posts_number"    />
        <result property="partTimeClassesNumber"    column="part_time_classes_number"    />
        <result property="taskVolume"    column="task_volume"    />
        <result property="acceptingInternStudent"    column="accepting_intern_student"    />
        <result property="acceptingRecentGraduates"    column="accepting_recent_graduates"    />
        <result property="provideAnnualEmployees"    column="provide_annual_employees"    />
        <result property="coDevelopCoursesName"    column="co_develop_courses_name"    />
        <result property="coDevelopCoursesNumber"    column="co_develop_courses_number"    />
        <result property="commonTextbookName"    column="common_textbook_name"    />
        <result property="commonTextbookNumber"    column="common_textbook_number"    />
        <result property="trainingBaseName"    column="training_base_name"    />
        <result property="trainingBaseNumber"    column="training_base_number"    />
        <result property="practiceCenterName"    column="practice_center_name"    />
        <result property="practiceCenterNumber"    column="practice_center_number"    />
        <result property="annualContractIncome"    column="annual_contract_income"    />
        <result property="jointMajorCount"    column="joint_major_count"    />
        <result property="featuredMajorCount"    column="featured_major_count"    />
        <result property="trainingWorkstationCount"    column="training_workstation_count"    />
        <result property="workstationUtilizationRate"    column="workstation_utilization_rate"    />
        <result property="nationalCertificateCount"    column="national_certificate_count"    />
        <result property="provincialCertificateCount"    column="provincial_certificate_count"    />
        <result property="inventionPatentCount"    column="invention_patent_count"    />
        <result property="utilityPatentCount"    column="utility_patent_count"    />
        <result property="masterStudioCount"    column="master_studio_count"    />
        <result property="professionalGroupMatching"    column="professional_group_matching"    />
        <result property="integratedInvestment"    column="integrated_investment"    />
        <result property="belongingPark"    column="belonging_park"    />
        <result property="currentYearDonationValue"    column="current_year_donation_value"    />
        <result property="currentYearPlannedDonationValue"    column="current_year_planned_donation_value"    />
        <result property="currentYearScholarshipTotal"    column="current_year_scholarship_total"    />
        <result property="pastGraduatesCount"    column="past_graduates_count"    />
        <result property="currentGraduatesCount"    column="current_graduates_count"    />
        <result property="courseFullName"    column="course_full_name"    />
        <result property="textbookFullName"    column="textbook_full_name"    />
        <result property="skillCompetitionName"    column="skill_competition_name"    />
        <result property="industryCollabProjectCount"    column="industry_collab_project_count"    />
        <result property="trainingBaseCollaboration"    column="training_base_collaboration"    />
        <result property="orderTrainedStudents"    column="order_trained_students"    />
        <result property="apprenticeshipStudents"    column="apprenticeship_students"    />
        <result property="designPatents"    column="design_patents"    />
        <result property="softwareCopyrights"    column="software_copyrights"    />
        <result property="verticalProjectsCount"    column="vertical_projects_count"    />
        <result property="dataStatus"    column="data_status"    />
    </resultMap>

    <sql id="selectNekndIndustryEducationProjectsVo">
        select id, cover_uri, title, content_title, content, economic_benefits, social_benefits, cooperation_period, time_node, college_name, school_dept_id, cooperation_type, collaborator, professional_types, cooperative_enterprises_number, professional_group_industry_name, cooperation_above_scale_number, is_cooperation_above_scale, is_running_project, del_flag, create_time, update_time, class_number, student_number, order_class_name, field_engineer_project_level, is_physical_operation, community_name, front_line_posts_number, part_time_classes_number, task_volume, accepting_intern_student, accepting_recent_graduates, provide_annual_employees, co_develop_courses_name, co_develop_courses_number, common_textbook_name, common_textbook_number, training_base_name, training_base_number, practice_center_name, practice_center_number, annual_contract_income, joint_major_count, featured_major_count, training_workstation_count, workstation_utilization_rate, national_certificate_count, provincial_certificate_count, invention_patent_count, utility_patent_count, master_studio_count, professional_group_matching, integrated_investment,belonging_park, current_year_donation_value, current_year_planned_donation_value, current_year_scholarship_total, past_graduates_count, current_graduates_count, course_full_name, textbook_full_name, skill_competition_name, industry_collab_project_count, training_base_collaboration, order_trained_students, apprenticeship_students, design_patents, software_copyrights, vertical_projects_count , data_status from neknd_industry_education_projects
    </sql>

    <select id="selectNekndIndustryEducationProjectsList" parameterType="NekndIndustryEducationProjects" resultMap="NekndIndustryEducationProjectsResult">
        <include refid="selectNekndIndustryEducationProjectsVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="params.beginTimeNode != null and params.beginTimeNode != '' and params.endTimeNode != null and params.endTimeNode != ''"> and time_node between #{params.beginTimeNode} and #{params.endTimeNode}</if>
            <if test="collegeName != null  and collegeName != ''"> and college_name like concat('%', #{collegeName}, '%')</if>
            <if test="cooperationType != null  and cooperationType != ''"> and cooperation_type = #{cooperationType}</if>
            <if test="professionalTypes != null  and professionalTypes != ''"> and professional_types = #{professionalTypes}</if>
            <if test="professionalGroupIndustryName != null  and professionalGroupIndustryName != ''"> and professional_group_industry_name like concat('%', #{professionalGroupIndustryName}, '%')</if>
            <if test="isCooperationAboveScale != null  and isCooperationAboveScale != ''"> and is_cooperation_above_scale = #{isCooperationAboveScale}</if>
            <if test="isRunningProject != null  and isRunningProject != ''"> and is_running_project = #{isRunningProject}</if>
            <if test="orderClassName != null  and orderClassName != ''"> and order_class_name like concat('%', #{orderClassName}, '%')</if>
            <if test="fieldEngineerProjectLevel != null  and fieldEngineerProjectLevel != ''"> and field_engineer_project_level = #{fieldEngineerProjectLevel}</if>
            <if test="isPhysicalOperation != null  and isPhysicalOperation != ''"> and is_physical_operation = #{isPhysicalOperation}</if>
            <if test="communityName != null  and communityName != ''"> and community_name like concat('%', #{communityName}, '%')</if>
            <if test="coDevelopCoursesName != null  and coDevelopCoursesName != ''"> and co_develop_courses_name like concat('%', #{coDevelopCoursesName}, '%')</if>
            <if test="commonTextbookName != null  and commonTextbookName != ''"> and common_textbook_name like concat('%', #{commonTextbookName}, '%')</if>
            <if test="trainingBaseName != null  and trainingBaseName != ''"> and training_base_name like concat('%', #{trainingBaseName}, '%')</if>
            <if test="practiceCenterName != null  and practiceCenterName != ''"> and practice_center_name like concat('%', #{practiceCenterName}, '%')</if>
            <if test="belongingPark != null  and belongingPark != ''"> and belonging_park = #{belongingPark}</if>
            <if test="currentYearDonationValue != null  and currentYearDonationValue != ''"> and current_year_donation_value = #{currentYearDonationValue}</if>
            <if test="currentYearPlannedDonationValue != null  and currentYearPlannedDonationValue != ''"> and current_year_planned_donation_value = #{currentYearPlannedDonationValue}</if>
            <if test="currentYearScholarshipTotal != null  and currentYearScholarshipTotal != ''"> and current_year_scholarship_total = #{currentYearScholarshipTotal}</if>
            <if test="pastGraduatesCount != null  and pastGraduatesCount != ''"> and past_graduates_count = #{pastGraduatesCount}</if>
            <if test="currentGraduatesCount != null  and currentGraduatesCount != ''"> and current_graduates_count = #{currentGraduatesCount}</if>
            <if test="courseFullName != null  and courseFullName != ''"> and course_full_name like concat('%', #{courseFullName}, '%')</if>
            <if test="textbookFullName != null  and textbookFullName != ''"> and textbook_full_name like concat('%', #{textbookFullName}, '%')</if>
            <if test="skillCompetitionName != null  and skillCompetitionName != ''"> and skill_competition_name like concat('%', #{skillCompetitionName}, '%')</if>
            <if test="industryCollabProjectCount != null  and industryCollabProjectCount != ''"> and industry_collab_project_count = #{industryCollabProjectCount}</if>
            <if test="trainingBaseCollaboration != null  and trainingBaseCollaboration != ''"> and training_base_collaboration = #{trainingBaseCollaboration}</if>
            <if test="orderTrainedStudents != null  and orderTrainedStudents != ''"> and order_trained_students = #{orderTrainedStudents}</if>
            <if test="apprenticeshipStudents != null  and apprenticeshipStudents != ''"> and apprenticeship_students = #{apprenticeshipStudents}</if>
            <if test="designPatents != null  and designPatents != ''"> and design_patents = #{designPatents}</if>
            <if test="softwareCopyrights != null  and softwareCopyrights != ''"> and software_copyrights = #{softwareCopyrights}</if>
            <if test="verticalProjectsCount != null  and verticalProjectsCount != ''"> and vertical_projects_count = #{verticalProjectsCount}</if>
            <if test="dataStatus != null  and dataStatus != ''"> and data_status = #{dataStatus}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryEducationProjectsById" parameterType="Integer" resultMap="NekndIndustryEducationProjectsResult">
        <include refid="selectNekndIndustryEducationProjectsVo"/>
        where id = #{id}
    </select>

    <resultMap id="conservationMap" type="java.util.HashMap">
        <result property="Name" column="Name"/>
        <result property="count" column="count" />
        <result property="collegeName" column="collegeName" />
        <result property="deptId" column="deptId" />
    </resultMap>
    <select id="selectResourcesConservationListBydeptId" resultMap="conservationMap">
        SELECT
            CASE
                WHEN college_name = 1 THEN '商学院'
                WHEN college_name = 2 THEN '工学院'
                WHEN college_name = 3 THEN '会计学院'
                WHEN college_name = 4 THEN '建筑学院'
                WHEN college_name = 5 THEN '健康管理学院'
                WHEN college_name = 6 THEN '人文艺术学院'
                WHEN college_name = 7 THEN '生物工程学院'
                WHEN college_name = 8 THEN '信息工程学院'
                WHEN college_name = 9 THEN '其他'
                END AS Name,
            COUNT(college_name) as count,
            college_name as collegeName,
            school_dept_id as deptId
        FROM neknd_industry_education_projects
        WHERE college_name !="" and college_name is not NULL and del_flag =0
          and school_dept_id = #{schoolDeptId}
        GROUP BY college_name
        order by count desc;
    </select>

    <resultMap id="integratedInvestmentMap" type="hashmap">
        <result property="unitName" column="unit_name" />
        <result property="totalInvestment" column="total_investment" />
    </resultMap>
    <select id="getIntegratedInvestment" resultMap="integratedInvestmentMap">
        SELECT
            u.unit_name,
            SUM(p.integrated_investment) AS total_investment
        FROM
            neknd_industry_education_projects p
                LEFT JOIN neknd_unit u on p.belonging_park = u.id
        where p.belonging_park is not null
        GROUP BY
            belonging_park
    </select>

    <resultMap id="indicatorsProjectDataMap" type="hashmap">
        <result property="parkId" column="park_id"/>
        <result property="unitName" column="unit_name"/>
        <result property="professionalGroupMatching" column="professional_group_matching"/>
        <result property="jointMajorCount" column="joint_major_count"/>
        <result property="featuredMajorCount" column="featured_major_count"/>
        <result property="annualContractIncome" column="annual_contract_income"/>
        <result property="frontLinePostsNumber" column="front_line_posts_number"/>
        <result property="trainingWorkstationCount" column="training_workstation_count"/>
        <result property="workstationUtilizationRate" column="workstation_utilization_rate"/>
        <result property="nationalCertificateCount" column="national_certificate_count"/>
        <result property="provincialCertificateCount" column="provincial_certificate_count"/>
        <result property="coDevelopCoursesCount" column="co_develop_courses_count"/>
        <result property="inventionPatentCount" column="invention_patent_count"/>
        <result property="utilityPatentCount" column="utility_patent_count"/>
        <result property="annualEmployeesCount" column="annual_employees_count"/>
        <result property="masterStudioCount" column="master_studio_count"/>
    </resultMap>
    <select id="getIndicatorsProjectData" resultMap="indicatorsProjectDataMap">
        SELECT
            i.belonging_park AS park_id,
            u.unit_name,
            ROUND(AVG(i.professional_group_matching), 2) AS professional_group_matching,
            SUM(i.joint_major_count) AS joint_major_count,
            SUM(i.featured_major_count) AS featured_major_count,
            SUM(i.annual_contract_income) AS annual_contract_income,
            SUM(i.front_line_posts_number) AS front_line_posts_number,
            SUM(i.training_workstation_count) AS training_workstation_count,
            ROUND(AVG(i.workstation_utilization_rate), 2) AS workstation_utilization_rate,
            SUM(i.national_certificate_count) AS national_certificate_count,
            SUM(i.provincial_certificate_count) AS provincial_certificate_count,
            SUM(i.co_develop_courses_number) AS co_develop_courses_count,
            SUM(i.invention_patent_count) AS invention_patent_count,
            SUM(i.utility_patent_count) AS utility_patent_count,
            SUM(i.provide_annual_employees) AS annual_employees_count,
            SUM(i.master_studio_count) AS master_studio_count
        FROM neknd_industry_education_projects i
                 LEFT JOIN neknd_unit u ON i.belonging_park = u.id
        WHERE i.belonging_park IS NOT NULL
        GROUP BY i.belonging_park, u.unit_name;
    </select>

    <insert id="insertNekndIndustryEducationProjects" parameterType="NekndIndustryEducationProjects" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_industry_education_projects
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">cover_uri,</if>
            <if test="title != null">title,</if>
            <if test="contentTitle != null">content_title,</if>
            <if test="content != null">content,</if>
            <if test="economicBenefits != null">economic_benefits,</if>
            <if test="socialBenefits != null">social_benefits,</if>
            <if test="cooperationPeriod != null">cooperation_period,</if>
            <if test="timeNode != null">time_node,</if>
            <if test="collegeName != null">college_name,</if>
            <if test="schoolDeptId != null">school_dept_id,</if>
            <if test="cooperationType != null">cooperation_type,</if>
            <if test="collaborator != null">collaborator,</if>
            <if test="professionalTypes != null">professional_types,</if>
            <if test="cooperativeEnterprisesNumber != null">cooperative_enterprises_number,</if>
            <if test="professionalGroupIndustryName != null">professional_group_industry_name,</if>
            <if test="cooperationAboveScaleNumber != null">cooperation_above_scale_number,</if>
            <if test="isCooperationAboveScale != null">is_cooperation_above_scale,</if>
            <if test="isRunningProject != null">is_running_project,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="classNumber != null">class_number,</if>
            <if test="studentNumber != null">student_number,</if>
            <if test="orderClassName != null">order_class_name,</if>
            <if test="fieldEngineerProjectLevel != null">field_engineer_project_level,</if>
            <if test="isPhysicalOperation != null">is_physical_operation,</if>
            <if test="communityName != null">community_name,</if>
            <if test="frontLinePostsNumber != null">front_line_posts_number,</if>
            <if test="partTimeClassesNumber != null">part_time_classes_number,</if>
            <if test="taskVolume != null">task_volume,</if>
            <if test="acceptingInternStudent != null">accepting_intern_student,</if>
            <if test="acceptingRecentGraduates != null">accepting_recent_graduates,</if>
            <if test="provideAnnualEmployees != null">provide_annual_employees,</if>
            <if test="coDevelopCoursesName != null">co_develop_courses_name,</if>
            <if test="coDevelopCoursesNumber != null">co_develop_courses_number,</if>
            <if test="commonTextbookName != null">common_textbook_name,</if>
            <if test="commonTextbookNumber != null">common_textbook_number,</if>
            <if test="trainingBaseName != null">training_base_name,</if>
            <if test="trainingBaseNumber != null">training_base_number,</if>
            <if test="practiceCenterName != null">practice_center_name,</if>
            <if test="practiceCenterNumber != null">practice_center_number,</if>
            <if test="annualContractIncome != null">annual_contract_income,</if>
            <if test="jointMajorCount != null">joint_major_count,</if>
            <if test="featuredMajorCount != null">featured_major_count,</if>
            <if test="trainingWorkstationCount != null">training_workstation_count,</if>
            <if test="workstationUtilizationRate != null">workstation_utilization_rate,</if>
            <if test="nationalCertificateCount != null">national_certificate_count,</if>
            <if test="provincialCertificateCount != null">provincial_certificate_count,</if>
            <if test="inventionPatentCount != null">invention_patent_count,</if>
            <if test="utilityPatentCount != null">utility_patent_count,</if>
            <if test="masterStudioCount != null">master_studio_count,</if>
            <if test="professionalGroupMatching != null">professional_group_matching,</if>
            <if test="integratedInvestment != null">integrated_investment,</if>
            <if test="belongingPark != null">belonging_park,</if>
            <if test="currentYearDonationValue != null">current_year_donation_value,</if>
            <if test="currentYearPlannedDonationValue != null">current_year_planned_donation_value,</if>
            <if test="currentYearScholarshipTotal != null">current_year_scholarship_total,</if>
            <if test="pastGraduatesCount != null">past_graduates_count,</if>
            <if test="currentGraduatesCount != null">current_graduates_count,</if>
            <if test="courseFullName != null">course_full_name,</if>
            <if test="textbookFullName != null">textbook_full_name,</if>
            <if test="skillCompetitionName != null">skill_competition_name,</if>
            <if test="industryCollabProjectCount != null">industry_collab_project_count,</if>
            <if test="trainingBaseCollaboration != null">training_base_collaboration,</if>
            <if test="orderTrainedStudents != null">order_trained_students,</if>
            <if test="apprenticeshipStudents != null">apprenticeship_students,</if>
            <if test="designPatents != null">design_patents,</if>
            <if test="softwareCopyrights != null">software_copyrights,</if>
            <if test="verticalProjectsCount != null">vertical_projects_count,</if>
            <if test="dataStatus != null">data_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">#{coverUri},</if>
            <if test="title != null">#{title},</if>
            <if test="contentTitle != null">#{contentTitle},</if>
            <if test="content != null">#{content},</if>
            <if test="economicBenefits != null">#{economicBenefits},</if>
            <if test="socialBenefits != null">#{socialBenefits},</if>
            <if test="cooperationPeriod != null">#{cooperationPeriod},</if>
            <if test="timeNode != null">#{timeNode},</if>
            <if test="collegeName != null">#{collegeName},</if>
            <if test="schoolDeptId != null">#{schoolDeptId},</if>
            <if test="cooperationType != null">#{cooperationType},</if>
            <if test="collaborator != null">#{collaborator},</if>
            <if test="professionalTypes != null">#{professionalTypes},</if>
            <if test="cooperativeEnterprisesNumber != null">#{cooperativeEnterprisesNumber},</if>
            <if test="professionalGroupIndustryName != null">#{professionalGroupIndustryName},</if>
            <if test="cooperationAboveScaleNumber != null">#{cooperationAboveScaleNumber},</if>
            <if test="isCooperationAboveScale != null">#{isCooperationAboveScale},</if>
            <if test="isRunningProject != null">#{isRunningProject},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="classNumber != null">#{classNumber},</if>
            <if test="studentNumber != null">#{studentNumber},</if>
            <if test="orderClassName != null">#{orderClassName},</if>
            <if test="fieldEngineerProjectLevel != null">#{fieldEngineerProjectLevel},</if>
            <if test="isPhysicalOperation != null">#{isPhysicalOperation},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="frontLinePostsNumber != null">#{frontLinePostsNumber},</if>
            <if test="partTimeClassesNumber != null">#{partTimeClassesNumber},</if>
            <if test="taskVolume != null">#{taskVolume},</if>
            <if test="acceptingInternStudent != null">#{acceptingInternStudent},</if>
            <if test="acceptingRecentGraduates != null">#{acceptingRecentGraduates},</if>
            <if test="provideAnnualEmployees != null">#{provideAnnualEmployees},</if>
            <if test="coDevelopCoursesName != null">#{coDevelopCoursesName},</if>
            <if test="coDevelopCoursesNumber != null">#{coDevelopCoursesNumber},</if>
            <if test="commonTextbookName != null">#{commonTextbookName},</if>
            <if test="commonTextbookNumber != null">#{commonTextbookNumber},</if>
            <if test="trainingBaseName != null">#{trainingBaseName},</if>
            <if test="trainingBaseNumber != null">#{trainingBaseNumber},</if>
            <if test="practiceCenterName != null">#{practiceCenterName},</if>
            <if test="practiceCenterNumber != null">#{practiceCenterNumber},</if>
            <if test="annualContractIncome != null">#{annualContractIncome},</if>
            <if test="jointMajorCount != null">#{jointMajorCount},</if>
            <if test="featuredMajorCount != null">#{featuredMajorCount},</if>
            <if test="trainingWorkstationCount != null">#{trainingWorkstationCount},</if>
            <if test="workstationUtilizationRate != null">#{workstationUtilizationRate},</if>
            <if test="nationalCertificateCount != null">#{nationalCertificateCount},</if>
            <if test="provincialCertificateCount != null">#{provincialCertificateCount},</if>
            <if test="inventionPatentCount != null">#{inventionPatentCount},</if>
            <if test="utilityPatentCount != null">#{utilityPatentCount},</if>
            <if test="masterStudioCount != null">#{masterStudioCount},</if>
            <if test="professionalGroupMatching != null">#{professionalGroupMatching},</if>
            <if test="integratedInvestment != null">#{integratedInvestment},</if>
            <if test="belongingPark != null">#{belongingPark},</if>
            <if test="currentYearDonationValue != null">#{currentYearDonationValue},</if>
            <if test="currentYearPlannedDonationValue != null">#{currentYearPlannedDonationValue},</if>
            <if test="currentYearScholarshipTotal != null">#{currentYearScholarshipTotal},</if>
            <if test="pastGraduatesCount != null">#{pastGraduatesCount},</if>
            <if test="currentGraduatesCount != null">#{currentGraduatesCount},</if>
            <if test="courseFullName != null">#{courseFullName},</if>
            <if test="textbookFullName != null">#{textbookFullName},</if>
            <if test="skillCompetitionName != null">#{skillCompetitionName},</if>
            <if test="industryCollabProjectCount != null">#{industryCollabProjectCount},</if>
            <if test="trainingBaseCollaboration != null">#{trainingBaseCollaboration},</if>
            <if test="orderTrainedStudents != null">#{orderTrainedStudents},</if>
            <if test="apprenticeshipStudents != null">#{apprenticeshipStudents},</if>
            <if test="designPatents != null">#{designPatents},</if>
            <if test="softwareCopyrights != null">#{softwareCopyrights},</if>
            <if test="verticalProjectsCount != null">#{verticalProjectsCount},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
         </trim>
    </insert>

    <update id="updateNekndIndustryEducationProjects" parameterType="NekndIndustryEducationProjects">
        update neknd_industry_education_projects
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="title != null">title = #{title},</if>
            <if test="contentTitle != null">content_title = #{contentTitle},</if>
            <if test="content != null">content = #{content},</if>
            <if test="economicBenefits != null">economic_benefits = #{economicBenefits},</if>
            <if test="socialBenefits != null">social_benefits = #{socialBenefits},</if>
            <if test="cooperationPeriod != null">cooperation_period = #{cooperationPeriod},</if>
            <if test="timeNode != null">time_node = #{timeNode},</if>
            <if test="collegeName != null">college_name = #{collegeName},</if>
            <if test="schoolDeptId != null">school_dept_id = #{schoolDeptId},</if>
            <if test="cooperationType != null">cooperation_type = #{cooperationType},</if>
            <if test="collaborator != null">collaborator = #{collaborator},</if>
            <if test="professionalTypes != null">professional_types = #{professionalTypes},</if>
            <if test="cooperativeEnterprisesNumber != null">cooperative_enterprises_number = #{cooperativeEnterprisesNumber},</if>
            <if test="professionalGroupIndustryName != null">professional_group_industry_name = #{professionalGroupIndustryName},</if>
            <if test="cooperationAboveScaleNumber != null">cooperation_above_scale_number = #{cooperationAboveScaleNumber},</if>
            <if test="isCooperationAboveScale != null">is_cooperation_above_scale = #{isCooperationAboveScale},</if>
            <if test="isRunningProject != null">is_running_project = #{isRunningProject},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="classNumber != null">class_number = #{classNumber},</if>
            <if test="studentNumber != null">student_number = #{studentNumber},</if>
            <if test="orderClassName != null">order_class_name = #{orderClassName},</if>
            <if test="fieldEngineerProjectLevel != null">field_engineer_project_level = #{fieldEngineerProjectLevel},</if>
            <if test="isPhysicalOperation != null">is_physical_operation = #{isPhysicalOperation},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="frontLinePostsNumber != null">front_line_posts_number = #{frontLinePostsNumber},</if>
            <if test="partTimeClassesNumber != null">part_time_classes_number = #{partTimeClassesNumber},</if>
            <if test="taskVolume != null">task_volume = #{taskVolume},</if>
            <if test="acceptingInternStudent != null">accepting_intern_student = #{acceptingInternStudent},</if>
            <if test="acceptingRecentGraduates != null">accepting_recent_graduates = #{acceptingRecentGraduates},</if>
            <if test="provideAnnualEmployees != null">provide_annual_employees = #{provideAnnualEmployees},</if>
            <if test="coDevelopCoursesName != null">co_develop_courses_name = #{coDevelopCoursesName},</if>
            <if test="coDevelopCoursesNumber != null">co_develop_courses_number = #{coDevelopCoursesNumber},</if>
            <if test="commonTextbookName != null">common_textbook_name = #{commonTextbookName},</if>
            <if test="commonTextbookNumber != null">common_textbook_number = #{commonTextbookNumber},</if>
            <if test="trainingBaseName != null">training_base_name = #{trainingBaseName},</if>
            <if test="trainingBaseNumber != null">training_base_number = #{trainingBaseNumber},</if>
            <if test="practiceCenterName != null">practice_center_name = #{practiceCenterName},</if>
            <if test="practiceCenterNumber != null">practice_center_number = #{practiceCenterNumber},</if>
            <if test="annualContractIncome != null">annual_contract_income = #{annualContractIncome},</if>
            <if test="jointMajorCount != null">joint_major_count = #{jointMajorCount},</if>
            <if test="featuredMajorCount != null">featured_major_count = #{featuredMajorCount},</if>
            <if test="trainingWorkstationCount != null">training_workstation_count = #{trainingWorkstationCount},</if>
            <if test="workstationUtilizationRate != null">workstation_utilization_rate = #{workstationUtilizationRate},</if>
            <if test="nationalCertificateCount != null">national_certificate_count = #{nationalCertificateCount},</if>
            <if test="provincialCertificateCount != null">provincial_certificate_count = #{provincialCertificateCount},</if>
            <if test="inventionPatentCount != null">invention_patent_count = #{inventionPatentCount},</if>
            <if test="utilityPatentCount != null">utility_patent_count = #{utilityPatentCount},</if>
            <if test="masterStudioCount != null">master_studio_count = #{masterStudioCount},</if>
            <if test="professionalGroupMatching != null">professional_group_matching = #{professionalGroupMatching},</if>
            <if test="integratedInvestment != null">integrated_investment = #{integratedInvestment},</if>
            <if test="belongingPark != null">belonging_park = #{belongingPark},</if>
            <if test="currentYearDonationValue != null">current_year_donation_value = #{currentYearDonationValue},</if>
            <if test="currentYearPlannedDonationValue != null">current_year_planned_donation_value = #{currentYearPlannedDonationValue},</if>
            <if test="currentYearScholarshipTotal != null">current_year_scholarship_total = #{currentYearScholarshipTotal},</if>
            <if test="pastGraduatesCount != null">past_graduates_count = #{pastGraduatesCount},</if>
            <if test="currentGraduatesCount != null">current_graduates_count = #{currentGraduatesCount},</if>
            <if test="courseFullName != null">course_full_name = #{courseFullName},</if>
            <if test="textbookFullName != null">textbook_full_name = #{textbookFullName},</if>
            <if test="skillCompetitionName != null">skill_competition_name = #{skillCompetitionName},</if>
            <if test="industryCollabProjectCount != null">industry_collab_project_count = #{industryCollabProjectCount},</if>
            <if test="trainingBaseCollaboration != null">training_base_collaboration = #{trainingBaseCollaboration},</if>
            <if test="orderTrainedStudents != null">order_trained_students = #{orderTrainedStudents},</if>
            <if test="apprenticeshipStudents != null">apprenticeship_students = #{apprenticeshipStudents},</if>
            <if test="designPatents != null">design_patents = #{designPatents},</if>
            <if test="softwareCopyrights != null">software_copyrights = #{softwareCopyrights},</if>
            <if test="verticalProjectsCount != null">vertical_projects_count = #{verticalProjectsCount},</if>
            <if test="dataStatus != null">data_status = #{dataStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndIndustryEducationProjectsById" parameterType="Integer">
        delete from neknd_industry_education_projects where id = #{id}
    </delete>

    <delete id="deleteNekndIndustryEducationProjectsByIds" parameterType="String">
        delete from neknd_industry_education_projects where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>