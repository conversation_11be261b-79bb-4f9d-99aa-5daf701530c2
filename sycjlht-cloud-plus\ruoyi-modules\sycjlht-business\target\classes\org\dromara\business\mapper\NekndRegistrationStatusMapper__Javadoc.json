{"doc": " 培训报名Mapper接口\n\n <AUTHOR>\n @date 2024-06-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": " 查询培训报名\n\n @param id 培训报名主键\n @return 培训报名\n"}, {"name": "selectNekndRegistrationStatusList", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 查询培训报名列表\n\n @param nekndRegistrationStatus 培训报名\n @return 培训报名集合\n"}, {"name": "insertNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 新增培训报名\n\n @param nekndRegistrationStatus 培训报名\n @return 结果\n"}, {"name": "updateNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 修改培训报名\n\n @param nekndRegistrationStatus 培训报名\n @return 结果\n"}, {"name": "deleteNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": " 删除培训报名\n\n @param id 培训报名主键\n @return 结果\n"}, {"name": "deleteNekndRegistrationStatusByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除培训报名\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}, {"name": "getPersonalRegistrationCount", "paramTypes": ["java.lang.Long"], "doc": " 获取个人报名总数（培训+科普研学+继续教育）\n \n @param userId 用户ID\n @return 报名总数\n"}], "constructors": []}