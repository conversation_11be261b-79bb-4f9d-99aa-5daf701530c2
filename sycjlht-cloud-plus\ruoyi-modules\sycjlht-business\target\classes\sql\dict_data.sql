-- 新闻模块字典数据
-- 新闻类型字典
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES (100, '000000', '新闻类型', 'sys_news_type', '0', 'admin', NOW(), 'admin', NOW(), '新闻类型列表');

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1000, '000000', 1, '职教动态', '1', 'sys_news_type', '', 'primary', 'Y', '0', 'admin', NOW(), 'admin', NOW(), '职教动态'),
(1001, '000000', 2, '职教新闻', '2', 'sys_news_type', '', 'success', 'N', '0', 'admin', NOW(), 'admin', NOW(), '职教新闻'),
(1002, '000000', 3, '政策解读', '3', 'sys_news_type', '', 'info', 'N', '0', 'admin', NOW(), 'admin', NOW(), '政策解读'),
(1003, '000000', 4, '其他', '4', 'sys_news_type', '', 'warning', 'N', '0', 'admin', NOW(), 'admin', NOW(), '其他');

-- 政策类别字典
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES (101, '000000', '政策类别', 'sys_policy_category', '0', 'admin', NOW(), 'admin', NOW(), '政策类别列表');

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1010, '000000', 1, '国家政策', '1', 'sys_policy_category', '', 'primary', 'Y', '0', 'admin', NOW(), 'admin', NOW(), '国家政策'),
(1011, '000000', 2, '省级政策', '2', 'sys_policy_category', '', 'success', 'N', '0', 'admin', NOW(), 'admin', NOW(), '省级政策'),
(1012, '000000', 3, '市级政策', '3', 'sys_policy_category', '', 'info', 'N', '0', 'admin', NOW(), 'admin', NOW(), '市级政策'),
(1013, '000000', 4, '区县政策', '4', 'sys_policy_category', '', 'warning', 'N', '0', 'admin', NOW(), 'admin', NOW(), '区县政策');

-- 文件类型字典
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES (102, '000000', '文件类型', 'sys_file_type', '0', 'admin', NOW(), 'admin', NOW(), '文件类型列表');

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1020, '000000', 1, '指导意见', '1', 'sys_file_type', '', 'primary', 'Y', '0', 'admin', NOW(), 'admin', NOW(), '指导意见'),
(1021, '000000', 2, '实施方案', '2', 'sys_file_type', '', 'success', 'N', '0', 'admin', NOW(), 'admin', NOW(), '实施方案'),
(1022, '000000', 3, '政策文件', '3', 'sys_file_type', '', 'info', 'N', '0', 'admin', NOW(), 'admin', NOW(), '政策文件');

-- 方案类型字典
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES (103, '000000', '方案类型', 'sys_plan_type', '0', 'admin', NOW(), 'admin', NOW(), '方案类型列表');

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1030, '000000', 0, '无', '0', 'sys_plan_type', '', 'info', 'Y', '0', 'admin', NOW(), 'admin', NOW(), '无'),
(1031, '000000', 1, '经费方案', '1', 'sys_plan_type', '', 'primary', 'N', '0', 'admin', NOW(), 'admin', NOW(), '经费方案'),
(1032, '000000', 2, '土地方案', '2', 'sys_plan_type', '', 'success', 'N', '0', 'admin', NOW(), 'admin', NOW(), '土地方案'),
(1033, '000000', 3, '税收方案', '3', 'sys_plan_type', '', 'warning', 'N', '0', 'admin', NOW(), 'admin', NOW(), '税收方案');

-- 是否字典（如果不存在）
INSERT INTO sys_dict_type (dict_id, tenant_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES (104, '000000', '是否', 'sys_yes_no', '0', 'admin', NOW(), 'admin', NOW(), '是否选择') 
ON DUPLICATE KEY UPDATE dict_name = dict_name;

INSERT INTO sys_dict_data (dict_code, tenant_id, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1040, '000000', 0, '无', '0', 'sys_yes_no', '', 'info', 'Y', '0', 'admin', NOW(), 'admin', NOW(), '无'),
(1041, '000000', 1, '是', '1', 'sys_yes_no', '', 'primary', 'N', '0', 'admin', NOW(), 'admin', NOW(), '是'),
(1042, '000000', 2, '不是', '2', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', NOW(), 'admin', NOW(), '不是')
ON DUPLICATE KEY UPDATE dict_label = dict_label;
