<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndResourcesConservationMapper">
    
    <resultMap type="NekndResourcesConservation" id="NekndResourcesConservationResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="title"    column="title"    />
        <result property="contentTitle"    column="content_title"    />
        <result property="content"    column="content"    />
        <result property="coservationType"    column="coservation_type"    />
        <result property="timeNode"    column="time_node"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="schoolDeptId"    column="school_dept_id"    />
        <result property="collegeName"    column="college_name"    />
        <result property="cooperationType"    column="cooperation_type"    />
        <result property="collaborator"    column="collaborator"    />
    </resultMap>

    <sql id="selectNekndResourcesConservationVo">
        select id, cover_uri, title, content_title, content, coservation_type, time_node, del_flag, create_time, update_time, school_dept_id, college_name,cooperation_type,collaborator from neknd_resources_conservation
    </sql>

    <select id="selectNekndResourcesConservationList" parameterType="NekndResourcesConservation" resultMap="NekndResourcesConservationResult">
        <include refid="selectNekndResourcesConservationVo"/>
        <where>  
            <if test="coverUri != null  and coverUri != ''"> and cover_uri = #{coverUri}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="contentTitle != null  and contentTitle != ''"> and content_title = #{contentTitle}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="coservationType != null  and coservationType != ''"> and coservation_type = #{coservationType}</if>
            <if test="timeNode != null  and timeNode != ''"> and time_node = #{timeNode}</if>
              <if test="schoolDeptId != null  and schoolDeptId != ''"> and school_dept_id = #{schoolDeptId}</if>
               <if test="collegeName != null  and collegeName != ''"> and college_name = #{collegeName}</if>
               <if test="cooperationType != null  and cooperationType != ''"> and cooperation_type = #{cooperationType}</if>
               <if test="collaborator != null  and collaborator != ''"> and collaborator = #{collaborator}</if>
        </where>
    </select>

    <select id="selectNekndResourcesConservationListCompany" resultMap="NekndResourcesConservationResult">
        select *
        from neknd_resources_conservation
        where coservation_type = '0'
        <if test="collegeName != null  and collegeName != ''"> and college_name = #{collegeName}</if>
        <if test="cooperationType != null  and cooperationType != ''"> and cooperation_type = #{cooperationType}</if>
        <if test="collaborator != null  and collaborator != ''"> and collaborator = #{collaborator}</if>
    </select>

    <select id="selectNekndResourcesConservationListGovernment" resultMap="NekndResourcesConservationResult">
        select *
        from neknd_resources_conservation
        where coservation_type = '1'
    </select>
    
    <select id="selectNekndResourcesConservationById" parameterType="Integer" resultMap="NekndResourcesConservationResult">
        <include refid="selectNekndResourcesConservationVo"/>
        where id = #{id}
    </select>

    <resultMap id="conservationMap" type="java.util.HashMap">
        <result property="Name" column="Name"/>
        <result property="count" column="count" />
        <result property="collegeName" column="collegeName" />
        <result property="deptId" column="deptId" />
    </resultMap>
    <select id="selectResourcesConservationListBydeptId" resultMap="conservationMap">
        SELECT
            CASE
                WHEN college_name = 1 THEN '商学院'
                WHEN college_name = 2 THEN '工学院'
                WHEN college_name = 3 THEN '会计学院'
                WHEN college_name = 4 THEN '建筑学院'
                WHEN college_name = 5 THEN '健康管理学院'
                WHEN college_name = 6 THEN '人文艺术学院'
                WHEN college_name = 7 THEN '生物工程学院'
                WHEN college_name = 8 THEN '信息工程学院'
                WHEN college_name = 9 THEN '其他'
                END AS Name,
            COUNT(college_name) as count,
            college_name as collegeName,
            school_dept_id as deptId
        FROM neknd_resources_conservation
        WHERE college_name !="" and college_name is not NULL and del_flag =0
        and school_dept_id = #{schoolDeptId}
        GROUP BY college_name
        order by count desc;
    </select>

    <insert id="insertNekndResourcesConservation" parameterType="NekndResourcesConservation" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_resources_conservation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">cover_uri,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="contentTitle != null and contentTitle != ''">content_title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="coservationType != null and coservationType != ''">coservation_type,</if>
            <if test="timeNode != null and timeNode != ''">time_node,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="schoolDeptId != null and schoolDeptId != ''">school_dept_id,</if>
            <if test="collegeName != null and collegeName != ''">college_name,</if>
            <if test="cooperationType != null and cooperationType != ''">cooperation_type,</if>
            <if test="collaborator != null and collaborator != ''">collaborator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">#{coverUri},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="contentTitle != null and contentTitle != ''">#{contentTitle},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="coservationType != null and coservationType != ''">#{coservationType},</if>
            <if test="timeNode != null and timeNode != ''">#{timeNode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="schoolDeptId != null and schoolDeptId != ''">#{schoolDeptId},</if>
            <if test="collegeName != null and collegeName != ''">#{collegeName},</if>
            <if test="cooperationType != null and cooperationType != ''">#{cooperationType},</if>
            <if test="collaborator != null and collaborator != ''">#{collaborator},</if>
         </trim>
    </insert>

    <update id="updateNekndResourcesConservation" parameterType="NekndResourcesConservation">
        update neknd_resources_conservation
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">cover_uri = #{coverUri},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="contentTitle != null and contentTitle != ''">content_title = #{contentTitle},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="coservationType != null and coservationType != ''">coservation_type = #{coservationType},</if>
            <if test="timeNode != null and timeNode != ''">time_node = #{timeNode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="schoolDeptId != null and schoolDeptId != ''">school_dept_id = #{schoolDeptId},</if>
            <if test="collegeName != null and collegeName != ''">college_name = #{collegeName},</if>
            <if test="cooperationType != null and cooperationType != ''">cooperation_type = #{cooperationType},</if>
            <if test="collaborator != null and collaborator != ''">collaborator = #{collaborator},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndResourcesConservationById" parameterType="Integer">
        delete from neknd_resources_conservation where id = #{id}
    </delete>

    <delete id="deleteNekndResourcesConservationByIds" parameterType="String">
        delete from neknd_resources_conservation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>