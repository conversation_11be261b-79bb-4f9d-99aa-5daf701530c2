{"doc": " 读取项目相关配置\n\n <AUTHOR>\n", "fields": [{"name": "name", "doc": "项目名称 "}, {"name": "version", "doc": "版本 "}, {"name": "copyrightYear", "doc": "版权年份 "}, {"name": "profile", "doc": "上传路径 "}, {"name": "addressEnabled", "doc": "获取地址开关 "}, {"name": "captchaType", "doc": "验证码类型 "}], "enumConstants": [], "methods": [{"name": "getImportPath", "paramTypes": [], "doc": " 获取导入上传路径\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 获取头像上传路径\n"}, {"name": "getDownloadPath", "paramTypes": [], "doc": " 获取下载路径\n"}, {"name": "getUploadPath", "paramTypes": [], "doc": " 获取上传路径\n"}], "constructors": []}