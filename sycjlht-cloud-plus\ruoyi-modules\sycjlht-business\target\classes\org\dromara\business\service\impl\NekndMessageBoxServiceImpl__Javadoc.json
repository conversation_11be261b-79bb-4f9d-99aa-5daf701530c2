{"doc": " 留言箱Service业务层处理\n \n <AUTHOR>\n @date 2024-06-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": " 查询留言箱\n \n @param id 留言箱主键\n @return 留言箱\n"}, {"name": "selectNekndMessageBoxList", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 查询留言箱列表\n \n @param nekndMessageBox 留言箱\n @return 留言箱\n"}, {"name": "insertNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 新增留言箱\n \n @param nekndMessageBox 留言箱\n @return 结果\n"}, {"name": "updateNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 修改留言箱\n \n @param nekndMessageBox 留言箱\n @return 结果\n"}, {"name": "deleteNekndMessageBoxByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除留言箱\n \n @param ids 需要删除的留言箱主键\n @return 结果\n"}, {"name": "deleteNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": " 删除留言箱信息\n \n @param id 留言箱主键\n @return 结果\n"}, {"name": "updateReadStatusByParentId", "paramTypes": ["java.lang.Integer"], "doc": " 更新已读状态\n \n @param parentId 父级ID\n @return 结果\n"}, {"name": "getCountUnReadCStatus", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 获取未读消息数量\n \n @param userId 用户ID\n @param topicId 主题ID\n @return 未读数量\n"}, {"name": "selectAdminMessageBoxList", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 查询管理员留言箱列表\n \n @param nekndMessageBox 留言箱\n @return 留言箱集合\n"}, {"name": "deleteNekndMessageBoxByParentId", "paramTypes": ["java.lang.Integer"], "doc": " 根据父级ID删除留言箱\n \n @param id 父级ID\n @return 结果\n"}, {"name": "updateSenderReadStatusByParentId", "paramTypes": ["java.lang.Integer", "int"], "doc": " 更新发送者已读状态\n \n @param parentId 父级ID\n @param senderUserId 发送者用户ID\n @return 结果\n"}, {"name": "queryMessagesByTopic", "paramTypes": ["java.lang.Integer"], "doc": " 根据主题ID查询消息列表\n\n @param topicId 主题ID\n @return 消息集合\n"}], "constructors": []}