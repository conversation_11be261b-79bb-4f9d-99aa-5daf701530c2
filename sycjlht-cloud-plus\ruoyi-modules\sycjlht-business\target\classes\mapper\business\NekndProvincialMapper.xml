<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndProvincialMapper">
    
    <resultMap type="NekndProvincial" id="NekndProvincialResult">
        <result property="pid"    column="pid"    />
        <result property="provincial"    column="provincial"    />
    </resultMap>

    <sql id="selectNekndProvincialVo">
        select pid, provincial from neknd_provincial
    </sql>

    <select id="selectNekndProvincialList" parameterType="NekndProvincial" resultMap="NekndProvincialResult">
        <include refid="selectNekndProvincialVo"/>
        <where>  
            <if test="provincial != null  and provincial != ''"> and provincial = #{provincial}</if>
        </where>
    </select>
    
    <select id="selectNekndProvincialByPid" parameterType="Long" resultMap="NekndProvincialResult">
        <include refid="selectNekndProvincialVo"/>
        where pid = #{pid}
    </select>
        
    <insert id="insertNekndProvincial" parameterType="NekndProvincial">
        insert into neknd_provincial
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">pid,</if>
            <if test="provincial != null">provincial,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">#{pid},</if>
            <if test="provincial != null">#{provincial},</if>
         </trim>
    </insert>

    <update id="updateNekndProvincial" parameterType="NekndProvincial">
        update neknd_provincial
        <trim prefix="SET" suffixOverrides=",">
            <if test="provincial != null">provincial = #{provincial},</if>
        </trim>
        where pid = #{pid}
    </update>

    <delete id="deleteNekndProvincialByPid" parameterType="Long">
        delete from neknd_provincial where pid = #{pid}
    </delete>

    <delete id="deleteNekndProvincialByPids" parameterType="String">
        delete from neknd_provincial where pid in 
        <foreach item="pid" collection="array" open="(" separator="," close=")">
            #{pid}
        </foreach>
    </delete>

    <select id="selectNekndProvincialIdByName" resultType="Long">
        SELECT pid
        FROM neknd_provincial
        WHERE provincial= #{provincial}
    </select>

    <resultMap id="provincialData" type="hashmap">
        <result property="pid" column="pid" />
        <result property="provincial" column="provincial" />
    </resultMap>
    <select id="getProvincialNames" parameterType="java.util.Set" resultType="hashmap">
        SELECT
            pid, provincial
        FROM
            neknd_provincial
        where
            pid in
        <foreach item="id" collection="keys" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>