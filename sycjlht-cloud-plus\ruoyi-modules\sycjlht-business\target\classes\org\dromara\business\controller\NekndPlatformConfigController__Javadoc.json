{"doc": " 平台首页信息Controller\n \n <AUTHOR>\n @date 2025-02-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询平台首页信息列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPlatformConfig"], "doc": " 导出平台首页信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取平台首页信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": " 新增平台首页信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": " 修改平台首页信息\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取平台首页信息详细信息（公共接口）\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": " 获取平台首页信息详细信息（默认主题为0）\n"}, {"name": "clearCache", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 清除平台配置缓存\n"}, {"name": "clearAllCache", "paramTypes": [], "doc": " 批量清除缓存\n"}, {"name": "updateCache", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": " 更新缓存\n"}, {"name": "buildCacheKey", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建缓存Key\n"}], "constructors": []}