{"doc": " 提供通用唯一识别码（universally unique identifier）（UUID）实现\n\n <AUTHOR>\n", "fields": [{"name": "mostSigBits", "doc": "此UUID的最高64有效位 "}, {"name": "leastSigBits", "doc": "此UUID的最低64有效位 "}], "enumConstants": [], "methods": [{"name": "fastUUID", "paramTypes": [], "doc": " 获取类型 4（伪随机生成的）UUID 的静态工厂。\n\n @return 随机生成的 {@code UUID}\n"}, {"name": "randomUUID", "paramTypes": [], "doc": " 获取类型 4（伪随机生成的）UUID 的静态工厂。 使用加密的强伪随机数生成器生成该 UUID。\n\n @return 随机生成的 {@code UUID}\n"}, {"name": "randomUUID", "paramTypes": ["boolean"], "doc": " 获取类型 4（伪随机生成的）UUID 的静态工厂。 使用加密的强伪随机数生成器生成该 UUID。\n\n @param isSecure 是否使用{@link SecureRandom}如果是可以获得更安全的随机码，否则可以得到更好的性能\n @return 随机生成的 {@code UUID}\n"}, {"name": "nameUUIDFromBytes", "paramTypes": ["byte[]"], "doc": " 根据指定的字节数组获取类型 3（基于名称的）UUID 的静态工厂。\n\n @param name 用于构造 UUID 的字节数组。\n\n @return 根据指定数组生成的 {@code UUID}\n"}, {"name": "fromString", "paramTypes": ["java.lang.String"], "doc": " 根据 {@link #toString()} 方法中描述的字符串标准表示形式创建{@code UUID}。\n\n @param name 指定 {@code UUID} 字符串\n @return 具有指定值的 {@code UUID}\n @throws IllegalArgumentException 如果 name 与 {@link #toString} 中描述的字符串表示形式不符抛出此异常\n\n"}, {"name": "getLeastSignificantBits", "paramTypes": [], "doc": " 返回此 UUID 的 128 位值中的最低有效 64 位。\n\n @return 此 UUID 的 128 位值中的最低有效 64 位。\n"}, {"name": "getMostSignificantBits", "paramTypes": [], "doc": " 返回此 UUID 的 128 位值中的最高有效 64 位。\n\n @return 此 UUID 的 128 位值中最高有效 64 位。\n"}, {"name": "version", "paramTypes": [], "doc": " 与此 {@code UUID} 相关联的版本号. 版本号描述此 {@code UUID} 是如何生成的。\n <p>\n 版本号具有以下含意:\n <ul>\n <li>1 基于时间的 UUID\n <li>2 DCE 安全 UUID\n <li>3 基于名称的 UUID\n <li>4 随机生成的 UUID\n </ul>\n\n @return 此 {@code UUID} 的版本号\n"}, {"name": "variant", "paramTypes": [], "doc": " 与此 {@code UUID} 相关联的变体号。变体号描述 {@code UUID} 的布局。\n <p>\n 变体号具有以下含意：\n <ul>\n <li>0 为 NCS 向后兼容保留\n <li>2 <a href=\"http://www.ietf.org/rfc/rfc4122.txt\">IETF&nbsp;RFC&nbsp;4122</a>(Leach-Salz), 用于此类\n <li>6 保留，微软向后兼容\n <li>7 保留供以后定义使用\n </ul>\n\n @return 此 {@code UUID} 相关联的变体号\n"}, {"name": "timestamp", "paramTypes": [], "doc": " 与此 UUID 相关联的时间戳值。\n\n <p>\n 60 位的时间戳值根据此 {@code UUID} 的 time_low、time_mid 和 time_hi 字段构造。<br>\n 所得到的时间戳以 100 毫微秒为单位，从 UTC（通用协调时间） 1582 年 10 月 15 日零时开始。\n\n <p>\n 时间戳值仅在在基于时间的 UUID（其 version 类型为 1）中才有意义。<br>\n 如果此 {@code UUID} 不是基于时间的 UUID，则此方法抛出 UnsupportedOperationException。\n\n @throws UnsupportedOperationException 如果此 {@code UUID} 不是 version 为 1 的 UUID。\n"}, {"name": "clockSequence", "paramTypes": [], "doc": " 与此 UUID 相关联的时钟序列值。\n\n <p>\n 14 位的时钟序列值根据此 UUID 的 clock_seq 字段构造。clock_seq 字段用于保证在基于时间的 UUID 中的时间唯一性。\n <p>\n {@code clockSequence} 值仅在基于时间的 UUID（其 version 类型为 1）中才有意义。 如果此 UUID 不是基于时间的 UUID，则此方法抛出\n UnsupportedOperationException。\n\n @return 此 {@code UUID} 的时钟序列\n\n @throws UnsupportedOperationException 如果此 UUID 的 version 不为 1\n"}, {"name": "node", "paramTypes": [], "doc": " 与此 UUID 相关的节点值。\n\n <p>\n 48 位的节点值根据此 UUID 的 node 字段构造。此字段旨在用于保存机器的 IEEE 802 地址，该地址用于生成此 UUID 以保证空间唯一性。\n <p>\n 节点值仅在基于时间的 UUID（其 version 类型为 1）中才有意义。<br>\n 如果此 UUID 不是基于时间的 UUID，则此方法抛出 UnsupportedOperationException。\n\n @return 此 {@code UUID} 的节点值\n\n @throws UnsupportedOperationException 如果此 UUID 的 version 不为 1\n"}, {"name": "toString", "paramTypes": [], "doc": " 返回此{@code UUID} 的字符串表现形式。\n\n <p>\n UUID 的字符串表示形式由此 BNF 描述：\n\n <pre>\n {@code\n UUID                   = <time_low>-<time_mid>-<time_high_and_version>-<variant_and_sequence>-<node>\n time_low               = 4*<hexOctet>\n time_mid               = 2*<hexOctet>\n time_high_and_version  = 2*<hexOctet>\n variant_and_sequence   = 2*<hexOctet>\n node                   = 6*<hexOctet>\n hexOctet               = <hexDigit><hexDigit>\n hexDigit               = [0-9a-fA-F]\n }\n </pre>\n\n </blockquote>\n\n @return 此{@code UUID} 的字符串表现形式\n @see #toString(boolean)\n"}, {"name": "toString", "paramTypes": ["boolean"], "doc": " 返回此{@code UUID} 的字符串表现形式。\n\n <p>\n UUID 的字符串表示形式由此 BNF 描述：\n\n <pre>\n {@code\n UUID                   = <time_low>-<time_mid>-<time_high_and_version>-<variant_and_sequence>-<node>\n time_low               = 4*<hexOctet>\n time_mid               = 2*<hexOctet>\n time_high_and_version  = 2*<hexOctet>\n variant_and_sequence   = 2*<hexOctet>\n node                   = 6*<hexOctet>\n hexOctet               = <hexDigit><hexDigit>\n hexDigit               = [0-9a-fA-F]\n }\n </pre>\n\n </blockquote>\n\n @param isSimple 是否简单模式，简单模式为不带'-'的UUID字符串\n @return 此{@code UUID} 的字符串表现形式\n"}, {"name": "hashCode", "paramTypes": [], "doc": " 返回此 UUID 的哈希码。\n\n @return UUID 的哈希码值。\n"}, {"name": "equals", "paramTypes": ["java.lang.Object"], "doc": " 将此对象与指定对象比较。\n <p>\n 当且仅当参数不为 {@code null}、而是一个 UUID 对象、具有与此 UUID 相同的 varriant、包含相同的值（每一位均相同）时，结果才为 {@code true}。\n\n @param obj 要与之比较的对象\n\n @return 如果对象相同，则返回 {@code true}；否则返回 {@code false}\n"}, {"name": "compareTo", "paramTypes": ["org.dromara.business.utils.uuid.UUID"], "doc": " 将此 UUID 与指定的 UUID 比较。\n\n <p>\n 如果两个 UUID 不同，且第一个 UUID 的最高有效字段大于第二个 UUID 的对应字段，则第一个 UUID 大于第二个 UUID。\n\n @param val 与此 UUID 比较的 UUID\n\n @return 在此 UUID 小于、等于或大于 val 时，分别返回 -1、0 或 1。\n\n"}, {"name": "digits", "paramTypes": ["long", "int"], "doc": " 返回指定数字对应的hex值\n\n @param val 值\n @param digits 位\n @return 值\n"}, {"name": "checkTimeBase", "paramTypes": [], "doc": " 检查是否为time-based版本UUID\n"}, {"name": "getSecureRandom", "paramTypes": [], "doc": " 获取{@link SecureRandom}，类提供加密的强随机数生成器 (RNG)\n\n @return {@link SecureRandom}\n"}, {"name": "getRandom", "paramTypes": [], "doc": " 获取随机数生成器对象<br>\n ThreadLocalRandom是JDK 7之后提供并发产生随机数，能够解决多个线程发生的竞争争夺。\n\n @return {@link ThreadLocalRandom}\n"}], "constructors": [{"name": "<init>", "paramTypes": ["byte[]"], "doc": " 私有构造\n\n @param data 数据\n"}, {"name": "<init>", "paramTypes": ["long", "long"], "doc": " 使用指定的数据构造新的 UUID。\n\n @param mostSigBits 用于 {@code UUID} 的最高有效 64 位\n @param leastSigBits 用于 {@code UUID} 的最低有效 64 位\n"}]}