{"doc": " 市级Service接口\n\n <AUTHOR>\n @date 2024-05-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndProvincialCityByCid", "paramTypes": ["java.lang.Long"], "doc": " 查询市级\n\n @param cid 市级主键\n @return 市级\n"}, {"name": "selectNekndProvincialCityList", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": " 查询市级列表\n\n @param nekndProvincialCity 市级\n @return 市级集合\n"}, {"name": "insertNekndProvincialCity", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": " 新增市级\n\n @param nekndProvincialCity 市级\n @return 结果\n"}, {"name": "updateNekndProvincialCity", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": " 修改市级\n\n @param nekndProvincialCity 市级\n @return 结果\n"}, {"name": "deleteNekndProvincialCityByCids", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除市级\n\n @param cids 需要删除的市级主键集合\n @return 结果\n"}, {"name": "deleteNekndProvincialCityByCid", "paramTypes": ["java.lang.Long"], "doc": " 删除市级信息\n\n @param cid 市级主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询市级列表\n\n @param entity 查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}, {"name": "searchCities", "paramTypes": ["java.lang.String"], "doc": " 搜索城市数据\n\n @param keyword 搜索关键字\n @return 城市列表\n"}, {"name": "getCityStatistics", "paramTypes": [], "doc": " 获取城市统计信息\n\n @return 统计数据\n"}, {"name": "getPopularCities", "paramTypes": [], "doc": " 获取热门城市列表\n\n @return 热门城市列表\n"}], "constructors": []}