{"doc": " 成员单位信息Controller\n \n <AUTHOR>\n @date 2024-10-22\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndUnit", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询成员单位信息列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndUnit", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询成员单位信息列表（公共接口）\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndUnit"], "doc": " 导出成员单位信息列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入成员单位信息\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取成员单位信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 新增成员单位信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 修改成员单位信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除成员单位信息\n"}, {"name": "getParkDict", "paramTypes": [], "doc": " 查看所属园区字典\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新单位状态\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取单位统计信息\n"}, {"name": "listByType", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按单位类型查询\n"}, {"name": "search", "paramTypes": ["org.dromara.business.domain.NekndUnit", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 搜索成员单位\n"}], "constructors": []}