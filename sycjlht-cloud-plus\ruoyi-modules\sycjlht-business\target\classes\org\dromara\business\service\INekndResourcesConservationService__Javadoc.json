{"doc": " 资源共建（校企合作）Service接口\n\n <AUTHOR>\n @date 2024-11-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndResourcesConservationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询资源共建（校企合作）\n\n @param id 资源共建（校企合作）主键\n @return 资源共建（校企合作）\n"}, {"name": "selectNekndResourcesConservationList", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 查询资源共建（校企合作）列表\n\n @param nekndResourcesConservation 资源共建（校企合作）\n @return 资源共建（校企合作）集合\n"}, {"name": "selectNekndResourcesConservationListCompany", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 查询资源共建（校企合作）列表\n\n\n @return 资源共建（校企合作）集合\n"}, {"name": "selectNekndResourcesConservationListGovernment", "paramTypes": [], "doc": " 查询资源共建（校企合作）列表\n\n\n @return 资源共建（校企合作）集合\n"}, {"name": "insertNekndResourcesConservation", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 新增资源共建（校企合作）\n\n @param nekndResourcesConservation 资源共建（校企合作）\n @return 结果\n"}, {"name": "updateNekndResourcesConservation", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 修改资源共建（校企合作）\n\n @param nekndResourcesConservation 资源共建（校企合作）\n @return 结果\n"}, {"name": "deleteNekndResourcesConservationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除资源共建（校企合作）\n\n @param ids 需要删除的资源共建（校企合作）主键集合\n @return 结果\n"}, {"name": "deleteNekndResourcesConservationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除资源共建（校企合作）信息\n\n @param id 资源共建（校企合作）主键\n @return 结果\n"}, {"name": "selectNekndResourcesConservationListPage", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 分页查询资源共建列表\n\n @param nekndResourcesConservation 资源共建\n @return 资源共建集合\n"}, {"name": "selectNekndResourcesConservationListForExport", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": " 导出查询资源共建列表\n\n @param nekndResourcesConservation 资源共建\n @return 资源共建集合\n"}], "constructors": []}