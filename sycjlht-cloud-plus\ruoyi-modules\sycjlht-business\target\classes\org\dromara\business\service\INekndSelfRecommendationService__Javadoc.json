{"doc": " 自荐信息Service接口\n\n <AUTHOR>\n @date 2024-05-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSelfRecommendationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询自荐信息\n\n @param id 自荐信息主键\n @return 自荐信息\n"}, {"name": "selectNekndSelfRecommendationList", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": " 查询自荐信息列表\n\n @param nekndSelfRecommendation 自荐信息\n @return 自荐信息集合\n"}, {"name": "selectNekndSelfRecommendationList", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询自荐信息列表\n\n @param nekndSelfRecommendation 自荐信息\n @param pageQuery 分页查询\n @return 自荐信息集合\n"}, {"name": "insertNekndSelfRecommendation", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": " 新增自荐信息\n\n @param nekndSelfRecommendation 自荐信息\n @return 结果\n"}, {"name": "updateNekndSelfRecommendation", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": " 修改自荐信息\n\n @param nekndSelfRecommendation 自荐信息\n @return 结果\n"}, {"name": "deleteNekndSelfRecommendationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除自荐信息\n\n @param ids 需要删除的自荐信息主键集合\n @return 结果\n"}, {"name": "deleteNekndSelfRecommendationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除自荐信息信息\n\n @param id 自荐信息主键\n @return 结果\n"}], "constructors": []}