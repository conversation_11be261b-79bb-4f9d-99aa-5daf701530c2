{"doc": " 【请填写功能名称】Mapper接口\n\n <AUTHOR>\n @date 2024-12-25\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryPolicyNumberByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustryPolicyNumberByIndustryIdCount", "paramTypes": ["java.lang.Long"], "doc": " 查询对应id记录数\n\n @param industryId 【请填写功能名称】主键\n @return 【记录数】\n"}, {"name": "selectNekndIndustryPolicyNumberList", "paramTypes": ["org.dromara.business.domain.NekndIndustryPolicyNumber"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustryPolicyNumber 【请填写功能名称】\n @return 【请填写功能名称】集合\n"}, {"name": "insertNekndIndustryPolicyNumber", "paramTypes": ["org.dromara.business.domain.NekndIndustryPolicyNumber"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustryPolicyNumber 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustryPolicyNumber", "paramTypes": ["org.dromara.business.domain.NekndIndustryPolicyNumber"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustryPolicyNumber 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustryPolicyNumberByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 结果\n"}, {"name": "deleteNekndIndustryPolicyNumberByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industryIds 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}