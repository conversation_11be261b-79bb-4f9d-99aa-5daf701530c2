<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryProportionTeachersMapper">
    
    <resultMap type="NekndIndustryProportionTeachers" id="NekndIndustryProportionTeachersResult">
        <result property="industryId"    column="industry_id"    />
        <result property="teacherLevel"    column="teacher_level"    />
        <result property="teacherAccount"    column="teacher_account"    />
    </resultMap>

    <sql id="selectNekndIndustryProportionTeachersVo">
        select industry_id, teacher_level, teacher_account from neknd_industry_proportion_teachers
    </sql>

    <select id="selectNekndIndustryProportionTeachersList" parameterType="NekndIndustryProportionTeachers" resultMap="NekndIndustryProportionTeachersResult">
        <include refid="selectNekndIndustryProportionTeachersVo"/>
        <where>  
            <if test="teacherLevel != null  and teacherLevel != ''"> and teacher_level = #{teacherLevel}</if>
            <if test="teacherAccount != null "> and teacher_account = #{teacherAccount}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryProportionTeachersByIndustryId" parameterType="Long" resultMap="NekndIndustryProportionTeachersResult">
        <include refid="selectNekndIndustryProportionTeachersVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustryProportionTeachersByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_proportion_teachers
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustryProportionTeachers" parameterType="NekndIndustryProportionTeachers" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_proportion_teachers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="teacherLevel != null">teacher_level,</if>
            <if test="teacherAccount != null">teacher_account,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="teacherLevel != null">#{teacherLevel},</if>
            <if test="teacherAccount != null">#{teacherAccount},</if>
        </trim>
        On Duplicate Key Update industry_id = VALUES(industry_id), teacher_level = VALUES(teacher_level), teacher_account = VALUES(teacher_account)
    </insert>

    <update id="updateNekndIndustryProportionTeachers" parameterType="NekndIndustryProportionTeachers">
        update neknd_industry_proportion_teachers
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherLevel != null">teacher_level = #{teacherLevel},</if>
            <if test="teacherAccount != null">teacher_account = #{teacherAccount},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryProportionTeachersByIndustryId" parameterType="Long">
        delete from neknd_industry_proportion_teachers where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryProportionTeachersByIndustryIds" parameterType="String">
        delete from neknd_industry_proportion_teachers where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>