{"doc": " 招聘岗位对象 neknd_employ\n\n <AUTHOR>\n @date 2025-07-09\n", "fields": [{"name": "id", "doc": "ID "}, {"name": "tenantId", "doc": "租户ID "}, {"name": "delFlag", "doc": "删除标志（0代表存在 2代表删除） "}, {"name": "deptId", "doc": "部门id "}, {"name": "status", "doc": "招聘状态(0:已结束,1:招聘中) "}, {"name": "provincialId", "doc": "省id "}, {"name": "provincialName", "doc": "省名称 "}, {"name": "cityId", "doc": "市id "}, {"name": "cityName", "doc": "市名称 "}, {"name": "jobExperience", "doc": "招聘经验年限 "}, {"name": "jobEducation", "doc": "招聘学历 "}, {"name": "job<PERSON>um", "doc": "招聘人数 "}, {"name": "jobSalary", "doc": "薪资 "}, {"name": "jobFlag", "doc": "招聘标签 "}, {"name": "job<PERSON>ame", "doc": "岗位名称 "}, {"name": "jobType", "doc": "岗位类型 "}, {"name": "jobTypes", "doc": "岗位类型 "}, {"name": "aiStatus", "doc": "是否需要AI面试（Y:是 N:否） "}, {"name": "aiQuestionJson", "doc": "需要AI出的问题 "}, {"name": "reviewStatus", "doc": "审核状态(0未审核1审核通过2未通过)"}, {"name": "isTop", "doc": "是否置顶：0为不置顶 ；其他为置顶  "}, {"name": "nekndCompany", "doc": "企业信息字段 "}, {"name": "internshipJobNum", "doc": "实习岗位,筛选出岗位招聘人员数量大于20人的岗位。"}, {"name": "classificationDepartment", "doc": "学院分类字段 "}, {"name": "jobRequirements", "doc": "学院分类字段 "}, {"name": "companyName", "doc": "招聘企业名称 "}, {"name": "talentsNumber", "doc": "匹配人才数量 "}, {"name": "isCollect", "doc": "该用户是否收藏该岗位 "}, {"name": "priceLow", "doc": "最低薪资 "}, {"name": "priceHigh", "doc": "最高薪资 "}], "enumConstants": [], "methods": [], "constructors": []}