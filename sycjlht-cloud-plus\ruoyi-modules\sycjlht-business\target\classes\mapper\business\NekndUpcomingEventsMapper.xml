<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndUpcomingEventsMapper">
    
    <resultMap type="NekndUpcomingEvents" id="NekndUpcomingEventsResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="newsTitle"    column="news_title"    />
        <result property="newsType"    column="news_type"    />
        <result property="newsContent"    column="news_content"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sourceUri"    column="source_uri"    />
        <result property="sourceTitle"    column="source_title"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
    </resultMap>

    <sql id="selectNekndUpcomingEventsVo">
        select id, cover_uri, news_title, news_type, news_content, status, del_flag, create_by, create_time, update_by, update_time, source_uri, source_title,start_time,end_time from neknd_upcoming_events
    </sql>

    <select id="selectNekndUpcomingEventsList" parameterType="NekndUpcomingEvents" resultMap="NekndUpcomingEventsResult">
        <include refid="selectNekndUpcomingEventsVo"/>
        <where>
            del_flag=0
            <if test="newsTitle != null  and newsTitle != ''"> and news_title like concat('%', #{newsTitle}, '%')</if>
            <if test="newsType != null  and newsType != ''"> and news_type = #{newsType}</if>
            <if test="sourceTitle != null  and sourceTitle != ''"> and source_title like concat('%', #{sourceTitle}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by status asc, create_time desc
    </select>
    
    <select id="selectNekndUpcomingEventsById" parameterType="Integer" resultMap="NekndUpcomingEventsResult">
        <include refid="selectNekndUpcomingEventsVo"/>
        where del_flag=0 and id = #{id}
    </select>
        
    <insert id="insertNekndUpcomingEvents" parameterType="NekndUpcomingEvents" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_upcoming_events
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">cover_uri,</if>
            <if test="newsTitle != null and newsTitle != ''">news_title,</if>
            <if test="newsType != null and newsType != ''">news_type,</if>
            <if test="newsContent != null and newsContent != ''">news_content,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sourceUri != null">source_uri,</if>
            <if test="sourceTitle != null">source_title,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">#{coverUri},</if>
            <if test="newsTitle != null and newsTitle != ''">#{newsTitle},</if>
            <if test="newsType != null and newsType != ''">#{newsType},</if>
            <if test="newsContent != null and newsContent != ''">#{newsContent},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sourceUri != null">#{sourceUri},</if>
            <if test="sourceTitle != null">#{sourceTitle},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
         </trim>
    </insert>

    <update id="updateNekndUpcomingEvents" parameterType="NekndUpcomingEvents">
        update neknd_upcoming_events
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="newsTitle != null and newsTitle != ''">news_title = #{newsTitle},</if>
            <if test="newsType != null and newsType != ''">news_type = #{newsType},</if>
            <if test="newsContent != null and newsContent != ''">news_content = #{newsContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sourceUri != null">source_uri = #{sourceUri},</if>
            <if test="sourceTitle != null">source_title = #{sourceTitle},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndUpcomingEventsById" parameterType="Integer">
        delete from neknd_upcoming_events where id = #{id}
    </delete>

    <delete id="deleteNekndUpcomingEventsByIds" parameterType="String">
        delete from neknd_upcoming_events where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>