package org.dromara.business.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
// import com.alibaba.excel.annotation.ExcelProperty; // TODO: 添加EasyExcel依赖
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 活动预告信对象 neknd_upcoming_events
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
public class NekndUpcomingEvents extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 活动预告ID */
    private Integer id;

    /** 封面图 */
    // @ExcelProperty(value = "封面图") // TODO: 添加EasyExcel依赖
    private String coverUri;

    /** 活动预告标题 */
    // @ExcelProperty(value = "活动预告标题") // TODO: 添加EasyExcel依赖
    private String newsTitle;

    /** 活动预告类型（查字典表） */
    // @ExcelProperty(value = "活动预告类型") // TODO: 添加EasyExcel依赖
    private String newsType;

    /** 活动预告内容 */
    // @ExcelProperty(value = "活动预告内容") // TODO: 添加EasyExcel依赖
    private String newsContent;

    /** 活动预告状态 */
    // @ExcelProperty(value = "活动预告状态") // TODO: 添加EasyExcel依赖
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 来源url */
    // @ExcelProperty(value = "来源url") // TODO: 添加EasyExcel依赖
    private String sourceUri;

    /** 活动预告来源名称 */
    // @ExcelProperty(value = "活动预告来源名称") // TODO: 添加EasyExcel依赖
    private String sourceTitle;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setCoverUri(String coverUri) {
        this.coverUri = coverUri;
    }

    public String getCoverUri() {
        return coverUri;
    }

    public void setNewsTitle(String newsTitle) {
        this.newsTitle = newsTitle;
    }

    public String getNewsTitle() {
        return newsTitle;
    }

    public void setNewsType(String newsType) {
        this.newsType = newsType;
    }

    public String getNewsType() {
        return newsType;
    }

    public void setNewsContent(String newsContent) {
        this.newsContent = newsContent;
    }

    public String getNewsContent() {
        return newsContent;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setSourceUri(String sourceUri) {
        this.sourceUri = sourceUri;
    }

    public String getSourceUri() {
        return sourceUri;
    }

    public void setSourceTitle(String sourceTitle) {
        this.sourceTitle = sourceTitle;
    }

    public String getSourceTitle() {
        return sourceTitle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("coverUri", getCoverUri())
                .append("newsTitle", getNewsTitle())
                .append("newsType", getNewsType())
                .append("newsContent", getNewsContent())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("sourceUri", getSourceUri())
                .append("sourceTitle", getSourceTitle())
                .toString();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
