{"doc": " 类型转换器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "toStr", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": " 转换为字符串<br>\n 如果给定的值为null，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toStr", "paramTypes": ["java.lang.Object"], "doc": " 转换为字符串<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toChar", "paramTypes": ["java.lang.Object", "java.lang.Character"], "doc": " 转换为字符<br>\n 如果给定的值为null，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toChar", "paramTypes": ["java.lang.Object"], "doc": " 转换为字符<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toByte", "paramTypes": ["java.lang.Object", "java.lang.Byte"], "doc": " 转换为byte<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toByte", "paramTypes": ["java.lang.Object"], "doc": " 转换为byte<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toShort", "paramTypes": ["java.lang.Object", "java.lang.Short"], "doc": " 转换为Short<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toShort", "paramTypes": ["java.lang.Object"], "doc": " 转换为Short<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toNumber", "paramTypes": ["java.lang.Object", "java.lang.Number"], "doc": " 转换为Number<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toNumber", "paramTypes": ["java.lang.Object"], "doc": " 转换为Number<br>\n 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toInt", "paramTypes": ["java.lang.Object", "java.lang.Integer"], "doc": " 转换为int<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toInt", "paramTypes": ["java.lang.Object"], "doc": " 转换为int<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toIntArray", "paramTypes": ["java.lang.String"], "doc": " 转换为Integer数组<br>\n\n @param str 被转换的值\n @return 结果\n"}, {"name": "toLongArray", "paramTypes": ["java.lang.String"], "doc": " 转换为Long数组<br>\n\n @param str 被转换的值\n @return 结果\n"}, {"name": "toIntArray", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 转换为Integer数组<br>\n\n @param split 分隔符\n @param split 被转换的值\n @return 结果\n"}, {"name": "toLongArray", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 转换为Long数组<br>\n\n @param split 分隔符\n @param str 被转换的值\n @return 结果\n"}, {"name": "toStrArray", "paramTypes": ["java.lang.String"], "doc": " 转换为String数组<br>\n\n @param str 被转换的值\n @return 结果\n"}, {"name": "toStrArray", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 转换为String数组<br>\n\n @param split 分隔符\n @param split 被转换的值\n @return 结果\n"}, {"name": "toLong", "paramTypes": ["java.lang.Object", "java.lang.Long"], "doc": " 转换为long<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toLong", "paramTypes": ["java.lang.Object"], "doc": " 转换为long<br>\n 如果给定的值为<code>null</code>，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toDouble", "paramTypes": ["java.lang.Object", "java.lang.Double"], "doc": " 转换为double<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toDouble", "paramTypes": ["java.lang.Object"], "doc": " 转换为double<br>\n 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toFloat", "paramTypes": ["java.lang.Object", "java.lang.Float"], "doc": " 转换为Float<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toFloat", "paramTypes": ["java.lang.Object"], "doc": " 转换为Float<br>\n 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toBool", "paramTypes": ["java.lang.Object", "java.lang.Bo<PERSON>an"], "doc": " 转换为boolean<br>\n String支持的值为：true、false、yes、ok、no，1,0 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toBool", "paramTypes": ["java.lang.Object"], "doc": " 转换为boolean<br>\n 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toEnum", "paramTypes": ["java.lang.Class", "java.lang.Object", "java.lang.Enum"], "doc": " 转换为Enum对象<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n\n @param clazz Enum的Class\n @param value 值\n @param defaultValue 默认值\n @return Enum\n"}, {"name": "toEnum", "paramTypes": ["java.lang.Class", "java.lang.Object"], "doc": " 转换为Enum对象<br>\n 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>\n\n @param clazz Enum的Class\n @param value 值\n @return Enum\n"}, {"name": "toBigInteger", "paramTypes": ["java.lang.Object", "java.math.BigInteger"], "doc": " 转换为BigInteger<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toBigInteger", "paramTypes": ["java.lang.Object"], "doc": " 转换为BigInteger<br>\n 如果给定的值为空，或者转换失败，返回默认值<code>null</code><br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "toBigDecimal", "paramTypes": ["java.lang.Object", "java.math.BigDecimal"], "doc": " 转换为BigDecimal<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @param defaultValue 转换错误时的默认值\n @return 结果\n"}, {"name": "toBigDecimal", "paramTypes": ["java.lang.Object"], "doc": " 转换为BigDecimal<br>\n 如果给定的值为空，或者转换失败，返回默认值<br>\n 转换失败不会报错\n\n @param value 被转换的值\n @return 结果\n"}, {"name": "utf8Str", "paramTypes": ["java.lang.Object"], "doc": " 将对象转为字符串<br>\n 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法\n\n @param obj 对象\n @return 字符串\n"}, {"name": "str", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": " 将对象转为字符串<br>\n 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法\n\n @param obj 对象\n @param charsetName 字符集\n @return 字符串\n"}, {"name": "str", "paramTypes": ["java.lang.Object", "java.nio.charset.Charset"], "doc": " 将对象转为字符串<br>\n 1、Byte数组和ByteBuffer会被转换为对应字符串的数组 2、对象数组会调用Arrays.toString方法\n\n @param obj 对象\n @param charset 字符集\n @return 字符串\n"}, {"name": "str", "paramTypes": ["byte[]", "java.lang.String"], "doc": " 将byte数组转为字符串\n\n @param bytes byte数组\n @param charset 字符集\n @return 字符串\n"}, {"name": "str", "paramTypes": ["byte[]", "java.nio.charset.Charset"], "doc": " 解码字节码\n\n @param data 字符串\n @param charset 字符集，如果此字段为空，则解码的结果取决于平台\n @return 解码后的字符串\n"}, {"name": "str", "paramTypes": ["java.nio.ByteBuffer", "java.lang.String"], "doc": " 将编码的byteBuffer数据转换为字符串\n\n @param data 数据\n @param charset 字符集，如果为空使用当前系统字符集\n @return 字符串\n"}, {"name": "str", "paramTypes": ["java.nio.ByteBuffer", "java.nio.charset.Charset"], "doc": " 将编码的byteBuffer数据转换为字符串\n\n @param data 数据\n @param charset 字符集，如果为空使用当前系统字符集\n @return 字符串\n"}, {"name": "toSBC", "paramTypes": ["java.lang.String"], "doc": " 半角转全角\n\n @param input String.\n @return 全角字符串.\n"}, {"name": "toSBC", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": " 半角转全角\n\n @param input String\n @param notConvertSet 不替换的字符集合\n @return 全角字符串.\n"}, {"name": "toDBC", "paramTypes": ["java.lang.String"], "doc": " 全角转半角\n\n @param input String.\n @return 半角字符串\n"}, {"name": "toDBC", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": " 替换全角为半角\n\n @param text 文本\n @param notConvertSet 不替换的字符集合\n @return 替换后的字符\n"}, {"name": "digitUppercase", "paramTypes": ["double"], "doc": " 数字金额大写转换 先写个完整的然后将如零拾替换成零\n\n @param n 数字\n @return 中文大写数字\n"}], "constructors": []}