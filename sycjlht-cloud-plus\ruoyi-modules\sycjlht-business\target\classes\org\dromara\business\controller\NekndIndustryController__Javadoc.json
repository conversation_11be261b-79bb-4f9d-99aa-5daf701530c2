{"doc": " 行业管理Controller\n \n <AUTHOR>\n @date 2024-12-23\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndIndustry", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询行业管理列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndIndustry"], "doc": " 导出行业管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取行业管理详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndIndustry"], "doc": " 新增行业管理\n"}, {"name": "update", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "java.lang.String"], "doc": " 修改行业管理（文件上传更新）\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除行业管理\n"}, {"name": "getProfessionalGroupsAccount", "paramTypes": ["java.lang.Long"], "doc": " 查询专业群占比数据\n"}, {"name": "getTextMessage", "paramTypes": ["java.lang.Long"], "doc": " 查询文本信息\n"}, {"name": "getProportionTeachers", "paramTypes": ["java.lang.Long"], "doc": " 查询教师比例数据\n"}, {"name": "getValueChangeTrend", "paramTypes": ["java.lang.Long"], "doc": " 查询产值变化趋势\n"}, {"name": "getPolicyNumber", "paramTypes": ["java.lang.Long"], "doc": " 查询政策支持数量\n"}, {"name": "getSchoolCompanyCooperation", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询校企合作项目\n"}, {"name": "getJobRequirements", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询岗位要求\n"}, {"name": "getCooperationStrength", "paramTypes": ["java.lang.Long"], "doc": " 查询合作强度数据\n"}], "constructors": []}