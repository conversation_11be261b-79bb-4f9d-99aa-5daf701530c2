{"doc": " 聊天SSE控制器\n", "fields": [], "enumConstants": [], "methods": [{"name": "chatConversation", "paramTypes": ["java.util.Map"], "doc": " 处理非流式聊天请求的改进API端点\n 使用POST方法，路径为\"/chat/conversation\"\n 接收Map<String, String>作为请求体，支持多轮对话\n 该方法使用阻塞式(非流式)方式与Dify API通信，并支持会话上下文的持久化\n\n 请求参数说明:\n - sessionId: 用户会话ID，用于标识用户，必填\n - content: 用户当前消息内容，必填\n - conversationId: 会话ID，用于继续之前的对话，如果为空则创建新会话\n \n 响应数据说明:\n - content: AI回复的内容\n - message_id: 消息唯一ID\n - conversation_id: 会话ID，用于后续对话\n - metadata: 元数据，包含使用量信息等\n\n @param params 包含会话参数的Map对象\n @return R 包含AI回复内容、消息ID和会话ID的响应对象\n"}], "constructors": []}