<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndJuniorcollegeUpgradeUndergraduateRecordsMapper">
    
    <resultMap type="NekndJuniorcollegeUpgradeUndergraduateRecords" id="NekndJuniorcollegeUpgradeUndergraduateRecordsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="upgradeId"    column="upgrade_id"    />
        <result property="upgradeTitle"    column="upgrade_title"    />
    </resultMap>

    <sql id="selectNekndJuniorcollegeUpgradeUndergraduateRecordsVo">
        select id, user_id, name, phone, del_flag, create_by, create_time, update_by, update_time, upgrade_id, upgrade_title from neknd_juniorcollege_upgrade_undergraduate_records
    </sql>

    <select id="selectNekndJuniorcollegeUpgradeUndergraduateRecordsList" parameterType="NekndJuniorcollegeUpgradeUndergraduateRecords" resultMap="NekndJuniorcollegeUpgradeUndergraduateRecordsResult">
        <include refid="selectNekndJuniorcollegeUpgradeUndergraduateRecordsVo"/>
        <where>
            del_flag= 0
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="upgradeId != null  and upgradeId != ''"> and upgrade_id = #{upgradeId}</if>
            <if test="upgradeTitle != null  and upgradeTitle != ''"> and upgrade_title like concat('%', #{upgradeTitle}, '%')</if>
        </where>
    </select>
    
    <select id="selectNekndJuniorcollegeUpgradeUndergraduateRecordsById" parameterType="Long" resultMap="NekndJuniorcollegeUpgradeUndergraduateRecordsResult">
        <include refid="selectNekndJuniorcollegeUpgradeUndergraduateRecordsVo"/>
        where del_flag=0 and id = #{id}
    </select>
        
    <insert id="insertNekndJuniorcollegeUpgradeUndergraduateRecords" parameterType="NekndJuniorcollegeUpgradeUndergraduateRecords" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_juniorcollege_upgrade_undergraduate_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="upgradeId != null">upgrade_id,</if>
            <if test="upgradeTitle != null">upgrade_title,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="upgradeId != null">#{upgradeId},</if>
            <if test="upgradeTitle != null">#{upgradeTitle},</if>
         </trim>
    </insert>

    <update id="updateNekndJuniorcollegeUpgradeUndergraduateRecords" parameterType="NekndJuniorcollegeUpgradeUndergraduateRecords">
        update neknd_juniorcollege_upgrade_undergraduate_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
<!--            <if test="upgradeId != null">upgrade_id = #{upgradeId},</if>-->
<!--            <if test="upgradeTitle != null">upgrade_title = #{upgradeTitle},</if>-->
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndJuniorcollegeUpgradeUndergraduateRecordsById" parameterType="Long">
        delete from neknd_juniorcollege_upgrade_undergraduate_records where id = #{id}
    </delete>

    <delete id="deleteNekndJuniorcollegeUpgradeUndergraduateRecordsByIds" parameterType="String">
        delete from neknd_juniorcollege_upgrade_undergraduate_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>