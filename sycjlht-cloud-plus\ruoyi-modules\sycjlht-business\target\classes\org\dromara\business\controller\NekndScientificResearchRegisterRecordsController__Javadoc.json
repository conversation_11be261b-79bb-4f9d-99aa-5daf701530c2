{"doc": " 科普研学报名记录Controller\n \n <AUTHOR>\n @date 2024-08-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询科普研学报名记录列表\n"}, {"name": "scientificResearchStudents", "paramTypes": ["java.lang.Integer", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据科普研学id查看报名该科普研学的人员信息\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": " 导出科普研学报名记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取科普研学报名记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": " 新增科普研学报名记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": " 修改科普研学报名记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除科普研学报名记录\n"}, {"name": "getMyRegistrations", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前用户的报名记录\n"}, {"name": "cancelRegistration", "paramTypes": ["java.lang.Long"], "doc": " 取消报名\n"}], "constructors": []}