<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndMessageTopicMapper">
    
    <resultMap type="NekndMessageTopic" id="NekndMessageTopicResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="recipientUserId"    column="recipient_user_id"    />
        <result property="recipientUserName"    column="recipient_user_name"    />
        <result property="recipientDeptId"    column="recipient_dept_id"    />
        <result property="avatar"    column="avatar"    />
        <result property="title"    column="title"    />
        <result property="readStatus"    column="read_status"    />
        <result property="senderUserId"    column="sender_user_id"    />
        <result property="senderUserName"    column="sender_user_name"    />
        <result property="senderDeptId"    column="sender_dept_id"    />
        <result property="recipientAvatar"    column="recipient_avatar"    />
        <result property="senderDelFlag"    column="sender_del_flag"    />
        <result property="recipientDelFlag"    column="recipient_del_flag"    />
    </resultMap>

    <sql id="selectNekndMessageTopicVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, status, recipient_user_id, recipient_user_name, recipient_dept_id, avatar, title, read_status, sender_user_id, sender_user_name, sender_dept_id,recipient_avatar,sender_del_flag,recipient_del_flag from neknd_message_topic
    </sql>

    <select id="selectNekndMessageTopicList" parameterType="NekndMessageTopic" resultMap="NekndMessageTopicResult">
        <include refid="selectNekndMessageTopicVo"/>
        <where>  
            <if test="status != null  and status != ''"> and status = #{status}</if>
<!--            <if test="recipientUserId != null "> and recipient_user_id = #{recipientUserId}</if>-->
            <if test="recipientUserName != null  and recipientUserName != ''"> and recipient_user_name like concat('%', #{recipientUserName}, '%')</if>
            <if test="recipientDeptId != null "> and recipient_dept_id = #{recipientDeptId}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="readStatus != null  and readStatus != ''"> and read_status = #{readStatus}</if>
            <if test="senderUserId != null or recipientUserId != null ">
              <trim prefix="and (" suffix=")">
                <if test="recipientUserId != null "> recipient_user_id = #{recipientUserId}</if>
                <if test="senderUserId != null "> or sender_user_id = #{senderUserId}</if>
              </trim>
            </if>
<!--            <if test="senderUserId != null "> or sender_user_id = #{senderUserId}</if>-->
            <if test="senderUserName != null  and senderUserName != ''"> and sender_user_name like concat('%', #{senderUserName}, '%')</if>
            <if test="senderDeptId != null "> and sender_dept_id = #{senderDeptId}</if>
            <if test="senderDelFlag != null "> and sender_del_flag = #{senderDelFlag}</if>
            <if test="recipientDelFlag != null "> and recipient_del_flag = #{recipientDelFlag}</if>
            <!-- 添加过滤条件 只能看到当前用户 -->
            <if test="currentUserId != null">
                and not (sender_user_id = #{currentUserId} and sender_del_flag = '2')
            </if>
            <if test="currentUserId != null">
                and not (recipient_user_id = #{currentUserId} and recipient_del_flag = '2')
            </if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectNekndMessageTopicById" parameterType="Integer" resultMap="NekndMessageTopicResult">
        <include refid="selectNekndMessageTopicVo"/>
        where id = #{id}
    </select>
    <select id="getCountMessageTopic" resultType="java.lang.Integer">
        select count(1) from neknd_message_topic
        <where>
            <trim prefix="and (" suffix=")">
                <if test="recipientUserId != null "> recipient_user_id = #{recipientUserId}</if>
                <if test="senderUserId != null "> or sender_user_id = #{senderUserId}</if>
            </trim>
        </where>

    </select>

    <insert id="insertNekndMessageTopic" parameterType="NekndMessageTopic" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into neknd_message_topic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="recipientUserId != null">recipient_user_id,</if>
            <if test="recipientUserName != null">recipient_user_name,</if>
            <if test="recipientDeptId != null">recipient_dept_id,</if>
            <if test="avatar != null">avatar,</if>
            <if test="title != null">title,</if>
            <if test="readStatus != null">read_status,</if>
            <if test="senderUserId != null">sender_user_id,</if>
            <if test="senderUserName != null">sender_user_name,</if>
            <if test="senderDeptId != null">sender_dept_id,</if>
            <if test="recipientAvatar != null">recipient_avatar,</if>
            <if test="senderDelFlag != null">sender_del_flag,</if>
            <if test="recipientDelFlag != null">recipient_del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="recipientUserId != null">#{recipientUserId},</if>
            <if test="recipientUserName != null">#{recipientUserName},</if>
            <if test="recipientDeptId != null">#{recipientDeptId},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="title != null">#{title},</if>
            <if test="readStatus != null">#{readStatus},</if>
            <if test="senderUserId != null">#{senderUserId},</if>
            <if test="senderUserName != null">#{senderUserName},</if>
            <if test="senderDeptId != null">#{senderDeptId},</if>
            <if test="recipientAvatar != null">#{recipientAvatar},</if>
            <if test="senderDelFlag != null">#{senderDelFlag},</if>
            <if test="recipientDelFlag != null">#{recipientDelFlag},</if>
         </trim>
    </insert>

    <update id="updateNekndMessageTopic" parameterType="NekndMessageTopic">
        update neknd_message_topic
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="recipientUserId != null">recipient_user_id = #{recipientUserId},</if>
            <if test="recipientUserName != null">recipient_user_name = #{recipientUserName},</if>
            <if test="recipientDeptId != null">recipient_dept_id = #{recipientDeptId},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="title != null">title = #{title},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="senderUserId != null">sender_user_id = #{senderUserId},</if>
            <if test="senderUserName != null">sender_user_name = #{senderUserName},</if>
            <if test="senderDeptId != null">sender_dept_id = #{senderDeptId},</if>
            <if test="recipientAvatar != null">recipient_avatar = #{recipientAvatar},</if>
            <if test="senderDelFlag != null">sender_del_flag = #{senderDelFlag},</if>
            <if test="recipientDelFlag != null">recipient_del_flag = #{recipientDelFlag},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateReadStatusById">
        update neknd_message_topic set read_status = 1 where del_flag=0 and id = #{id}
    </update>
    <update id="updateSenderReadStatusById">
        update neknd_message_topic set read_status = 1 where del_flag=0 and id = #{id}
    </update>

    <delete id="deleteNekndMessageTopicById" parameterType="Integer">
        delete from neknd_message_topic where id = #{id}
    </delete>

    <delete id="deleteNekndMessageTopicByIds" parameterType="String">
        delete from neknd_message_topic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>