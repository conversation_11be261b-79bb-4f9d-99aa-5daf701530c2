{"doc": " 【请填写功能名称】Service业务层处理\n\n <AUTHOR>\n @date 2024-12-25\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryProfessionalGroupsAccountByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustryProfessionalGroupsAccountList", "paramTypes": ["org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\n @return 【请填写功能名称】\n"}, {"name": "insertNekndIndustryProfessionalGroupsAccount", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustryProfessionalGroupsAccount", "paramTypes": ["org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustryProfessionalGroupsAccountByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industryIds 需要删除的【请填写功能名称】主键\n @return 结果\n"}, {"name": "deleteNekndIndustryProfessionalGroupsAccountByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】信息\n\n @param industryId 【请填写功能名称】主键\n @return 结果\n"}], "constructors": []}