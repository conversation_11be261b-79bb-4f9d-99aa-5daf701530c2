{"doc": " 咸宁对接的数据的同步日志Service业务层处理\n\n <AUTHOR>\n @date 2025-07-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndDataSyncRecordById", "paramTypes": ["java.lang.Long"], "doc": " 查询咸宁对接的数据的同步日志\n\n @param id 咸宁对接的数据的同步日志主键\n @return 咸宁对接的数据的同步日志\n"}, {"name": "selectNekndDataSyncRecordList", "paramTypes": ["org.dromara.business.domain.NekndDataSyncRecord"], "doc": " 查询咸宁对接的数据的同步日志列表\n\n @param nekndDataSyncRecord 咸宁对接的数据的同步日志\n @return 咸宁对接的数据的同步日志\n"}, {"name": "insertNekndDataSyncRecord", "paramTypes": ["org.dromara.business.domain.NekndDataSyncRecord"], "doc": " 新增咸宁对接的数据的同步日志\n\n @param nekndDataSyncRecord 咸宁对接的数据的同步日志\n @return 结果\n"}, {"name": "updateNekndDataSyncRecord", "paramTypes": ["org.dromara.business.domain.NekndDataSyncRecord"], "doc": " 修改咸宁对接的数据的同步日志\n\n @param nekndDataSyncRecord 咸宁对接的数据的同步日志\n @return 结果\n"}, {"name": "deleteNekndDataSyncRecordByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除咸宁对接的数据的同步日志\n\n @param ids 需要删除的咸宁对接的数据的同步日志主键\n @return 结果\n"}, {"name": "deleteNekndDataSyncRecordById", "paramTypes": ["java.lang.Long"], "doc": " 删除咸宁对接的数据的同步日志信息\n\n @param id 咸宁对接的数据的同步日志主键\n @return 结果\n"}], "constructors": []}