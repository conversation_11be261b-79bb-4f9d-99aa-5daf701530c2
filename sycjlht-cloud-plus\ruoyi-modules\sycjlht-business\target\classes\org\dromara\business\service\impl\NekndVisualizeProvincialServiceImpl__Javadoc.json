{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getDynamicMonitoringAllIndicatorResult", "paramTypes": ["java.util.List"], "doc": " 获取市域动态监测结果\n"}, {"name": "getParkDistribution", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 获取园区分布及产值结果\n"}, {"name": "getProvincialPolicyLandingResult", "paramTypes": ["java.util.List"], "doc": " 政策落地\n"}, {"name": "getKeyLeadingEnterpriseResult", "paramTypes": ["java.util.List"], "doc": " 重点企业排名\n"}, {"name": "getProvincialLevelSchoolResult", "paramTypes": ["java.util.List"], "doc": " 获取各市水平院校数量分布\n"}, {"name": "getProvincialTalentTrainingConversionRateResutl", "paramTypes": ["java.util.List"], "doc": " 贯通人才培养转化率\n"}, {"name": "getSkillCertificate", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 技能证书\n"}, {"name": "getKeyHelpListResult", "paramTypes": ["java.util.List"], "doc": " 重点帮扶清单\n"}], "constructors": []}