{"doc": " 项目需求Service接口\n\n <AUTHOR>\n @date 2024-05-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSupplyDemandById", "paramTypes": ["java.lang.Integer"], "doc": " 查询项目需求\n\n @param id 项目需求主键\n @return 项目需求\n"}, {"name": "selectNekndSupplyDemandList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": " 查询项目需求列表\n\n @param nekndSupplyDemand 项目需求\n @return 项目需求集合\n"}, {"name": "selectDemandList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": " 后台查询项目需求列表\n\n @param nekndSupplyDemand 项目需求\n @return 项目需求集合\n"}, {"name": "insertNekndSupplyDemand", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": " 新增项目需求\n\n @param nekndSupplyDemand 项目需求\n @return 结果\n"}, {"name": "updateNekndSupplyDemand", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": " 修改项目需求\n\n @param nekndSupplyDemand 项目需求\n @return 结果\n"}, {"name": "deleteNekndSupplyDemandByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除项目需求\n\n @param ids 需要删除的项目需求主键集合\n @return 结果\n"}, {"name": "deleteNekndSupplyDemandById", "paramTypes": ["java.lang.Integer"], "doc": " 删除项目需求信息\n\n @param id 项目需求主键\n @return 结果\n"}, {"name": "upDockingtatus", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 根据需求id修改对接状态(0:待对接,1:已对接,2:已结束)\n\n"}, {"name": "selectAuditDemandList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": " 查询审核项目需求列表\n @param nekndSupplyDemand\n @return\n"}], "constructors": []}