{"doc": " 课程Service业务层处理\n\n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesByCourseId", "paramTypes": ["java.lang.Integer"], "doc": " 查询课程\n\n @param courseId 课程主键\n @return 课程\n"}, {"name": "selectNekndCoursesList", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": " 查询课程列表\n\n @param nekndCourses 课程\n @return 课程\n"}, {"name": "insertNekndCourses", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": " 新增课程\n\n @param nekndCourses 课程\n @return 结果\n"}, {"name": "updateNekndCourses", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": " 修改课程\n\n @param nekndCourses 课程\n @return 结果\n"}, {"name": "deleteNekndCoursesByCourseIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除课程\n\n @param courseIds 需要删除的课程主键\n @return 结果\n"}, {"name": "deleteNekndCoursesByCourseId", "paramTypes": ["java.lang.Integer"], "doc": " 删除课程信息\n\n @param courseId 课程主键\n @return 结果\n"}], "constructors": []}