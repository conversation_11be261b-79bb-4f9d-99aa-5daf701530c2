<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndPortraitTalentMapper">
    
    <resultMap type="NekndPortraitTalent" id="NekndPortraitTalentResult">
        <result property="id"    column="id"    />
        <result property="personId"    column="person_id"    />
        <result property="age"    column="age"    />
        <result property="technicalCapacityInfo"    column="technical_capacity_info"    />
        <result property="technicalCapacityDetail"    column="technical_capacity_detail"    />
        <result property="technicalCapacityGrade"    column="technical_capacity_grade"    />
        <result property="qualityStabilityInfo"    column="quality_stability_info"    />
        <result property="qualityStabilityDetail"    column="quality_stability_detail"    />
        <result property="qualityStabilityGrade"    column="quality_stability_grade"    />
        <result property="iqInfo"    column="iq_info"    />
        <result property="iqDetail"    column="iq_detail"    />
        <result property="iqGrade"    column="iq_grade"    />
        <result property="eqInfo"    column="eq_info"    />
        <result property="eqDetail"    column="eq_detail"    />
        <result property="eqGrade"    column="eq_grade"    />
        <result property="behavioralHabitInfo"    column="behavioral_habit_info"    />
        <result property="behavioralHabitDetail"    column="behavioral_habit_detail"    />
        <result property="behavioralHabitGrade"    column="behavioral_habit_grade"    />
        <result property="moralSentimentsInfo"    column="moral_sentiments_info"    />
        <result property="moralSentimentsDetail"    column="moral_sentiments_detail"    />
        <result property="moralSentimentsGrade"    column="moral_sentiments_grade"    />
        <result property="merit"    column="merit"    />
        <result property="deficiency"    column="deficiency"    />
        <result property="comprehensiveGrade"    column="comprehensive_grade"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNekndPortraitTalentVo">
        select id, person_id, age, technical_capacity_info, technical_capacity_detail, technical_capacity_grade, quality_stability_info, quality_stability_detail, quality_stability_grade, iq_info, iq_detail, iq_grade, eq_info, eq_detail, eq_grade, behavioral_habit_info, behavioral_habit_detail, behavioral_habit_grade, moral_sentiments_info, moral_sentiments_detail, moral_sentiments_grade, merit, deficiency, comprehensive_grade, del_flag, create_time, update_time from neknd_portrait_talent
    </sql>

    <select id="selectNekndPortraitTalentList" parameterType="NekndPortraitTalent" resultMap="NekndPortraitTalentResult">
        <include refid="selectNekndPortraitTalentVo"/>
        <where>  
            <if test="personId != null "> and person_id = #{personId}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="technicalCapacityInfo != null  and technicalCapacityInfo != ''"> and technical_capacity_info = #{technicalCapacityInfo}</if>
            <if test="technicalCapacityDetail != null  and technicalCapacityDetail != ''"> and technical_capacity_detail = #{technicalCapacityDetail}</if>
            <if test="technicalCapacityGrade != null  and technicalCapacityGrade != ''"> and technical_capacity_grade = #{technicalCapacityGrade}</if>
            <if test="qualityStabilityInfo != null  and qualityStabilityInfo != ''"> and quality_stability_info = #{qualityStabilityInfo}</if>
            <if test="qualityStabilityDetail != null  and qualityStabilityDetail != ''"> and quality_stability_detail = #{qualityStabilityDetail}</if>
            <if test="qualityStabilityGrade != null  and qualityStabilityGrade != ''"> and quality_stability_grade = #{qualityStabilityGrade}</if>
            <if test="iqInfo != null  and iqInfo != ''"> and iq_info = #{iqInfo}</if>
            <if test="iqDetail != null  and iqDetail != ''"> and iq_detail = #{iqDetail}</if>
            <if test="iqGrade != null  and iqGrade != ''"> and iq_grade = #{iqGrade}</if>
            <if test="eqInfo != null  and eqInfo != ''"> and eq_info = #{eqInfo}</if>
            <if test="eqDetail != null  and eqDetail != ''"> and eq_detail = #{eqDetail}</if>
            <if test="eqGrade != null  and eqGrade != ''"> and eq_grade = #{eqGrade}</if>
            <if test="behavioralHabitInfo != null  and behavioralHabitInfo != ''"> and behavioral_habit_info = #{behavioralHabitInfo}</if>
            <if test="behavioralHabitDetail != null  and behavioralHabitDetail != ''"> and behavioral_habit_detail = #{behavioralHabitDetail}</if>
            <if test="behavioralHabitGrade != null  and behavioralHabitGrade != ''"> and behavioral_habit_grade = #{behavioralHabitGrade}</if>
            <if test="moralSentimentsInfo != null  and moralSentimentsInfo != ''"> and moral_sentiments_info = #{moralSentimentsInfo}</if>
            <if test="moralSentimentsDetail != null  and moralSentimentsDetail != ''"> and moral_sentiments_detail = #{moralSentimentsDetail}</if>
            <if test="moralSentimentsGrade != null  and moralSentimentsGrade != ''"> and moral_sentiments_grade = #{moralSentimentsGrade}</if>
            <if test="merit != null  and merit != ''"> and merit = #{merit}</if>
            <if test="deficiency != null  and deficiency != ''"> and deficiency = #{deficiency}</if>
            <if test="comprehensiveGrade != null  and comprehensiveGrade != ''"> and comprehensive_grade = #{comprehensiveGrade}</if>
        </where>
    </select>

    <select id="selectPortraitTalentList" parameterType="NekndPortraitTalent" resultType="map">
        select
        t.person_id as personId,
        p.name as name,
        t.age as age,
        t.technical_capacity_info as technicalCapacityInfo,
        t.technical_capacity_detail as technicalCapacityDetail,
        t.technical_capacity_grade as technicalCapacityGrade,
        t.quality_stability_info as qualityStabilityInfo,
        t.quality_stability_detail as qualityStabilityDetail,
        t.quality_stability_grade as qualityStabilityGrade,
        t.iq_info as iqInfo,
        t.iq_detail as iqDetail,
        t.iq_grade as iqGrade,
        t.eq_info as eqInfo,
        t.eq_detail as eqDetail,
        t.eq_grade as eqGrade,
        t.behavioral_habit_info as behavioralHabitInfo,
        t.behavioral_habit_detail as behavioralHabitDetail,
        t.behavioral_habit_grade as behavioralHabitGrade,
        t.moral_sentiments_info as moralSentimentsInfo,
        t.moral_sentiments_detail as moralSentimentsDetail,
        t.moral_sentiments_grade as moralSentimentsGrade,
        t.merit as merit,
        t.deficiency as deficiency,
        t.comprehensive_grade as comprehensiveGrade
        FROM
        neknd_person AS p
        RIGHT JOIN
        neknd_portrait_talent AS t
        ON
        p.id = t.person_id
        WHERE
        p.del_flag = 0
        AND t.del_flag = 0
        <where>
            <if test="personId != null "> and person_id = #{personId}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="technicalCapacityInfo != null  and technicalCapacityInfo != ''"> and technical_capacity_info = #{technicalCapacityInfo}</if>
            <if test="technicalCapacityDetail != null  and technicalCapacityDetail != ''"> and technical_capacity_detail = #{technicalCapacityDetail}</if>
            <if test="technicalCapacityGrade != null  and technicalCapacityGrade != ''"> and technical_capacity_grade = #{technicalCapacityGrade}</if>
            <if test="qualityStabilityInfo != null  and qualityStabilityInfo != ''"> and quality_stability_info = #{qualityStabilityInfo}</if>
            <if test="qualityStabilityDetail != null  and qualityStabilityDetail != ''"> and quality_stability_detail = #{qualityStabilityDetail}</if>
            <if test="qualityStabilityGrade != null  and qualityStabilityGrade != ''"> and quality_stability_grade = #{qualityStabilityGrade}</if>
            <if test="iqInfo != null  and iqInfo != ''"> and iq_info = #{iqInfo}</if>
            <if test="iqDetail != null  and iqDetail != ''"> and iq_detail = #{iqDetail}</if>
            <if test="iqGrade != null  and iqGrade != ''"> and iq_grade = #{iqGrade}</if>
            <if test="eqInfo != null  and eqInfo != ''"> and eq_info = #{eqInfo}</if>
            <if test="eqDetail != null  and eqDetail != ''"> and eq_detail = #{eqDetail}</if>
            <if test="eqGrade != null  and eqGrade != ''"> and eq_grade = #{eqGrade}</if>
            <if test="behavioralHabitInfo != null  and behavioralHabitInfo != ''"> and behavioral_habit_info = #{behavioralHabitInfo}</if>
            <if test="behavioralHabitDetail != null  and behavioralHabitDetail != ''"> and behavioral_habit_detail = #{behavioralHabitDetail}</if>
            <if test="behavioralHabitGrade != null  and behavioralHabitGrade != ''"> and behavioral_habit_grade = #{behavioralHabitGrade}</if>
            <if test="moralSentimentsInfo != null  and moralSentimentsInfo != ''"> and moral_sentiments_info = #{moralSentimentsInfo}</if>
            <if test="moralSentimentsDetail != null  and moralSentimentsDetail != ''"> and moral_sentiments_detail = #{moralSentimentsDetail}</if>
            <if test="moralSentimentsGrade != null  and moralSentimentsGrade != ''"> and moral_sentiments_grade = #{moralSentimentsGrade}</if>
            <if test="merit != null  and merit != ''"> and merit = #{merit}</if>
            <if test="deficiency != null  and deficiency != ''"> and deficiency = #{deficiency}</if>
            <if test="comprehensiveGrade != null  and comprehensiveGrade != ''"> and comprehensive_grade = #{comprehensiveGrade}</if>
        </where>
    </select>
    
    <select id="selectNekndPortraitTalentById" parameterType="Integer" resultMap="NekndPortraitTalentResult">
        <include refid="selectNekndPortraitTalentVo"/>
        where person_id = #{id}
    </select>

    <select id="selectPortraitTalentInfoById" parameterType="Integer" resultType="map">
        SELECT
            p.id as id,
            p.user_id as userId,
            p.picture_uri as pictureUri,
            p.name as name,
            p.sex as sex,
            p.address as address,
            p.phone as phone,
            p.email as email,
            p.work_year as workYear,
            p.position_status as positionStatus,
            p.technical as technical,
            p.expected_position as expectedPosition,
            p.expected_money as expectedMoney,
            p.work_experience_json as workExperienceJson,
            p.educational_background_json as educationalBackgroundJson,
            p.del_flag as delFlag,
            p.create_by as createBy,
            p.create_time as createTime,
            p.update_by as updateBy,
            p.update_time as updateTime,
            p.remark as remark,
            p.company_dept_id as companyDeptId,
            p.company_dept_name as companyDeptName,
            p.school_dept_id as schoolDeptId,
            p.school_dept_name as schoolDeptName,
            p.classify as classify,
            p.status as status,
            p.acceptance_status as acceptanceStatus,
            p.job_type as jobType,
            p.provincial_id as provincialId,
            p.provincial_name as provincialName,
            p.city_id as cityId,
            p.city_name as cityName,
            p.education_status as educationStatus,
            p.update_status as updateStatus,
            p.certificate_honor_uri as certificateHonorUri,
            p.pending_approval_company_dept_id as pendingApprovalCompanyDeptId,
            p.pending_approval_school_dept_id as pendingApprovalSchoolDeptId,
            p.pending_classify as pendingClassify,
            p.appointment as appointment,
            p.personal_experience as personalExperience,
            p.professional_titles as professionalTitles,
            p.credentials_of_experts_or_talents as credentialsOfExpertsOrTalents,
            p.resume as resume,
            p.appointment_time as appointmentTime,
            p.is_top as isTop,
            t.age as age,
            t.technical_capacity_info as technicalCapacityInfo,
            t.technical_capacity_detail as technicalCapacityDetail,
            t.technical_capacity_grade as technicalCapacityGrade,
            t.quality_stability_info as qualityStabilityInfo,
            t.quality_stability_detail as qualityStabilityDetail,
            t.quality_stability_grade as qualityStabilityGrade,
            t.iq_info as iqInfo,
            t.iq_detail as iqDetail,
            t.iq_grade as iqGrade,
            t.eq_info as eqInfo,
            t.eq_detail as eqDetail,
            t.eq_grade as eqGrade,
            t.behavioral_habit_info as behavioralHabitInfo,
            t.behavioral_habit_detail as behavioralHabitDetail,
            t.behavioral_habit_grade as behavioralHabitGrade,
            t.moral_sentiments_info as moralSentimentsInfo,
            t.moral_sentiments_detail as moralSentimentsDetail,
            t.moral_sentiments_grade as moralSentimentsGrade,
            t.merit as merit,
            t.deficiency as deficiency,
            t.comprehensive_grade as comprehensiveGrade
        FROM
            neknd_person AS p
                LEFT JOIN
            neknd_portrait_talent AS t
            ON
                p.id = t.person_id
        WHERE
            t.person_id = #{id}
          AND p.del_flag = 0
          AND t.del_flag = 0;
    </select>

    <insert id="insertNekndPortraitTalent" parameterType="NekndPortraitTalent" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_portrait_talent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personId != null">person_id,</if>
            <if test="age != null">age,</if>
            <if test="technicalCapacityInfo != null">technical_capacity_info,</if>
            <if test="technicalCapacityDetail != null">technical_capacity_detail,</if>
            <if test="technicalCapacityGrade != null">technical_capacity_grade,</if>
            <if test="qualityStabilityInfo != null">quality_stability_info,</if>
            <if test="qualityStabilityDetail != null">quality_stability_detail,</if>
            <if test="qualityStabilityGrade != null">quality_stability_grade,</if>
            <if test="iqInfo != null">iq_info,</if>
            <if test="iqDetail != null">iq_detail,</if>
            <if test="iqGrade != null">iq_grade,</if>
            <if test="eqInfo != null">eq_info,</if>
            <if test="eqDetail != null">eq_detail,</if>
            <if test="eqGrade != null">eq_grade,</if>
            <if test="behavioralHabitInfo != null">behavioral_habit_info,</if>
            <if test="behavioralHabitDetail != null">behavioral_habit_detail,</if>
            <if test="behavioralHabitGrade != null">behavioral_habit_grade,</if>
            <if test="moralSentimentsInfo != null">moral_sentiments_info,</if>
            <if test="moralSentimentsDetail != null">moral_sentiments_detail,</if>
            <if test="moralSentimentsGrade != null">moral_sentiments_grade,</if>
            <if test="merit != null">merit,</if>
            <if test="deficiency != null">deficiency,</if>
            <if test="comprehensiveGrade != null">comprehensive_grade,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personId != null">#{personId},</if>
            <if test="age != null">#{age},</if>
            <if test="technicalCapacityInfo != null">#{technicalCapacityInfo},</if>
            <if test="technicalCapacityDetail != null">#{technicalCapacityDetail},</if>
            <if test="technicalCapacityGrade != null">#{technicalCapacityGrade},</if>
            <if test="qualityStabilityInfo != null">#{qualityStabilityInfo},</if>
            <if test="qualityStabilityDetail != null">#{qualityStabilityDetail},</if>
            <if test="qualityStabilityGrade != null">#{qualityStabilityGrade},</if>
            <if test="iqInfo != null">#{iqInfo},</if>
            <if test="iqDetail != null">#{iqDetail},</if>
            <if test="iqGrade != null">#{iqGrade},</if>
            <if test="eqInfo != null">#{eqInfo},</if>
            <if test="eqDetail != null">#{eqDetail},</if>
            <if test="eqGrade != null">#{eqGrade},</if>
            <if test="behavioralHabitInfo != null">#{behavioralHabitInfo},</if>
            <if test="behavioralHabitDetail != null">#{behavioralHabitDetail},</if>
            <if test="behavioralHabitGrade != null">#{behavioralHabitGrade},</if>
            <if test="moralSentimentsInfo != null">#{moralSentimentsInfo},</if>
            <if test="moralSentimentsDetail != null">#{moralSentimentsDetail},</if>
            <if test="moralSentimentsGrade != null">#{moralSentimentsGrade},</if>
            <if test="merit != null">#{merit},</if>
            <if test="deficiency != null">#{deficiency},</if>
            <if test="comprehensiveGrade != null">#{comprehensiveGrade},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNekndPortraitTalent" parameterType="NekndPortraitTalent">
        update neknd_portrait_talent
        <trim prefix="SET" suffixOverrides=",">
            <if test="personId != null">person_id = #{personId},</if>
            <if test="age != null">age = #{age},</if>
            <if test="technicalCapacityInfo != null">technical_capacity_info = #{technicalCapacityInfo},</if>
            <if test="technicalCapacityDetail != null">technical_capacity_detail = #{technicalCapacityDetail},</if>
            <if test="technicalCapacityGrade != null">technical_capacity_grade = #{technicalCapacityGrade},</if>
            <if test="qualityStabilityInfo != null">quality_stability_info = #{qualityStabilityInfo},</if>
            <if test="qualityStabilityDetail != null">quality_stability_detail = #{qualityStabilityDetail},</if>
            <if test="qualityStabilityGrade != null">quality_stability_grade = #{qualityStabilityGrade},</if>
            <if test="iqInfo != null">iq_info = #{iqInfo},</if>
            <if test="iqDetail != null">iq_detail = #{iqDetail},</if>
            <if test="iqGrade != null">iq_grade = #{iqGrade},</if>
            <if test="eqInfo != null">eq_info = #{eqInfo},</if>
            <if test="eqDetail != null">eq_detail = #{eqDetail},</if>
            <if test="eqGrade != null">eq_grade = #{eqGrade},</if>
            <if test="behavioralHabitInfo != null">behavioral_habit_info = #{behavioralHabitInfo},</if>
            <if test="behavioralHabitDetail != null">behavioral_habit_detail = #{behavioralHabitDetail},</if>
            <if test="behavioralHabitGrade != null">behavioral_habit_grade = #{behavioralHabitGrade},</if>
            <if test="moralSentimentsInfo != null">moral_sentiments_info = #{moralSentimentsInfo},</if>
            <if test="moralSentimentsDetail != null">moral_sentiments_detail = #{moralSentimentsDetail},</if>
            <if test="moralSentimentsGrade != null">moral_sentiments_grade = #{moralSentimentsGrade},</if>
            <if test="merit != null">merit = #{merit},</if>
            <if test="deficiency != null">deficiency = #{deficiency},</if>
            <if test="comprehensiveGrade != null">comprehensive_grade = #{comprehensiveGrade},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndPortraitTalentById" parameterType="Integer">
        delete from neknd_portrait_talent where id = #{id}
    </delete>

    <delete id="deleteNekndPortraitTalentByIds" parameterType="String">
        delete from neknd_portrait_talent where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>