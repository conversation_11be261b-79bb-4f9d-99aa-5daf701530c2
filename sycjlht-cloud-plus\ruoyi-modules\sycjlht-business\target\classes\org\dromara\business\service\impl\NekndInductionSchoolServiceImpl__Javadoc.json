{"doc": " 引产入校信息Service业务层处理\n\n <AUTHOR>\n @date 2024-12-26\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndInductionSchoolById", "paramTypes": ["java.lang.Integer"], "doc": " 查询引产入校信息\n\n @param id 引产入校信息主键\n @return 引产入校信息\n"}, {"name": "selectNekndInductionSchoolList", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": " 查询引产入校信息列表\n\n @param nekndInductionSchool 引产入校信息\n @return 引产入校信息\n"}, {"name": "insertNekndInductionSchool", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": " 新增引产入校信息\n\n @param nekndInductionSchool 引产入校信息\n @return 结果\n"}, {"name": "updateNekndInductionSchool", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": " 修改引产入校信息\n\n @param nekndInductionSchool 引产入校信息\n @return 结果\n"}, {"name": "deleteNekndInductionSchoolByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除引产入校信息\n\n @param ids 需要删除的引产入校信息主键\n @return 结果\n"}, {"name": "selectCollegeStats", "paramTypes": ["java.lang.String"], "doc": " 批量删除引产入校信息\n\n @return 结果\n"}, {"name": "deleteNekndInductionSchoolById", "paramTypes": ["java.lang.Integer"], "doc": " 删除引产入校信息信息\n\n @param id 引产入校信息主键\n @return 结果\n"}, {"name": "queryMyApplications", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询我的申请\n"}], "constructors": []}