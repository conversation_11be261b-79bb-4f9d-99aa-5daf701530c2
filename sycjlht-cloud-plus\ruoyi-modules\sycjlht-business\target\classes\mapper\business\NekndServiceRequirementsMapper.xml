<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndServiceRequirementsMapper">

    <resultMap type="NekndServiceRequirements" id="NekndServiceRequirementsResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="serviceProviderId" column="service_provider_id"/>
        <result property="serviceProviderName" column="service_provider_name"/>
        <result property="serviceField" column="service_field"/>
        <result property="area" column="area"/>
        <result property="money" column="money"/>
        <result property="introduction"  column="introduction"/>
        <result property="name" column="name"/>
        <result property="pictureUri" column="picture_uri"/>
        <result property="deliveryMethod" column="delivery_method"/>
        <result property="honorary" column="honorary"/>
        <result property="phone" column="phone"/>
        <result property="content" column="content"/>
        <result property="value" column="value"/>
        <result property="scenarios" column="scenarios"/>
        <result property="productCase" column="product_case"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="reviewStatus" column="review_status"/>
        <result property="serviceProviderCategory" column="service_provider_category"/>
    </resultMap>

    <sql id="selectNekndServiceRequirementsVo">
        select id,
               create_time,
               update_time,
               service_provider_id,
               service_provider_name,
               service_field,
               area,
               money,
               introduction,
               name,
               picture_uri,
               delivery_method,
               honorary,
               phone,
               content,
               value,
               scenarios,
               product_case,
               provincial_id,
               provincial_name,
               city_id,
               city_name,
               review_status,
               service_provider_category
        from neknd_service_requirements
    </sql>

<!--    根据部门权限来查数据-->
    <select id="selectNekndServiceRequirementsList" parameterType="NekndServiceRequirements" resultMap="NekndServiceRequirementsResult">
        select u.id,
        u.create_time,
        u.update_time,
        u.service_provider_id,
        u.service_provider_name,
        u.service_field,
        u.area,
        u.money,
        u.introduction,
        u.name,
        u.picture_uri,
        u.delivery_method,
        u.honorary,
        u.phone,
        u.content,
        u.value,
        u.scenarios,
        u.product_case,
        u.provincial_id,
        u.provincial_name,
        u.city_id,
        u.city_name,
        u.review_status,
        u.service_provider_category
        from neknd_service_requirements u  left join sys_dept d on u.service_provider_id = d.dept_id
        <where>
            u.del_flag=0
            <if test="serviceProviderName != null  and serviceProviderName != ''">and service_provider_name like concat('%', #{serviceProviderName}, '%')</if>
            <if test="serviceField != null  and serviceField != ''">and service_field = #{serviceField}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="deliveryMethod != null  and deliveryMethod != ''">and delivery_method = #{deliveryMethod}</if>
            <if test="reviewStatus != null  and reviewStatus != ''">and review_status = #{reviewStatus}</if>
            <if test="serviceProviderCategory != null  and serviceProviderCategory != ''">and service_provider_category = #{serviceProviderCategory}</if>
            ${params.dataScope}
        </where>
        order by u.create_time desc
    </select>

    <select id="selectNekndServiceRequirementsById" parameterType="Integer" resultMap="NekndServiceRequirementsResult">
        <include refid="selectNekndServiceRequirementsVo"/>
        where id = #{id} and del_flag = 0
    </select>
    <select id="getNekndServiceRequirementsList" parameterType="NekndServiceRequirements" resultMap="NekndServiceRequirementsResult">
<!--        <include refid="selectNekndServiceRequirementsVo"/>-->
        select id,
        create_time,
        update_time,
        service_provider_id,
        service_provider_name,
        service_field,
        area,
        money,
        introduction,
        name,
        picture_uri,
        delivery_method,
        honorary,
        phone,
        content,
        value,
        scenarios,
        product_case,
        provincial_id,
        provincial_name,
        city_id,
        city_name,
        review_status,
        service_provider_category
        from neknd_service_requirements
        <where>
            review_status = 1 and del_flag = 0
<!--            <if test="searchValue != null  and searchValue != ''">-->
<!--            and service_provider_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">
            and name like concat('%', #{searchValue}, '%')</if>
            <if test="serviceField != null  and serviceField != ''">and service_field = #{serviceField}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="serviceProviderCategory != null  and serviceProviderCategory != ''">and service_provider_category = #{serviceProviderCategory}</if>
        </where>
        order by create_time desc
    </select>

    <!--推荐服务 -->
    <select id="getRecommendedServicesList" parameterType="NekndServiceRequirements" resultMap="NekndServiceRequirementsResult">
        <!--        <include refid="selectNekndServiceRequirementsVo"/>-->
        select id,
        create_time,
        update_time,
        service_provider_id,
        service_provider_name,
        service_field,
        area,
        money,
        introduction,
        name,
        picture_uri,
        delivery_method,
        honorary,
        phone,
        content,
        value,
        scenarios,
        product_case,
        provincial_id,
        provincial_name,
        city_id,
        city_name,
        review_status,
        service_provider_category
        from neknd_service_requirements
        <where>
            review_status = 1 and del_flag = 0
            <!--            <if test="searchValue != null  and searchValue != ''">-->
            <!--            and service_provider_name like concat('%', #{searchValue}, '%')</if>-->
            <if test="searchValue != null  and searchValue != ''">
                and name like concat('%', #{searchValue}, '%')</if>
            <if test="serviceField != null  and serviceField != ''">and service_field = #{serviceField}</if>
            <if test="money != null  and money != ''">and money = #{money}</if>
            <if test="provincialId != null  and provincialId != ''">and provincial_id = #{provincialId}</if>
            <if test="cityId != null  and cityId != ''">and city_id = #{cityId}</if>
            <if test="serviceProviderCategory != null  and serviceProviderCategory != ''">and service_provider_category = #{serviceProviderCategory}</if>
        </where>
    </select>
    <select id="getServiceRequirementsByDeptId" resultType="com.neknd.system.domain.NekndServiceRequirements">
        <include refid="selectNekndServiceRequirementsVo"/>
         where service_provider_id = #{deptId} and del_flag = 0
    </select>

    <insert id="insertNekndServiceRequirements" parameterType="NekndServiceRequirements" >
        insert into neknd_service_requirements
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="serviceProviderId != null">service_provider_id,</if>
            <if test="serviceProviderName != null">service_provider_name,</if>
            <if test="serviceField != null">service_field,</if>
            <if test="area != null">area,</if>
            <if test="money != null">money,</if>
            <if test="introduction != null">introduction,</if>
            <if test="name != null">name,</if>
            <if test="pictureUri != null">picture_uri,</if>
            <if test="deliveryMethod != null">delivery_method,</if>
            <if test="honorary != null">honorary,</if>
            <if test="phone != null">phone,</if>
            <if test="content != null">content,</if>
            <if test="value != null">value,</if>
            <if test="scenarios != null">scenarios,</if>
            <if test="productCase != null">product_case,</if>
            <if test="provincialId != null">provincial_id,</if>
            <if test="provincialName != null">provincial_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="serviceProviderCategory != null">service_provider_category,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="serviceProviderId != null">#{serviceProviderId},</if>
            <if test="serviceProviderName != null">#{serviceProviderName},</if>
            <if test="serviceField != null">#{serviceField},</if>
            <if test="area != null">#{area},</if>
            <if test="money != null">#{money},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="name != null">#{name},</if>
            <if test="pictureUri != null">#{pictureUri},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="honorary != null">#{honorary},</if>
            <if test="phone != null">#{phone},</if>
            <if test="content != null">#{content},</if>
            <if test="value != null">#{value},</if>
            <if test="scenarios != null">#{scenarios},</if>
            <if test="productCase != null">#{productCase},</if>
            <if test="provincialId != null">#{provincialId},</if>
            <if test="provincialName != null">#{provincialName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="serviceProviderCategory != null">#{serviceProviderCategory},</if>
        </trim>
    </insert>

    <update id="updateNekndServiceRequirements" parameterType="NekndServiceRequirements">
        update neknd_service_requirements
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="serviceProviderId != null">service_provider_id = #{serviceProviderId},</if>
            <if test="serviceProviderName != null">service_provider_name = #{serviceProviderName},</if>
            <if test="serviceField != null">service_field = #{serviceField},</if>
            <if test="area != null">area = #{area},</if>
            <if test="money != null">money = #{money},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="name != null">name = #{name},</if>
            <if test="pictureUri != null">picture_uri = #{pictureUri},</if>
            <if test="deliveryMethod != null">delivery_method = #{deliveryMethod},</if>
            <if test="honorary != null">honorary = #{honorary},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="content != null">content = #{content},</if>
            <if test="value != null">value = #{value},</if>
            <if test="scenarios != null">scenarios = #{scenarios},</if>
            <if test="productCase != null"> product_case = #{productCase},</if>
            <if test="provincialId != null">provincial_id = #{provincialId},</if>
            <if test="provincialName != null">provincial_name = #{provincialName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="serviceProviderCategory != null">service_provider_category = #{serviceProviderCategory},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteNekndServiceRequirementsById" parameterType="Integer">
        update neknd_service_requirements
        set del_flag=2 where id = #{id}
    </update>

    <update id="deleteNekndServiceRequirementsByIds" parameterType="String">
        UPDATE neknd_service_requirements SET del_flag = 2
        WHERE id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectServiceFieldDictValueByDictLabel" resultType="String">
        SELECT dict_value
        FROM sys_dict_data
        WHERE dict_type = "sys_demand_industry"
          AND dict_label= #{dictLabel}
    </select>

    <select id="selectMoneyDictValueByDictLabel" resultType="String">
        SELECT dict_value
        FROM sys_dict_data
        WHERE dict_type = "sys_job_salary"
          AND dict_label= #{dictLabel}
    </select>
</mapper>