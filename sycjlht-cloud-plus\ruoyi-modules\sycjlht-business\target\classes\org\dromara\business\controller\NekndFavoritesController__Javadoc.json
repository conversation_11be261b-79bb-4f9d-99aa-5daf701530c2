{"doc": " 企业人才收藏关系（人才收藏岗位，企业收藏人才）Controller\n\n <AUTHOR>\n @date 2025-04-14\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndFavorites", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询企业人才收藏关系列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndFavorites"], "doc": " 导出企业人才收藏关系列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取企业人才收藏关系详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 新增企业人才收藏关系\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 修改企业人才收藏关系\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除企业人才收藏关系\n"}, {"name": "toggleFavorite", "paramTypes": ["org.dromara.business.domain.NekndFavorites"], "doc": " 收藏和取消收藏\n"}, {"name": "getFavorite", "paramTypes": ["org.dromara.business.domain.NekndFavorites", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前用户的收藏列表\n"}, {"name": "batchToggleFavorite", "paramTypes": ["java.util.List"], "doc": " 批量切换收藏状态\n"}, {"name": "getFavoriteStatistics", "paramTypes": [], "doc": " 获取收藏统计信息\n"}, {"name": "checkFavorite", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 检查是否已收藏\n"}], "constructors": []}