<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndLargeDataScreenMapper">
    
    <resultMap type="NekndLargeDataScreen" id="NekndLargeDataScreenResult">
        <result property="id"    column="id"    />
        <result property="paramOne"    column="param_one"    />
        <result property="paramTwo"    column="param_two"    />
        <result property="paramThree"    column="param_three"    />
        <result property="paramFour"    column="param_four"    />
        <result property="paramFive"    column="param_five"    />
        <result property="paramSix"    column="param_six"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectNekndLargeDataScreenVo">
        select id, param_one, param_two, param_three, param_four, param_five, param_six, type from neknd_large_data_screen
    </sql>

    <select id="selectNekndLargeDataScreenList" parameterType="NekndLargeDataScreen" resultMap="NekndLargeDataScreenResult">
        <include refid="selectNekndLargeDataScreenVo"/>
        <where>  
            <if test="paramOne != null  and paramOne != ''"> and param_one = #{paramOne}</if>
            <if test="paramTwo != null  and paramTwo != ''"> and param_two = #{paramTwo}</if>
            <if test="paramThree != null  and paramThree != ''"> and param_three = #{paramThree}</if>
            <if test="paramFour != null  and paramFour != ''"> and param_four = #{paramFour}</if>
            <if test="paramFive != null  and paramFive != ''"> and param_five = #{paramFive}</if>
            <if test="paramSix != null  and paramSix != ''"> and param_six = #{paramSix}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectNekndLargeDataScreenById" parameterType="Long" resultMap="NekndLargeDataScreenResult">
        <include refid="selectNekndLargeDataScreenVo"/>
        where id = #{id}
    </select>
    <select id="selectLargeDataScreenByType" resultMap="NekndLargeDataScreenResult">
        <include refid="selectNekndLargeDataScreenVo"/>
        where type = #{type}
    </select>

    <insert id="insertNekndLargeDataScreen" parameterType="NekndLargeDataScreen" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_large_data_screen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paramOne != null">param_one,</if>
            <if test="paramTwo != null">param_two,</if>
            <if test="paramThree != null">param_three,</if>
            <if test="paramFour != null">param_four,</if>
            <if test="paramFive != null">param_five,</if>
            <if test="paramSix != null">param_six,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paramOne != null">#{paramOne},</if>
            <if test="paramTwo != null">#{paramTwo},</if>
            <if test="paramThree != null">#{paramThree},</if>
            <if test="paramFour != null">#{paramFour},</if>
            <if test="paramFive != null">#{paramFive},</if>
            <if test="paramSix != null">#{paramSix},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateNekndLargeDataScreen" parameterType="NekndLargeDataScreen">
        update neknd_large_data_screen
        <trim prefix="SET" suffixOverrides=",">
            <if test="paramOne != null">param_one = #{paramOne},</if>
            <if test="paramTwo != null">param_two = #{paramTwo},</if>
            <if test="paramThree != null">param_three = #{paramThree},</if>
            <if test="paramFour != null">param_four = #{paramFour},</if>
            <if test="paramFive != null">param_five = #{paramFive},</if>
            <if test="paramSix != null">param_six = #{paramSix},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndLargeDataScreenById" parameterType="Long">
        delete from neknd_large_data_screen where id = #{id}
    </delete>

    <delete id="deleteNekndLargeDataScreenByIds" parameterType="String">
        delete from neknd_large_data_screen where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>