{"doc": " 课程管理控制器\n\n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCourses", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询课程列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCourses"], "doc": " 导出课程列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入课程数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取课程详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": " 新增课程\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": " 修改课程\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除课程\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndCourses", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 门户查询课程列表\n"}, {"name": "getInfoByCourseId", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 门户获取课程详细信息\n"}], "constructors": []}