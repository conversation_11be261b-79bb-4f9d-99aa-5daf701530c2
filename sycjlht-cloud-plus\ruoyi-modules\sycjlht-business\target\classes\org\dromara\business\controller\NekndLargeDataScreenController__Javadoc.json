{"doc": " 数据大屏Controller\n \n <AUTHOR>\n @date 2025-01-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": " 获取数据大屏信息（公开接口）\n"}, {"name": "list", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询数据大屏列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 导出数据大屏列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取数据大屏详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 新增数据大屏\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndLargeDataScreen"], "doc": " 修改数据大屏\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除数据大屏\n"}, {"name": "getRealtimeData", "paramTypes": ["java.lang.String"], "doc": " 获取实时数据大屏数据\n"}, {"name": "refreshScreenData", "paramTypes": ["java.lang.String"], "doc": " 异步刷新数据大屏数据\n"}, {"name": "getScreenConfig", "paramTypes": ["java.lang.String"], "doc": " 获取数据大屏配置\n"}], "constructors": []}