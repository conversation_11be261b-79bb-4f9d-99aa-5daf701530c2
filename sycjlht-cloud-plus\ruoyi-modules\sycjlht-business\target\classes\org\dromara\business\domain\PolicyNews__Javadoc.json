{"doc": " 政策新闻信息对象 neknd_policy_news\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [{"name": "id", "doc": " 政策新闻ID\n"}, {"name": "cover<PERSON>ri", "doc": " 封面图\n"}, {"name": "newsTitle", "doc": " 政策新闻标题\n"}, {"name": "newsType", "doc": " 新闻类型（1职教动态 2职教新闻 3政策解读 4其他）\n"}, {"name": "newsContent", "doc": " 政策新闻内容\n"}, {"name": "status", "doc": " 新闻状态（0正常 1关闭）\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 2代表删除）\n"}, {"name": "sourceTitle", "doc": " 政策新闻来源名称\n"}, {"name": "policyCategory", "doc": " 政策类别（政策库字段）\n"}, {"name": "policy2category", "doc": " 政策类别二级筛选（政策库字段）\n"}, {"name": "fileType", "doc": " 文件类型（1指导意见 2实施方案 3政策文件）\n"}, {"name": "isThematicMeeting", "doc": " 是否为专题会议（0无 1是 2不是）\n"}, {"name": "planType", "doc": " 方案类型（0无 1经费方案 2土地方案 3税收方案）\n"}, {"name": "belongPark", "doc": " 所属园区\n"}, {"name": "newsPosition", "doc": " 新闻位置\n"}, {"name": "fileTypeFunds", "doc": " 文件类型（0无 1有资金 2无资金）\n"}, {"name": "belongDistrict", "doc": " 所属园区\n"}], "enumConstants": [], "methods": [], "constructors": []}