{"doc": " 面试记录查看Controller\n \n <AUTHOR>\n @date 2024-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询面试记录查看列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 导出面试记录查看列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取面试记录查看详细信息\n"}, {"name": "add", "paramTypes": ["java.lang.Integer"], "doc": " 新增面试记录，投递简历\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 修改面试记录查看\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除面试记录查看\n"}, {"name": "status", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 用户查看招聘岗位的投递状态\n"}, {"name": "getEmployInterviewList", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 根据userId查询面试记录的岗位信息\n 个人查看投递记录\n 0待审核1审核通过2审核未通过3待定5线上面试6线下面试7面试通过8面试未通过9已到岗10未到岗\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 查看面试记录（个人）\n"}, {"name": "selectEnterprise", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 公司查看投递记录\n"}, {"name": "updateResumeReviewStatus", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": " 简历审核，修改面试记录的状态\n"}, {"name": "getCountInterviewed", "paramTypes": [], "doc": " 获取公司已经面试过的人数\n"}], "constructors": []}