<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndOrderClassRegisterRecordsMapper">
    
    <resultMap type="NekndOrderClassRegisterRecords" id="NekndOrderClassRegisterRecordsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="orderClassRegisterTime"    column="order_class_register_time"    />
        <result property="orderClassRegisterNumber"    column="order_class_register_number"    />
        <result property="orderClassId"    column="order_class_id"    />
        <result property="orderClassTitle"    column="order_class_title"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="address"    column="address"    />
        <result property="price"    column="price"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
    </resultMap>

    <sql id="selectNekndOrderClassRegisterRecordsVo">
        select id, user_id, name, phone, order_class_register_time, order_class_register_number, order_class_id, order_class_title, dept_id, dept_name, del_flag,create_by, create_time, update_by, update_time,address,price,start_time,end_time from neknd_order_class_register_records
    </sql>

    <select id="selectNekndOrderClassRegisterRecordsList" parameterType="NekndOrderClassRegisterRecords" resultMap="NekndOrderClassRegisterRecordsResult">
        <include refid="selectNekndOrderClassRegisterRecordsVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="orderClassRegisterTime != null "> and order_class_register_time = #{orderClassRegisterTime}</if>
            <if test="orderClassRegisterNumber != null "> and order_class_register_number = #{orderClassRegisterNumber}</if>
            <if test="orderClassTitle != null  and orderClassTitle != ''"> and order_class_title like concat('%', #{orderClassTitle}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="deptId != null  and deptId != ''"> and dept_id = #{deptId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndOrderClassRegisterRecordsById" parameterType="Long" resultMap="NekndOrderClassRegisterRecordsResult">
        <include refid="selectNekndOrderClassRegisterRecordsVo"/>
        where id = #{id}
    </select>
    <select id="selectOrderClassStudentsByOrderClassId"
            resultMap="NekndOrderClassRegisterRecordsResult">
        <include refid="selectNekndOrderClassRegisterRecordsVo"/>
        where order_class_id = #{orderClassId} and del_flag = 0
        order by create_time desc
    </select>

    <insert id="insertNekndOrderClassRegisterRecords" parameterType="NekndOrderClassRegisterRecords" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_order_class_register_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="orderClassRegisterTime != null">order_class_register_time,</if>
            <if test="orderClassRegisterNumber != null">order_class_register_number,</if>
            <if test="orderClassId != null">order_class_id,</if>
            <if test="orderClassTitle != null">order_class_title,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="address != null">address,</if>
            <if test="price != null">price,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="orderClassRegisterTime != null">#{orderClassRegisterTime},</if>
            <if test="orderClassRegisterNumber != null">#{orderClassRegisterNumber},</if>
            <if test="orderClassId != null">#{orderClassId},</if>
            <if test="orderClassTitle != null">#{orderClassTitle},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="address != null">#{address},</if>
            <if test="price != null">#{price},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
         </trim>
    </insert>

    <update id="updateNekndOrderClassRegisterRecords" parameterType="NekndOrderClassRegisterRecords">
        update neknd_order_class_register_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="orderClassRegisterTime != null">order_class_register_time = #{orderClassRegisterTime},</if>
            <if test="orderClassRegisterNumber != null">order_class_register_number = #{orderClassRegisterNumber},</if>
            <if test="orderClassId != null">order_class_id = #{orderClassId},</if>
            <if test="orderClassTitle != null">order_class_title = #{orderClassTitle},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="address != null">address = #{address},</if>
            <if test="price != null">price = #{price},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndOrderClassRegisterRecordsById" parameterType="Long">
        delete from neknd_order_class_register_records where id = #{id}
    </delete>

    <delete id="deleteNekndOrderClassRegisterRecordsByIds" parameterType="String">
        delete from neknd_order_class_register_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>