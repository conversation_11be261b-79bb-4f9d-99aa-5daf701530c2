{"doc": " 视频观看进度记录Service业务层处理\n\n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesVideoViewProgressById", "paramTypes": ["java.lang.Integer"], "doc": " 查询视频观看进度记录\n\n @param id 视频观看进度记录主键\n @return 视频观看进度记录\n"}, {"name": "selectNekndCoursesVideoViewProgressList", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": " 查询视频观看进度记录列表\n\n @param nekndCoursesVideoViewProgress 视频观看进度记录\n @return 视频观看进度记录\n"}, {"name": "insertNekndCoursesVideoViewProgress", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": " 新增视频观看进度记录\n\n @param nekndCoursesVideoViewProgress 视频观看进度记录\n @return 结果\n"}, {"name": "updateNekndCoursesVideoViewProgress", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": " 修改视频观看进度记录\n\n @param nekndCoursesVideoViewProgress 视频观看进度记录\n @return 结果\n"}, {"name": "deleteNekndCoursesVideoViewProgressByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除视频观看进度记录\n\n @param ids 需要删除的视频观看进度记录主键\n @return 结果\n"}, {"name": "deleteNekndCoursesVideoViewProgressById", "paramTypes": ["java.lang.Integer"], "doc": " 删除视频观看进度记录信息\n\n @param id 视频观看进度记录主键\n @return 结果\n"}], "constructors": []}