{"doc": " 报告权限管理Mapper接口\n\n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": " 查询报告权限管理\n\n @param id 报告权限管理主键\n @return 报告权限管理\n"}, {"name": "selectNekndReportPrivilegeList", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 查询报告权限管理列表\n\n @param nekndReportPrivilege 报告权限管理\n @return 报告权限管理集合\n"}, {"name": "insertNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 新增报告权限管理\n\n @param nekndReportPrivilege 报告权限管理\n @return 结果\n"}, {"name": "updateNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": " 修改报告权限管理\n\n @param nekndReportPrivilege 报告权限管理\n @return 结果\n"}, {"name": "deleteNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": " 删除报告权限管理\n\n @param id 报告权限管理主键\n @return 结果\n"}, {"name": "deleteNekndReportPrivilegeByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除报告权限管理\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}