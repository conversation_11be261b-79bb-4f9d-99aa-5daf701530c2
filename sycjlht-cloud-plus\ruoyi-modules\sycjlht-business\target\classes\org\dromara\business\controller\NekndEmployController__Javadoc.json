{"doc": " 招聘岗位Controller\n \n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询招聘岗位列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndEmploy"], "doc": " 导出招聘岗位列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 导入岗位数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取招聘岗位详细信息\n"}, {"name": "getPublicInfo", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 获取招聘岗位详细信息（公开接口）\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 新增招聘岗位\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 修改招聘岗位\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除招聘岗位\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询招聘岗位列表（公开接口）\n"}, {"name": "getRecommend", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询推荐招聘岗位列表\n"}, {"name": "updateTop", "paramTypes": ["java.lang.Long"], "doc": " 岗位置顶\n"}, {"name": "cancelTop", "paramTypes": ["java.lang.Long"], "doc": " 取消岗位置顶\n"}, {"name": "getRefineText", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " AI面试-润化文本\n"}, {"name": "getInterviewQuestion", "paramTypes": ["java.lang.String"], "doc": " AI面试-出题\n"}, {"name": "sendText", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " AI面试-用户发送消息\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.String"], "doc": " AI面试-结束面试\n"}, {"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 上传文件获取fileId\n"}, {"name": "postAnalysisResult", "paramTypes": ["org.dromara.business.domain.vo.RequestParamVo"], "doc": " 获取分析结果\n"}, {"name": "postSendMessage", "paramTypes": ["org.dromara.business.domain.vo.RequestParamVo"], "doc": " 对话\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 修改审核状态\n"}, {"name": "getJobDisplay", "paramTypes": [], "doc": " 岗位展示列表\n"}, {"name": "getRealTimeEmploy", "paramTypes": [], "doc": " 发布岗位实时数据\n"}, {"name": "getHotJobs", "paramTypes": [], "doc": " 岗位投递TOP10\n"}, {"name": "getAIJobContent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " AI智能填充岗位描述或要求\n"}, {"name": "getEmployMatch", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": " 分析岗位与人才匹配度\n"}, {"name": "createNewInterview", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.system.api.model.LoginUser"], "doc": " 创建新的面试\n"}, {"name": "buildInterviewSystemPrompt", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": " 构建面试系统提示\n"}, {"name": "saveInterviewRecord", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.system.api.model.LoginUser", "java.util.List"], "doc": " 保存面试记录\n"}, {"name": "processUserMessage", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord", "java.lang.String"], "doc": " 处理用户消息\n"}, {"name": "sendInterviewRecordToCompany", "paramTypes": ["org.dromara.business.domain.NekndEmploy", "org.dromara.business.domain.NekndEmployInterviewRecord", "org.dromara.system.api.model.LoginUser", "org.dromara.system.api.domain.vo.RemoteUserVo"], "doc": " 发送面试记录到企业\n"}, {"name": "callKimiFilesContentApi", "paramTypes": ["java.lang.String"], "doc": " 调用Kimi Files Content API\n"}, {"name": "processAnalysis", "paramTypes": ["org.dromara.business.domain.vo.RequestParamVo", "java.lang.String"], "doc": " 处理分析请求\n"}, {"name": "getAnalysisSystemPrompt", "paramTypes": [], "doc": " 获取分析系统提示\n"}, {"name": "postCall", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " HTTP POST调用\n"}], "constructors": []}