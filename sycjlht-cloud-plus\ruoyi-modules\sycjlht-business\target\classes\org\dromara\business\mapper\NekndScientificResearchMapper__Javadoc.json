{"doc": " 科普研学信息Mapper接口\n\n <AUTHOR>\n @date 2024-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchById", "paramTypes": ["java.lang.Integer"], "doc": " 查询科普研学信息\n\n @param id 科普研学信息主键\n @return 科普研学信息\n"}, {"name": "selectNekndScientificResearchList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": " 查询科普研学信息列表\n\n @param nekndScientificResearch 科普研学信息\n @return 科普研学信息集合\n"}, {"name": "insertNekndScientificResearch", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": " 新增科普研学信息\n\n @param nekndScientificResearch 科普研学信息\n @return 结果\n"}, {"name": "updateNekndScientificResearch", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": " 修改科普研学信息\n\n @param nekndScientificResearch 科普研学信息\n @return 结果\n"}, {"name": "deleteNekndScientificResearchById", "paramTypes": ["java.lang.Integer"], "doc": " 删除科普研学信息\n\n @param id 科普研学信息主键\n @return 结果\n"}, {"name": "deleteNekndScientificResearchByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除科普研学信息\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}