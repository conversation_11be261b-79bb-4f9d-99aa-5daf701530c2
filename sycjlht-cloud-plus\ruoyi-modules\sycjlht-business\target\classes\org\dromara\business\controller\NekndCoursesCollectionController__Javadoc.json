{"doc": " 课程收藏记录Controller\n \n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询课程收藏记录列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesCollection"], "doc": " 导出课程收藏记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取课程收藏记录详细信息\n"}, {"name": "collectionsAndUncollections", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": " 新增课程收藏记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除课程收藏记录\n"}, {"name": "getOwnList", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询当前用户收藏课程记录列表\n"}], "constructors": []}