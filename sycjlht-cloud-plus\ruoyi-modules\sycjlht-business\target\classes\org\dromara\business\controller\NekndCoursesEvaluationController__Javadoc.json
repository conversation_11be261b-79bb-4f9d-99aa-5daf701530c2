{"doc": " 课程评价记录Controller\n \n <AUTHOR>\n @date 2024-12-08\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询课程评价记录列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndCoursesEvaluation"], "doc": " 导出课程评价记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取课程评价记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": " 新增课程评价记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndCoursesEvaluation"], "doc": " 修改课程评价记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除课程评价记录\n"}], "constructors": []}