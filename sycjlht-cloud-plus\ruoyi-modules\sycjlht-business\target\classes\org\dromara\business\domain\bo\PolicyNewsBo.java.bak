package org.dromara.business.domain.bo;

import org.dromara.business.domain.PolicyNews;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 政策新闻信息业务对象 neknd_policy_news
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PolicyNews.class, reverseConvertGenerate = false)
public class PolicyNewsBo extends BaseEntity {

    /**
     * 政策新闻ID
     */
    @NotNull(message = "政策新闻ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 封面图
     */
    private String coverUri;

    /**
     * 政策新闻标题
     */
    @NotBlank(message = "政策新闻标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String newsTitle;

    /**
     * 新闻类型（1职教动态 2职教新闻 3政策解读 4其他）
     */
    @NotBlank(message = "新闻类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String newsType;

    /**
     * 政策新闻内容
     */
    private String newsContent;

    /**
     * 新闻状态（0正常 1关闭）
     */
    private String status;

    /**
     * 政策新闻来源名称
     */
    private String sourceTitle;

    /**
     * 政策类别（政策库字段）
     */
    private String policyCategory;

    /**
     * 政策类别二级筛选（政策库字段）
     */
    private String policy2category;

    /**
     * 文件类型（1指导意见 2实施方案 3政策文件）
     */
    private String fileType;

    /**
     * 是否为专题会议（0无 1是 2不是）
     */
    private String isThematicMeeting;

    /**
     * 方案类型（0无 1经费方案 2土地方案 3税收方案）
     */
    private String planType;

    /**
     * 所属园区
     */
    private String belongPark;

    /**
     * 新闻位置
     */
    private String newsPosition;

    /**
     * 文件类型（0无 1有资金 2无资金）
     */
    private String fileTypeFunds;

    /**
     * 所属园区
     */
    private String belongDistrict;
}
