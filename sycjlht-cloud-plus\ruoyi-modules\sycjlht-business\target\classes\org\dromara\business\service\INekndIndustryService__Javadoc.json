{"doc": " 【请填写功能名称】Service接口\n\n <AUTHOR>\n @date 2024-12-23\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustryList", "paramTypes": ["org.dromara.business.domain.NekndIndustry"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustry 【请填写功能名称】\n @return 【请填写功能名称】集合\n"}, {"name": "insertNekndIndustry", "paramTypes": ["org.dromara.business.domain.NekndIndustry"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustry 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustry", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "org.dromara.business.domain.NekndIndustry"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustry 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustryByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industryIds 需要删除的【请填写功能名称】主键集合\n @return 结果\n"}, {"name": "deleteNekndIndustryByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】信息\n\n @param industryId 【请填写功能名称】主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndIndustry", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询产业列表\n\n @param nekndIndustry 查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}], "constructors": []}