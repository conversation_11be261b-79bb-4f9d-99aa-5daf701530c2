{"doc": " 政策新闻信息Controller\n 整合了现代化架构和单体业务逻辑\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询政策新闻信息列表\n"}, {"name": "export", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出政策新闻信息列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入政策新闻信息\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取政策新闻信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 新增政策新闻信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 修改政策新闻信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除政策新闻信息\n"}, {"name": "audit", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 审核政策新闻\n 1.审核通过 2.审核不通过\n"}, {"name": "getData", "paramTypes": [], "doc": " 获取政策新闻统计数据\n"}, {"name": "publicList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 公开接口：查询政策新闻信息列表（无需权限）\n"}, {"name": "publicGetInfo", "paramTypes": ["java.lang.Long"], "doc": " 公开接口：获取政策新闻信息详细信息（无需权限）\n"}], "constructors": []}