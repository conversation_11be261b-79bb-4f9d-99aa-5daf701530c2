package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import cn.dev33.satoken.annotation.SaIgnore;
import org.dromara.business.annotation.Log;
import org.dromara.business.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.business.domain.NekndCourses;
import org.dromara.business.domain.NekndCoursesCollection;
import org.dromara.business.domain.NekndCoursesVideos;
import org.dromara.business.domain.NekndLikeRecords;
import org.dromara.business.service.INekndCoursesCollectionService;
import org.dromara.business.service.INekndCoursesService;
import org.dromara.business.service.INekndCoursesVideosService;
import org.dromara.business.service.INekndLikeRecordsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.RemoteDictService;
import org.dromara.system.api.model.LoginUser;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程管理控制器
 *
 * <AUTHOR>
 * @date 2024-12-08
 */
@RestController
@RequestMapping("/courses")
@RequiredArgsConstructor
public class NekndCoursesController extends BaseController {

    private final INekndCoursesService nekndCoursesService;
    private final INekndCoursesVideosService nekndCoursesVideosService;
    private final INekndCoursesCollectionService coursesCollectionService;
    private final INekndLikeRecordsService likeRecordsService;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteDictService remoteDictService;

    /**
     * 查询课程列表
     */
    @SaCheckPermission("business:courses:list")
    @GetMapping("/list")
    public TableDataInfo<NekndCourses> list(NekndCourses nekndCourses, PageQuery pageQuery) {
        // TODO: 待实现分页查询业务逻辑 - 需要Service层添加queryPageList方法
        // return nekndCoursesService.queryPageList(nekndCourses, pageQuery);
        return TableDataInfo.build(); // 暂时返回空结果，待业务逻辑实现
    }

    /**
     * 导出课程列表
     */
    @SaCheckPermission("business:courses:export")
    @Log(title = "课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NekndCourses nekndCourses) {
        List<NekndCourses> list = nekndCoursesService.selectNekndCoursesList(nekndCourses);
        ExcelUtil.exportExcel(list, "课程", NekndCourses.class, response);
    }

    /**
     * 导入课程数据
     */
    @Log(title = "导入课程数据", businessType = BusinessType.IMPORT)
    @SaCheckPermission("business:courses:import")
    @PostMapping("/importData")
    public R<String> importData(MultipartFile file) throws Exception {
        List<NekndCourses> nekndCoursesList = ExcelUtil.importExcel(file.getInputStream(), NekndCourses.class);
        String operName = LoginHelper.getUsername();
        String message = nekndCoursesService.importCourses(nekndCoursesList, operName);
        return R.ok(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "课程", NekndCourses.class, response);
    }

    /**
     * 获取课程详细信息
     */
    @SaCheckPermission("business:courses:query")
    @GetMapping(value = "/{courseId}")
    public R<NekndCourses> getInfo(@PathVariable("courseId") Integer courseId) {
        return R.ok(nekndCoursesService.selectNekndCoursesByCourseId(courseId));
    }

    /**
     * 新增课程
     */
    @SaCheckPermission("business:courses:add")
    @Log(title = "课程", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@RequestBody NekndCourses nekndCourses) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return R.fail("获取用户登录信息失败!");
        }
        // TODO: 设置创建者信息 - 暂时注释类型不匹配的字段
        // nekndCourses.setCreateId(loginUser.getUserId());
        // nekndCourses.setCreateBy(loginUser.getUsername()); // 类型不匹配，暂时注释
        return toAjax(nekndCoursesService.insertNekndCourses(nekndCourses));
    }

    /**
     * 修改课程
     */
    @SaCheckPermission("business:courses:edit")
    @Log(title = "课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@RequestBody NekndCourses nekndCourses) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return R.fail("获取用户登录信息失败!");
        }
        // TODO: 设置更新者信息 - 暂时注释类型不匹配的字段
        // nekndCourses.setUpdateId(loginUser.getUserId());
        // nekndCourses.setUpdateBy(loginUser.getUsername()); // 类型不匹配，暂时注释
        return toAjax(nekndCoursesService.updateNekndCourses(nekndCourses));
    }

    /**
     * 删除课程
     */
    @SaCheckPermission("business:courses:remove")
    @Log(title = "课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{courseIds}")
    public R<Void> remove(@PathVariable Integer[] courseIds) {
        return toAjax(nekndCoursesService.deleteNekndCoursesByCourseIds(courseIds));
    }

    /**
     * 门户查询课程列表
     */
    @SaIgnore
    @GetMapping("/getList")
    public TableDataInfo<NekndCourses> getList(NekndCourses nekndCourses, PageQuery pageQuery) {
        // TODO: 待实现门户课程分页查询业务逻辑
        // return nekndCoursesService.queryPageList(nekndCourses, pageQuery);
        return TableDataInfo.build(); // 暂时返回空结果，待业务逻辑实现
    }

    /**
     * 门户获取课程详细信息
     */
    @SaIgnore
    @GetMapping(value = "/getInfoByCourseId/{courseId}")
    public R<NekndCourses> getInfoByCourseId(@PathVariable("courseId") Integer courseId,
            @RequestParam(required = false) Integer userId) {
        // 查询课程视频列表
        NekndCoursesVideos videos = new NekndCoursesVideos();
        videos.setCourseId(courseId);
        List<NekndCoursesVideos> nekndCoursesVideos = nekndCoursesVideosService.selectNekndCoursesVideosList(videos);

        // 查询课程详情
        NekndCourses nekndCourses = nekndCoursesService.selectNekndCoursesByCourseId(courseId);
        if (nekndCourses == null) {
            return R.fail("课程不存在");
        }

        // 更新课程访问量
        nekndCourses.setViewCount(nekndCourses.getViewCount() + 1);
        nekndCoursesService.updateNekndCourses(nekndCourses);

        // 如果用户登录，查询收藏状态
        if (userId != null) {
            // 查看当前用户是否收藏该课程
            NekndCoursesCollection collectionQuery = new NekndCoursesCollection();
            collectionQuery.setCoursesId(courseId);
            collectionQuery.setUserId(userId);
            boolean isCollected = coursesCollectionService.selectNekndCoursesCollectionList(collectionQuery).size() > 0;
            nekndCourses.setCollect(isCollected);
        }

        // 如果用户登录，查询点赞状态
        if (userId != null) {
            NekndLikeRecords likeRecords = likeRecordsService.selectLikeRecordsByUserIdAndCourseId(userId.longValue(),
                    courseId);
            Boolean isLiked = false;
            if (likeRecords != null) {
                // 是否已取消点赞，true为已点赞，false为未点赞
                isLiked = "0".equals(likeRecords.getIsCanceled());
            }
            nekndCourses.setLike(isLiked);
        }

        // 设置课程视频列表
        nekndCourses.setVideos(nekndCoursesVideos);
        return R.ok(nekndCourses);
    }
}