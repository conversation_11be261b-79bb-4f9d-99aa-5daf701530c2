{"doc": " 入学管理Controller\n \n <AUTHOR>\n @date 2024-12-26\n", "fields": [], "enumConstants": [], "methods": [{"name": "getCollegeChartData", "paramTypes": [], "doc": " 获取学院图表数据\n"}, {"name": "getByCollegeDataList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取学院数据列表\n"}, {"name": "list", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询入学管理列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndInductionSchool"], "doc": " 导出入学管理列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入入学信息\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取入学管理详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": " 新增入学管理\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndInductionSchool"], "doc": " 修改入学管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除入学管理\n"}, {"name": "audit", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 审核入学申请\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " 获取入学统计信息\n"}, {"name": "getByYear", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 按入学年份查询\n"}, {"name": "getMyApplications", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前用户的入学申请\n"}], "constructors": []}