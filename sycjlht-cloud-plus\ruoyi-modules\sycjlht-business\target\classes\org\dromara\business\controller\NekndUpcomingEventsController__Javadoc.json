{"doc": " 活动预告信息Controller\n \n <AUTHOR>\n @date 2024-10-10\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询活动预告信息列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询活动预告信息列表（匿名访问，自动更新状态）\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndUpcomingEvents"], "doc": " 导出活动预告信息列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入活动预告信息\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取活动预告信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": " 新增活动预告信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": " 修改活动预告信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除活动预告信息\n"}], "constructors": []}