{"doc": " 报告管理Service业务层处理\n\n <AUTHOR>\n @date 2024-09-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndReportInformationById", "paramTypes": ["java.lang.Integer"], "doc": " 查询报告管理\n\n @param id 报告管理主键\n @return 报告管理\n"}, {"name": "selectNekndReportInformationList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": " 查询报告管理列表\n\n @param nekndReportInformation 报告管理\n @return 报告管理\n"}, {"name": "insertNekndReportInformation", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": " 新增报告管理\n\n @param nekndReportInformation 报告管理\n @return 结果\n"}, {"name": "updateNekndReportInformation", "paramTypes": ["org.dromara.business.domain.NekndReportInformation"], "doc": " 修改报告管理\n\n @param nekndReportInformation 报告管理\n @return 结果\n"}, {"name": "deleteNekndReportInformationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除报告管理\n\n @param ids 需要删除的报告管理主键\n @return 结果\n"}, {"name": "deleteNekndReportInformationById", "paramTypes": ["java.lang.Integer"], "doc": " 删除报告管理信息\n\n @param id 报告管理主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询报告管理列表\n\n @param nekndReportInformation 报告管理\n @param pageQuery 分页查询\n @return 报告管理集合\n"}, {"name": "queryPublicPageList", "paramTypes": ["org.dromara.business.domain.NekndReportInformation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询公开报告管理列表\n\n @param nekndReportInformation 报告管理\n @param pageQuery 分页查询\n @return 报告管理集合\n"}], "constructors": []}