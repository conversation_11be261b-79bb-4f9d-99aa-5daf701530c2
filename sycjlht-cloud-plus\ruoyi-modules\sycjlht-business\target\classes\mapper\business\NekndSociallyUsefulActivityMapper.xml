<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndSociallyUsefulActivityMapper">
    
    <resultMap type="NekndSociallyUsefulActivity" id="NekndSociallyUsefulActivityResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="title"    column="title"    />
        <result property="contentTitle"    column="content_title"    />
        <result property="content"    column="content"    />
        <result property="timeNode"    column="time_node"    />
        <result property="sourceUri"    column="source_uri"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNekndSociallyUsefulActivityVo">
        select id, cover_uri, title, content_title, content, time_node, source_uri, del_flag, create_time, update_time from neknd_socially_useful_activity
    </sql>

    <select id="selectNekndSociallyUsefulActivityList" parameterType="NekndSociallyUsefulActivity" resultMap="NekndSociallyUsefulActivityResult">
        <include refid="selectNekndSociallyUsefulActivityVo"/>
        <where>  
            <if test="coverUri != null  and coverUri != ''"> and cover_uri = #{coverUri}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="contentTitle != null  and contentTitle != ''"> and content_title = #{contentTitle}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="timeNode != null  and timeNode != ''"> and time_node = #{timeNode}</if>
            <if test="sourceUri != null  and sourceUri != ''"> and source_uri = #{sourceUri}</if>
        </where>
    </select>
    
    <select id="selectNekndSociallyUsefulActivityById" parameterType="Integer" resultMap="NekndSociallyUsefulActivityResult">
        <include refid="selectNekndSociallyUsefulActivityVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndSociallyUsefulActivity" parameterType="NekndSociallyUsefulActivity" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_socially_useful_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">cover_uri,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="contentTitle != null and contentTitle != ''">content_title,</if>
            <if test="content != null">content,</if>
            <if test="timeNode != null and timeNode != ''">time_node,</if>
            <if test="sourceUri != null">source_uri,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">#{coverUri},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="contentTitle != null and contentTitle != ''">#{contentTitle},</if>
            <if test="content != null">#{content},</if>
            <if test="timeNode != null and timeNode != ''">#{timeNode},</if>
            <if test="sourceUri != null">#{sourceUri},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNekndSociallyUsefulActivity" parameterType="NekndSociallyUsefulActivity">
        update neknd_socially_useful_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null and coverUri != ''">cover_uri = #{coverUri},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="contentTitle != null and contentTitle != ''">content_title = #{contentTitle},</if>
            <if test="content != null">content = #{content},</if>
            <if test="timeNode != null and timeNode != ''">time_node = #{timeNode},</if>
            <if test="sourceUri != null">source_uri = #{sourceUri},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndSociallyUsefulActivityById" parameterType="Integer">
        delete from neknd_socially_useful_activity where id = #{id}
    </delete>

    <delete id="deleteNekndSociallyUsefulActivityByIds" parameterType="String">
        delete from neknd_socially_useful_activity where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>