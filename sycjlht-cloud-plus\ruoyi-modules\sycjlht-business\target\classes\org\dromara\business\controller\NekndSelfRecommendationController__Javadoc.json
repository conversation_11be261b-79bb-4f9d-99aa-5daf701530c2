{"doc": " 自荐信息控制器\n \n <AUTHOR>\n @date 2024-05-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询服务商自己的自荐信息列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSelfRecommendation"], "doc": " 导出自荐信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取自荐信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": " 新增自荐信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": " 修改自荐信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除自荐信息\n"}, {"name": "listByRequirementId", "paramTypes": ["java.lang.Integer", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据项目需求id查询服务商自荐信息列表\n"}, {"name": "updateDockingStatus", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String"], "doc": " 根据项目需求id和自荐信息id和对接状态修改服务商和项目需求的对接状态\n 企业点击确认对接后，将状态改为已对接\n"}, {"name": "getCount", "paramTypes": ["java.lang.Integer"], "doc": " 根据项目需求id查询服务商数量\n"}, {"name": "getDockingRecord", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询企业需求方自己的对接记录列表\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 根据自荐信息id修改自荐信息审核状态\n"}], "constructors": []}