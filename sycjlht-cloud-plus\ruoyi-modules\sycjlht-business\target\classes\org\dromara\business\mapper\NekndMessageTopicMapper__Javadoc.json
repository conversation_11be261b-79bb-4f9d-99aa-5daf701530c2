{"doc": " 留言主题Mapper接口\n\n <AUTHOR>\n @date 2024-08-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": " 查询留言主题\n\n @param id 留言主题主键\n @return 留言主题\n"}, {"name": "selectNekndMessageTopicList", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 查询留言主题列表\n\n @param nekndMessageTopic 留言主题\n @return 留言主题集合\n"}, {"name": "insertNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 新增留言主题\n\n @param nekndMessageTopic 留言主题\n @return 结果\n"}, {"name": "updateNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": " 修改留言主题\n\n @param nekndMessageTopic 留言主题\n @return 结果\n"}, {"name": "deleteNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": " 删除留言主题\n\n @param id 留言主题主键\n @return 结果\n"}, {"name": "deleteNekndMessageTopicByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除留言主题\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}