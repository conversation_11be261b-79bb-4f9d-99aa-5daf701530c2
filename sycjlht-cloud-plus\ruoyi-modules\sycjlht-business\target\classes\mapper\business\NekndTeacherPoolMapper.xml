<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndTeacherPoolMapper">
    
    <resultMap type="NekndTeacherPool" id="NekndTeacherPoolResult">
        <result property="id"    column="id"    />
        <result property="position"    column="position"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="name"    column="name"    />
        <result property="content"    column="content"    />
        <result property="certificateUri"    column="certificate_uri"    />
        <result property="professionalFields"    column="professional_fields"    />
        <result property="honoraryTitle"    column="honorary_title"    />
        <result property="honoraryEvents"    column="honorary_events"    />
        <result property="educationalExperience"    column="educational_experience"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="classify"    column="classify"    />
        <result property="school"    column="school"    />
        <result property="departmentName"    column="department_name"    />
        <result property="idCard"    column="id_card"    />
        <result property="educationLevel"    column="education_level"    />
        <result property="academicDegree"    column="academic_degree"    />
        <result property="personnelCategory"    column="personnel_category"    />
        <result property="isProfessionalTeacher"    column="is_professional_teacher"    />
        <result property="phone"    column="phone"    />
        <result property="sex"    column="sex"    />
        <result property="teacherId"    column="teacher_id"    />
    </resultMap>

    <sql id="selectNekndTeacherPoolVo">
        select id, position, cover_uri, name, content, certificate_uri, professional_fields, honorary_title, honorary_events, educational_experience, del_flag, create_by, create_time, update_by, update_time,classify, school, department_name, id_card, education_level, academic_degree, personnel_category, is_professional_teacher, phone, sex,teacher_id from neknd_teacher_pool
    </sql>

    <select id="selectNekndTeacherPoolList" parameterType="NekndTeacherPool" resultMap="NekndTeacherPoolResult">
        <include refid="selectNekndTeacherPoolVo"/>
        <where>
            del_flag = 0
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="classify != null  and classify != ''"> and classify = #{classify}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="professionalFields != null  and professionalFields != ''"> and professional_fields like concat('%', #{professionalFields}, '%')</if>
            <if test="school != null  and school != ''"> and school like concat('%', #{school}, '%')</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="departmentName != null  and departmentName != ''"> and department_name like concat('%', #{departmentName}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="educationLevel != null  and educationLevel != ''"> and education_level = #{educationLevel}</if>
            <if test="academicDegree != null  and academicDegree != ''"> and academic_degree = #{academicDegree}</if>
            <if test="personnelCategory != null  and personnelCategory != ''"> and personnel_category = #{personnelCategory}</if>
            <if test="isProfessionalTeacher != null  and isProfessionalTeacher != ''"> and is_professional_teacher = #{isProfessionalTeacher}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="teacherId != null  and teacherId != ''"> and teacher_id = #{teacherId}</if>
        </where>
    </select>
    
    <select id="selectNekndTeacherPoolById" parameterType="Integer" resultMap="NekndTeacherPoolResult">
        <include refid="selectNekndTeacherPoolVo"/>
        where del_flag = 0 and id = #{id}
    </select>
        
    <insert id="insertNekndTeacherPool" parameterType="NekndTeacherPool" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_teacher_pool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="position != null and position != ''">position,</if>
            <if test="coverUri != null">cover_uri,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="content != null">content,</if>
            <if test="certificateUri != null">certificate_uri,</if>
            <if test="professionalFields != null and professionalFields != ''">professional_fields,</if>
            <if test="honoraryTitle != null">honorary_title,</if>
            <if test="honoraryEvents != null">honorary_events,</if>
            <if test="educationalExperience != null">educational_experience,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="classify != null">classify,</if>
            <if test="school != null">school,</if>
            <if test="departmentName != null">department_name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="educationLevel != null">education_level,</if>
            <if test="academicDegree != null">academic_degree,</if>
            <if test="personnelCategory != null">personnel_category,</if>
            <if test="isProfessionalTeacher != null">is_professional_teacher,</if>
            <if test="phone != null">phone,</if>
            <if test="sex != null">sex,</if>
            <if test="teacherId != null">teacher_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="position != null and position != ''">#{position},</if>
            <if test="coverUri != null">#{coverUri},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="certificateUri != null">#{certificateUri},</if>
            <if test="professionalFields != null and professionalFields != ''">#{professionalFields},</if>
            <if test="honoraryTitle != null">#{honoraryTitle},</if>
            <if test="honoraryEvents != null">#{honoraryEvents},</if>
            <if test="educationalExperience != null">#{educationalExperience},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="classify != null">#{classify},</if>
            <if test="school != null">#{school},</if>
            <if test="departmentName != null">#{departmentName},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="educationLevel != null">#{educationLevel},</if>
            <if test="academicDegree != null">#{academicDegree},</if>
            <if test="personnelCategory != null">#{personnelCategory},</if>
            <if test="isProfessionalTeacher != null">#{isProfessionalTeacher},</if>
            <if test="phone != null">#{phone},</if>
            <if test="sex != null">#{sex},</if>
            <if test="teacherId != null">#{teacherId},</if>
         </trim>
    </insert>

    <update id="updateNekndTeacherPool" parameterType="NekndTeacherPool">
        update neknd_teacher_pool
        <trim prefix="SET" suffixOverrides=",">
            <if test="position != null and position != ''">position = #{position},</if>
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="content != null">content = #{content},</if>
            <if test="certificateUri != null">certificate_uri = #{certificateUri},</if>
            <if test="professionalFields != null and professionalFields != ''">professional_fields = #{professionalFields},</if>
            <if test="honoraryTitle != null">honorary_title = #{honoraryTitle},</if>
            <if test="honoraryEvents != null">honorary_events = #{honoraryEvents},</if>
            <if test="educationalExperience != null">educational_experience = #{educationalExperience},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="classify != null">classify = #{classify},</if>
            <if test="school != null">school = #{school},</if>
            <if test="departmentName != null">department_name = #{departmentName},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="educationLevel != null">education_level = #{educationLevel},</if>
            <if test="academicDegree != null">academic_degree = #{academicDegree},</if>
            <if test="personnelCategory != null">personnel_category = #{personnelCategory},</if>
            <if test="isProfessionalTeacher != null">is_professional_teacher = #{isProfessionalTeacher},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndTeacherPoolById" parameterType="Integer">
        delete from neknd_teacher_pool where id = #{id}
    </delete>

    <update id="updateNekndTeacherPoolToDeletedByIds" parameterType="String">
        update  neknd_teacher_pool set del_flag=2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>