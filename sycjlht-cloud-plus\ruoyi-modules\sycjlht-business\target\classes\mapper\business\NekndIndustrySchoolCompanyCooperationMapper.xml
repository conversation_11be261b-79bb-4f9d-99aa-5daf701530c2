<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustrySchoolCompanyCooperationMapper">
    
    <resultMap type="NekndIndustrySchoolCompanyCooperation" id="NekndIndustrySchoolCompanyCooperationResult">
        <result property="industryId"    column="industry_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="projectIntroduction"    column="project_introduction"    />
        <result property="partnersName"    column="partners_name"    />
        <result property="year"    column="year"    />
    </resultMap>

    <sql id="selectNekndIndustrySchoolCompanyCooperationVo">
        select industry_id, project_name, project_introduction, partners_name, year from neknd_industry_school_company_cooperation
    </sql>

    <select id="selectNekndIndustrySchoolCompanyCooperationList" parameterType="NekndIndustrySchoolCompanyCooperation" resultMap="NekndIndustrySchoolCompanyCooperationResult">
        <include refid="selectNekndIndustrySchoolCompanyCooperationVo"/>
        <where>  
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectIntroduction != null  and projectIntroduction != ''"> and project_introduction = #{projectIntroduction}</if>
            <if test="partnersName != null  and partnersName != ''"> and partners_name like concat('%', #{partnersName}, '%')</if>
            <if test="year != null "> and year = #{year}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustrySchoolCompanyCooperationByIndustryId" parameterType="Long" resultMap="NekndIndustrySchoolCompanyCooperationResult">
        <include refid="selectNekndIndustrySchoolCompanyCooperationVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustrySchoolCompanyCooperationByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_school_company_cooperation
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustrySchoolCompanyCooperation" parameterType="NekndIndustrySchoolCompanyCooperation" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_school_company_cooperation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="projectName != null">project_name,</if>
            <if test="projectIntroduction != null">project_introduction,</if>
            <if test="partnersName != null">partners_name,</if>
            <if test="year != null">year,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="projectIntroduction != null">#{projectIntroduction},</if>
            <if test="partnersName != null">#{partnersName},</if>
            <if test="year != null">#{year},</if>
        </trim>
        On Duplicate Key Update industry_id = values(industry_id),project_name = values(project_name),project_introduction = values(project_introduction),partners_name = values(partners_name),year = values(year)
    </insert>

    <update id="updateNekndIndustrySchoolCompanyCooperation" parameterType="NekndIndustrySchoolCompanyCooperation">
        update neknd_industry_school_company_cooperation
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="projectIntroduction != null">project_introduction = #{projectIntroduction},</if>
            <if test="partnersName != null">partners_name = #{partnersName},</if>
            <if test="year != null">year = #{year},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustrySchoolCompanyCooperationByIndustry" parameterType="Long">
        delete from neknd_industry_school_company_cooperation where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustrySchoolCompanyCooperationByIndustrys" parameterType="String">
        delete from neknd_industry_school_company_cooperation where industry_id in
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>