{"doc": " 关于平台信息编辑Controller\n \n <AUTHOR>\n @date 2025-02-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询关于平台信息编辑列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 导出关于平台信息编辑列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取关于平台信息编辑详细信息\n"}, {"name": "getAdminInfo", "paramTypes": ["java.lang.Integer"], "doc": " 管理端获取关于平台信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 新增关于平台信息编辑\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 修改关于平台信息编辑\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除关于平台信息编辑\n"}, {"name": "getLatestAbout", "paramTypes": [], "doc": " 获取最新的关于平台信息\n"}, {"name": "getAboutByPlatform", "paramTypes": ["java.lang.String"], "doc": " 按平台类型获取关于信息\n"}, {"name": "publish", "paramTypes": ["java.lang.Integer"], "doc": " 发布关于平台信息\n"}, {"name": "unpublish", "paramTypes": ["java.lang.Integer"], "doc": " 下线关于平台信息\n"}], "constructors": []}