{"doc": " 安全服务工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserId", "paramTypes": [], "doc": " 用户ID\n"}, {"name": "getDeptId", "paramTypes": [], "doc": " 获取部门ID\n"}, {"name": "getUsername", "paramTypes": [], "doc": " 获取用户账户\n"}, {"name": "getLoginUser", "paramTypes": [], "doc": " 获取用户\n"}, {"name": "getAuthentication", "paramTypes": [], "doc": " 获取Authentication\n"}, {"name": "encryptPassword", "paramTypes": ["java.lang.String"], "doc": " 生成BCryptPasswordEncoder密码\n\n @param password 密码\n @return 加密字符串\n"}, {"name": "matchesPassword", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 判断密码是否相同\n\n @param rawPassword 真实密码\n @param encodedPassword 加密后字符\n @return 结果\n"}, {"name": "isAdmin", "paramTypes": ["java.lang.Long"], "doc": " 是否为管理员\n\n @param userId 用户ID\n @return 结果\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 验证用户是否具备某权限\n\n @param permission 权限字符串\n @return 用户是否具备某权限\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.util.Collection", "java.lang.String"], "doc": " 判断是否包含权限\n\n @param authorities 权限列表\n @param permission 权限字符串\n @return 用户是否具备某权限\n"}, {"name": "hasRole", "paramTypes": ["java.lang.String"], "doc": " 验证用户是否拥有某个角色\n\n @param role 角色标识\n @return 用户是否具备某角色\n"}, {"name": "hasRole", "paramTypes": ["java.util.Collection", "java.lang.String"], "doc": " 判断是否包含角色\n\n @param roles 角色列表\n @param role 角色\n @return 用户是否具备某角色权限\n"}], "constructors": []}