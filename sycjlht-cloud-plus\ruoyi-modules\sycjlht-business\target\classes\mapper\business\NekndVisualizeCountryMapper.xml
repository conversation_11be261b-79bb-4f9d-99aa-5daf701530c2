<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndVisualizeCountryMapper">
    <resultMap id="participateData" type="hashmap">
        <result property="unitType" column="unit_type"/>
        <result property="count" column="count"/>
    </resultMap>
    <select id="getParticipateData" resultMap="participateData">
        select
            unit_type,
            count(nu.unit_type) as count
        from
            neknd_unit nu
        group by
            unit_type;
    </select>

    <resultMap id="provincialInvestmentRanking" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="specialGovernmentFunds" column="special_government_funds"/>
        <result property="utilizationFund" column="utilization_fund"/>
    </resultMap>
    <select id="getProvincialInvestmentRanking" resultMap="provincialInvestmentRanking">
        select
            nu.provincial_id,
            nu.provincial_name,
            sum(nup.special_government_funds) as special_government_funds,
            round(avg(utilization_fund), 2) as utilization_fund
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.unit_type = '3'
        group by
            nu.provincial_id;
    </select>

    <resultMap id="specialtyCoverageMatchRate" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="dominantIndustryMatchRate" column="dominant_industry_match_rate"/>
    </resultMap>
    <select id="getSpecialtyCoverageMatchRate" resultMap="specialtyCoverageMatchRate">
        select
            nu.provincial_id,
            nu.provincial_name,
            JSON_ARRAYAGG(nup.dominant_industry_match_rate) as dominant_industry_match_rate
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.unit_type = '3' and nup.dominant_industry_match_rate is not null
        group by
            nu.provincial_id;
    </select>

    <resultMap id="provincialLocalEmploymentRate" type="hashmap">
        <result property="id" column="id"/>
        <result property="unitName" column="unit_name"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="localEmploymentRate" column="local_employment_rate"/>
    </resultMap>
    <select id="getProvincialLocalEmploymentRate" resultMap="provincialLocalEmploymentRate">
        select
            nu.id,
            nu.unit_name,
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            nup.local_employment_rate
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.unit_type = '3';
    </select>

    <resultMap id="provincialCountryLevelPark" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="count" column="count"/>
    </resultMap>
    <select id="getProvincialCountryLevelPark" resultMap="provincialCountryLevelPark">
        select
            nu.provincial_id,
            nu.provincial_name,
            count(nup.park_type) as count
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nup.park_type = '1'
        group by
            nu.provincial_id;
    </select>

    <resultMap id="provincialNationalLevelPark" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="count" column="count"/>
    </resultMap>
    <select id="getProvincialNationalLevelSchool" resultMap="provincialNationalLevelPark">
        select
            nu.provincial_id,
            nu.provincial_name,
            count(nc.company_type) as count
        from
            neknd_company nc
        inner join
            neknd_unit nu on
            nc.belonging_park = nu.id
        where
            status = '1' and company_type = '国家级'
        group by
            nu.provincial_id ;
    </select>

    <resultMap id="provincialNationalLevelSchool" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="specialty" column="specialty"/>
    </resultMap>
    <select id="getProvincialNationalLevelMajor" resultMap="provincialNationalLevelSchool">
        select
            nu.provincial_id,
            nu.provincial_name,
            json_arrayagg(nup.specialty) as specialty
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nup.specialty is not null
        group by
            nu.provincial_id;
    </select>

    <resultMap id="provincialEnterpriseType" type="hashmap">
        <result property="id" column="id"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="companyType" column="company_type"/>
        <result property="count" column="count"/>
    </resultMap>
    <select id="getProvincialEnterpriseType" resultMap="provincialEnterpriseType">
        select
            nu.id,
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            nc.company_type,
            count(nc.company_type) as count
        from
            neknd_company nc
        inner join
            neknd_unit nu on
            nc.belonging_park = nu.id
        where
            nc.status = '0' and nc.company_type is not null
        group by
            company_type;
    </select>

    <resultMap id="provincialComprehensiveRanking" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="schoolEnterpriseDepth" column="school_enterprise_depth"/>
        <result property="employmentQuality" column="employment_quality"/>
        <result property="parkOutputRatio" column="park_output_ratio"/>
    </resultMap>
    <select id="getProvincialComprehensiveRanking" resultMap="provincialComprehensiveRanking">
        select
            nu.provincial_id,
            nu.provincial_name,
            json_arrayagg(nup.park_output_ratio) as park_output_ratio,
            round(avg(nup.school_enterprise_depth), 2) as school_enterprise_depth,
            round(avg(nup.employment_quality), 2) as employment_quality
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
                nu.id = nup.uid
        where
            nu.unit_type = '3'
        group by
            nu.provincial_id;
    </select>

    <resultMap id="industryEducationBenchmark" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="integratedInvestment" column="integrated_investment"/>
    </resultMap>
    <select id="getIndustryEducationBenchmark" resultMap="industryEducationBenchmark">
        select
            nu.provincial_id,
            nu.provincial_name,
            sum(niep.integrated_investment) as integrated_investment
        from
            neknd_industry_education_projects niep
        inner join
            neknd_unit nu on
            niep.belonging_park = nu.id
        group by
            nu.provincial_id;
    </select>

    <resultMap id="maximumPark" type="hashmap">
        <result property="unitName" column="unit_name"/>
        <result property="amountInPlace" column="amount_in_place"/>
    </resultMap>
    <select id="getMaximumPark" resultMap="maximumPark">
        select
            nu.unit_name ,
            COALESCE(nup.amount_in_place, 0) as amount_in_place
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.unit_type = '3'
        group by
            nu.id;
    </select>

    <resultMap id="financialSupport" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="specialGovernmentFunds" column="special_government_funds"/>
    </resultMap>
    <select id="getFinancialSupport" resultMap="financialSupport">
        select
            nu.provincial_id,
            nu.provincial_name,
            sum(nup.special_government_funds) as special_government_funds
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.unit_type = '3'
        group by
            nu.provincial_id;
    </select>

    <resultMap id="featuredIndustryPark" type="hashmap">
        <result property="unitName" column="unit_name"/>
        <result property="featuredMajorCount" column="featured_major_count"/>
    </resultMap>
    <select id="getFeaturedIndustryPark" resultMap="featuredIndustryPark">
        select
            nu.unit_name,
            sum(niep.featured_major_count) as featured_major_count
        from
            neknd_industry_education_projects niep
        inner join
            neknd_unit nu on
            niep.belonging_park = nu.id
        where
            niep.featured_major_count is not null
        group by
            niep.belonging_park;
    </select>

    <resultMap id="keyLeadingEnterpriseAndSchool" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="belongingPark" column="belonging_park"/>
        <result property="integratedInvestment" column="integrated_investment"/>
    </resultMap>
    <select id="getKeyLeadingEnterpriseAndSchool" resultMap="keyLeadingEnterpriseAndSchool">
        select
            nu.provincial_id,
            nu.provincial_name,
            niep.belonging_park,
            sum(niep.integrated_investment) as integrated_investment
        from
            neknd_industry_education_projects niep
        inner join
            neknd_unit nu on
            niep.belonging_park = nu.id
        where
            niep.integrated_investment is not null
        group by
            nu.provincial_id;
    </select>

    <resultMap id="warningPanel" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="parkOutputRatio" column="park_output_ratio"/>
        <result property="schoolEnterpriseDepth" column="school_enterprise_depth"/>
        <result property="employmentQuality" column="employment_quality"/>
    </resultMap>
    <select id="getWarningPanel" resultMap="warningPanel">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            json_arrayagg(nup.park_output_ratio) as park_output_ratio,
            round(avg(nup.school_enterprise_depth), 2) as school_enterprise_depth,
            round(avg(nup.employment_quality), 2) as employment_quality
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        where
            nu.unit_type = '3'
        group by
            nu.city_id;
    </select>

    <resultMap id="fundUsageRate" type="hashmap">
        <result property="provincialId" column="provincial_id" />
        <result property="provincialName" column="provincial_name" />
        <result property="cityId" column="city_id" />
        <result property="cityName" column="city_name" />
        <result property="utilizationFund" column="utilization_fund" />
    </resultMap>
    <select id="getFundUsageRate" resultMap="fundUsageRate">
        select
            nu.provincial_id,
            nu.provincial_name,
            nu.city_id,
            nu.city_name,
            round(avg(nup.utilization_fund), 2) as utilization_fund
        from
            neknd_unit nu
        inner join
            neknd_unit_park nup on
            nu.id = nup.uid
        group by
            nu.city_id
        having
            round(avg(nup.utilization_fund), 2) &lt; 0.3;
    </select>
</mapper>