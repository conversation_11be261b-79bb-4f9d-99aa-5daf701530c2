{"doc": " 留言箱Controller\n \n <AUTHOR>\n @date 2024-06-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndMessageBox", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询留言箱列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndMessageBox"], "doc": " 导出留言箱列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取留言箱详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 新增留言\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 修改留言\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除留言\n"}, {"name": "sendToMultiple", "paramTypes": ["org.dromara.business.domain.NekndMessageBox", "java.util.List"], "doc": " 发送留言给多个用户\n"}, {"name": "mark<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long"], "doc": " 标记消息为已读\n"}, {"name": "getUnreadCount", "paramTypes": [], "doc": " 获取未读消息数量\n"}, {"name": "getMessageTopics", "paramTypes": [], "doc": " 获取消息主题列表\n"}, {"name": "reply", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": " 回复消息\n"}, {"name": "getConversation", "paramTypes": ["java.lang.Long", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取对话记录\n"}], "constructors": []}