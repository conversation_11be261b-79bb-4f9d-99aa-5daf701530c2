{"doc": " 科研成果Controller\n\n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询科研成果列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndResearchAchievement"], "doc": " 导出科研成果列表\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取科研成果详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": " 新增科研成果\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": " 修改科研成果\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除科研成果\n"}, {"name": "recommendOutcomes", "paramTypes": [], "doc": " 推荐成果\n"}, {"name": "auditing", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 审核科研成果\n"}, {"name": "getInfoAchievement", "paramTypes": ["java.lang.Integer"], "doc": " 获取科研成果详细信息\n"}, {"name": "getAchievementDisplay", "paramTypes": [], "doc": " 科研成果展示\n"}], "constructors": []}