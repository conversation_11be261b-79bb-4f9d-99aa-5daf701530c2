<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndPersonEvaluateMapper">
    
    <resultMap type="NekndPersonEvaluate" id="NekndPersonEvaluateResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="evaluationCycle"    column="evaluation_cycle"    />

    </resultMap>

    <sql id="selectNekndPersonEvaluateVo">
        select id, user_id, dept_id, del_flag, create_by, create_time, update_by, update_time, remark, status, user_name, evaluation_cycle from neknd_person_evaluate
    </sql>

    <select id="selectNekndPersonEvaluateList" parameterType="NekndPersonEvaluate" resultMap="NekndPersonEvaluateResult">
        <include refid="selectNekndPersonEvaluateVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
<!--            <if test="userIds != null ">-->
<!--                and user_id in-->
<!--                <foreach item="id" index="index" collection="userIds" open="(" separator="," close=")">-->
<!--                    #{id}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="userIds != null and userIds.size() > 0">
                AND user_id IN
                <foreach item="id" index="index" collection="userIds" open="(" separator="," close=")">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndPersonEvaluateById" parameterType="Integer" resultMap="NekndPersonEvaluateResult">
        <include refid="selectNekndPersonEvaluateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndPersonEvaluate" parameterType="NekndPersonEvaluate" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_person_evaluate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="userName != null">user_name,</if>
            <if test="evaluationCycle != null">evaluation_cycle,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="userName != null">#{userName},</if>
            <if test="evaluationCycle != null">#{evaluationCycle},</if>
         </trim>
    </insert>

    <update id="updateNekndPersonEvaluate" parameterType="NekndPersonEvaluate">
        update neknd_person_evaluate
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="evaluationCycle != null">evaluation_cycle = #{evaluationCycle},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndPersonEvaluateById" parameterType="Integer">
        delete from neknd_person_evaluate where id = #{id}
    </delete>

    <delete id="deleteNekndPersonEvaluateByIds" parameterType="String">
        delete from neknd_person_evaluate where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>