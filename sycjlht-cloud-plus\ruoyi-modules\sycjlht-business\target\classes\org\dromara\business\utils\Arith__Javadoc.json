{"doc": " 精确的浮点数运算\n\n <AUTHOR>\n", "fields": [{"name": "DEF_DIV_SCALE", "doc": "默认除法运算精度 "}], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["double", "double"], "doc": " 提供精确的加法运算。\n @param v1 被加数\n @param v2 加数\n @return 两个参数的和\n"}, {"name": "sub", "paramTypes": ["double", "double"], "doc": " 提供精确的减法运算。\n @param v1 被减数\n @param v2 减数\n @return 两个参数的差\n"}, {"name": "mul", "paramTypes": ["double", "double"], "doc": " 提供精确的乘法运算。\n @param v1 被乘数\n @param v2 乘数\n @return 两个参数的积\n"}, {"name": "div", "paramTypes": ["double", "double"], "doc": " 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到\n 小数点以后10位，以后的数字四舍五入。\n @param v1 被除数\n @param v2 除数\n @return 两个参数的商\n"}, {"name": "div", "paramTypes": ["double", "double", "int"], "doc": " 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指\n 定精度，以后的数字四舍五入。\n @param v1 被除数\n @param v2 除数\n @param scale 表示表示需要精确到小数点以后几位。\n @return 两个参数的商\n"}, {"name": "round", "paramTypes": ["double", "int"], "doc": " 提供精确的小数位四舍五入处理。\n @param v 需要四舍五入的数字\n @param scale 小数点后保留几位\n @return 四舍五入后的结果\n"}, {"name": "avg", "paramTypes": ["java.util.List", "int"], "doc": " 提供精确的平均值运算。\n @param values 需要计算平均值的数值列表\n @param scale 表示需要精确到小数点以后几位。\n @return 平均值\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "这个类不能实例化 "}]}