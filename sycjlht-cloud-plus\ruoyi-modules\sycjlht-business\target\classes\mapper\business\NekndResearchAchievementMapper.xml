<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndResearchAchievementMapper">

    <resultMap type="NekndResearchAchievement" id="NekndResearchAchievementResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="flag"    column="flag"    />
        <result property="stage" column="stage" />
        <result property="attribute" column="attribute" />
        <result property="price" column="price" />
        <result property="fileAddress" column="file_address" />
        <result property="patent" column="patent" />
        <result property="reviewStatus" column="review_status" />
        <result property="year" column="year" />
        <result property="parkType"    column="park_type"    />
    </resultMap>

    <sql id="selectNekndResearchAchievementVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, create_id, dept_id, title, content, flag, stage, attribute, price, file_address, patent, review_status, park_type from neknd_research_achievement
    </sql>

    <select id="selectNekndResearchAchievementList" parameterType="NekndResearchAchievement" resultMap="NekndResearchAchievementResult">
        select u.id, u.del_flag, u.create_by, u.create_time,u.review_status, u.update_by, u.update_time, u.remark, u.dept_id, u.title, u.content, u.flag,u.stage,u.attribute,u.price,u.file_address,u.patent
        from neknd_research_achievement u  left join sys_dept d on u.dept_id = d.dept_id
        <where>
            u.del_flag=0
            <if test="deptId != null "> and u.dept_id = #{deptId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="stage != null  and stage != ''"> and stage = #{stage}</if>
            <if test="attribute != null  and attribute != ''"> and attribute = #{attribute}</if>
            <if test="createTime != null  and createTime != ''"> and create_time = #{createTime}</if>
            <if test="reviewStatus != null and reviewStatus != ''">and review_status= #{reviewStatus}</if>
            <if test="parkType != null  and parkType != ''"> and park_type = #{parkType}</if>
            <if test="patent != null  and patent != ''"> and patent = #{patent}</if>
            ${params.dataScope}
        </where>
        order by u.create_time desc
    </select>

    <select id="selectNekndResearchAchievementById" parameterType="Integer" resultMap="NekndResearchAchievementResult">
        <include refid="selectNekndResearchAchievementVo"/>
        where id = #{id} and del_flag=0
    </select>

    <insert id="insertNekndResearchAchievement" parameterType="NekndResearchAchievement" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_research_achievement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="flag != null">flag,</if>

            <if test="stage != null">stage,</if>
            <if test="attribute != null">attribute,</if>
            <if test="price != null">price,</if>
            <if test="fileAddress != null">file_address,</if>
            <if test="patent != null">patent,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="createId!=null">create_id,</if>
            <if test="parkType != null">park_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="flag != null">#{flag},</if>

            <if test="stage != null">#{stage},</if>
            <if test="attribute != null">#{attribute},</if>
            <if test="price != null">#{price},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="patent != null">#{patent},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="createId != null">#{createId},</if>
            <if test="parkType != null">#{parkType},</if>
         </trim>
    </insert>

    <update id="updateNekndResearchAchievement" parameterType="NekndResearchAchievement">
        update neknd_research_achievement
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="flag != null">flag = #{flag},</if>

            <if test="stage != null">stage=#{stage},</if>
            <if test="attribute != null">attribute=#{attribute},</if>
            <if test="price != null">price=#{price},</if>
            <if test="fileAddress != null">file_address=#{fileAddress},</if>
            <if test="patent != null">patent=#{patent},</if>
            <if test="reviewStatus != null">review_status=#{reviewStatus},</if>
            <if test="createId != null">create_id=#{createId},</if>
            <if test="parkType != null">park_type = #{parkType},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteNekndResearchAchievementById" parameterType="Integer">
       update neknd_research_achievement set del_flag=2  where id = #{id}
    </update>

    <update id="deleteNekndResearchAchievementByIds" parameterType="String">
        update neknd_research_achievement set del_flag=2  where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="getRecommendOutcomes" resultMap="NekndResearchAchievementResult">
        select * from neknd_research_achievement where del_flag=0
    </select>
    <select id="getSanYun" parameterType="integer" resultMap="NekndResearchAchievementResult">
        select * from neknd_research_achievement where dept_id=#{deptId} and del_flag=0
    </select>


    <select id="getSift" parameterType="NekndResearchAchievement" resultMap="NekndResearchAchievementResult">
        select id, del_flag, create_by,create_time, year(create_time) as year, update_by, update_time, remark, dept_id, title, content, flag,stage,attribute,price,file_address,patent,review_status,create_id from neknd_research_achievement
        <where>
            review_status= 1 and del_flag= 0
            <if test="deptId != null and deptId != ''"> and dept_id = #{deptId}</if>
            <if test="title != null  and title != ''"> and title like concat('%' ,#{title}, '%') </if>
            <if test="searchValue != null  and searchValue != ''"> and title like concat('%' ,#{searchValue}, '%') </if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="stage != null and stage != ''">and stage=#{stage}</if>
            <if test="year != null and year != '' and year != '2020以前'">and year(create_time)=#{year}</if>
            <if test="reviewStatus != null and reviewStatus != ''">and review_status = #{reviewStatus}</if>
            <if test="year == '2020以前'">and year(create_time) &lt;= #{year}</if>
            <if test="attribute != null and attribute != ''">and attribute = #{attribute}</if>

        </where>
        order by create_time desc
    </select>


    <select id="getInfoAchievement" parameterType="Integer" resultMap="NekndResearchAchievementResult">
        select id, del_flag, create_by,create_time, year(create_time) as year, update_by, update_time, remark, dept_id, title, content, flag,stage,attribute,price,file_address,patent,review_status,create_id from neknd_research_achievement
        where id = #{id} and review_status= 1 and del_flag=0
    </select>
    <select id="getCountAchievement" resultType="java.lang.Integer">
        select count(1) from neknd_research_achievement where review_status= 1 and del_flag=0
    </select>
    <resultMap id="getAchievementDisplayMap" type="hashmap">
        <result property="schoolName" column="schoolName"/>
        <result property="researchTitle" column="researchTitle"/>
        <result property="outcomeCategory" column="outcomeCategory"/>
        <result property="patentNumber" column="patentNumber"/>
    </resultMap>
    <select id="getAchievementDisplay" resultMap="getAchievementDisplayMap">
        select c.company_name as "schoolName",r.title as "researchTitle"
             ,IF(r.flag=0,"新技术",IF(r.flag=1,"新工艺",IF(r.flag=2,"新产品"
                 ,IF(r.flag=3,"新材料",IF(r.flag=4,"新装备",IF(r.flag=5,"农业/生物新品种"
                     ,IF(r.flag=6,"矿产新品种","其他应用技术"))))))) as "outcomeCategory"
             ,patent as "patentNumber" from neknd_research_achievement as r LEFT JOIN neknd_company as c on r.dept_id=c.dept_id and c.del_flag=0 and r.del_flag=0 and r.review_status=1;
    </select>

    <select id="selectResearchAchievementList" parameterType="NekndResearchAchievement" resultMap="NekndResearchAchievementResult">
        <include refid="selectNekndResearchAchievementVo"/>
        <where>
            del_flag=0
            <if test="deptId != null "> and u.dept_id = #{deptId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="stage != null  and stage != ''"> and stage = #{stage}</if>
            <if test="attribute != null  and attribute != ''"> and attribute = #{attribute}</if>
            <if test="createTime != null  and createTime != ''"> and create_time = #{createTime}</if>
            <if test="reviewStatus != null and reviewStatus != ''">and review_status= #{reviewStatus}</if>
            <if test="parkType != null  and parkType != ''"> and park_type = #{parkType}</if>
            <if test="patent != null  and patent != ''"> and patent = #{patent}</if>
        </where>
        order by create_time desc
    </select>
</mapper>