{"doc": " 科普研学导师Service业务层处理\n\n <AUTHOR>\n @date 2025-06-09\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchMentorById", "paramTypes": ["java.lang.Integer"], "doc": " 查询科普研学导师\n\n @param id 科普研学导师主键\n @return 科普研学导师\n"}, {"name": "selectNekndScientificResearchMentorList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": " 查询科普研学导师列表\n\n @param nekndScientificResearchMentor 科普研学导师\n @return 科普研学导师\n"}, {"name": "insertNekndScientificResearchMentor", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": " 新增科普研学导师\n\n @param nekndScientificResearchMentor 科普研学导师\n @return 结果\n"}, {"name": "updateNekndScientificResearchMentor", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": " 修改科普研学导师\n\n @param nekndScientificResearchMentor 科普研学导师\n @return 结果\n"}, {"name": "deleteNekndScientificResearchMentorByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除科普研学导师\n\n @param ids 需要删除的科普研学导师主键\n @return 结果\n"}, {"name": "deleteNekndScientificResearchMentorById", "paramTypes": ["java.lang.Integer"], "doc": " 删除科普研学导师信息\n\n @param id 科普研学导师主键\n @return 结果\n"}, {"name": "selectResearchMentorByIdAndResearchId", "paramTypes": ["int", "int"], "doc": " 查询指定导师是否已经添加过指定科研项目\n @param mentorId\n @param researchId\n @return true  表示已经添加过 false 表示没有添加过\n"}], "constructors": []}