package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.system.api.model.LoginUser;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.business.domain.NekndTeacherPool;
import org.dromara.business.service.INekndTeacherPoolService;
import org.dromara.system.api.RemoteRoleService;
import org.dromara.common.satoken.utils.LoginHelper;
import org.apache.dubbo.config.annotation.DubboReference;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 师资库Controller
 *
 * <AUTHOR>
 * @date 2024-12-07
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/teacherPool")
public class NekndTeacherPoolController extends BaseController {
    private final INekndTeacherPoolService nekndTeacherPoolService;

    @DubboReference(check = false)
    private RemoteRoleService remoteRoleService;

    /**
     * 查询师资库列表
     */
    @PreAuthorize("@ss.hasPermi('system:teacherPool:list')")
    @GetMapping("/list")
    public TableDataInfo<NekndTeacherPool> list(NekndTeacherPool nekndTeacherPool, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return TableDataInfo.build(new ArrayList<>());
        }
        Long userId = loginUser.getUserId();
        List<Long> roleList = remoteRoleService.selectRoleListByUserId(userId);
        if (roleList.size() == 0) {
            return TableDataInfo.build(new ArrayList<>());
        }
        // 如果是教育端，根据创建人查询
        if (roleList.contains(4L)) {
            nekndTeacherPool.setCreateBy(userId);
        }

        // 使用现有的查询方法获取所有数据，然后手动分页
        List<NekndTeacherPool> allList = nekndTeacherPoolService.selectNekndTeacherPoolList(nekndTeacherPool);

        // 手动分页
        int pageNum = pageQuery.getPageNum() != null ? pageQuery.getPageNum() : 1;
        int pageSize = pageQuery.getPageSize() != null ? pageQuery.getPageSize() : 10;
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allList.size());

        List<NekndTeacherPool> pageList = new ArrayList<>();
        if (startIndex < allList.size()) {
            pageList = allList.subList(startIndex, endIndex);
        }

        TableDataInfo<NekndTeacherPool> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(pageList);
        dataInfo.setTotal(allList.size());
        dataInfo.setCode(200);
        dataInfo.setMsg("查询成功");
        return dataInfo;
    }

    /**
     * 匿名查询师资库列表
     */
    @GetMapping("/getList")
    @SaIgnore
    public TableDataInfo<NekndTeacherPool> getList(NekndTeacherPool nekndTeacherPool, PageQuery pageQuery) {
        // 使用现有的查询方法获取所有数据，然后手动分页
        List<NekndTeacherPool> allList = nekndTeacherPoolService.selectNekndTeacherPoolList(nekndTeacherPool);

        // 手动分页
        int pageNum = pageQuery.getPageNum() != null ? pageQuery.getPageNum() : 1;
        int pageSize = pageQuery.getPageSize() != null ? pageQuery.getPageSize() : 10;
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allList.size());

        List<NekndTeacherPool> pageList = new ArrayList<>();
        if (startIndex < allList.size()) {
            pageList = allList.subList(startIndex, endIndex);
        }

        TableDataInfo<NekndTeacherPool> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(pageList);
        dataInfo.setTotal(allList.size());
        dataInfo.setCode(200);
        dataInfo.setMsg("查询成功");
        return dataInfo;
    }

    /**
     * 导出师资库列表
     */
    @PreAuthorize("@ss.hasPermi('system:teacherPool:export')")
    @Log(title = "师资库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NekndTeacherPool nekndTeacherPool) {
        List<NekndTeacherPool> list = nekndTeacherPoolService.selectNekndTeacherPoolList(nekndTeacherPool);
        ExcelUtil.exportExcel(list, "师资库数据", NekndTeacherPool.class, response);
    }

    @Log(title = "导入师资库", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<String> importData(@RequestPart("file") MultipartFile file) throws Exception {
        ExcelResult<NekndTeacherPool> result = ExcelUtil.importExcel(file.getInputStream(), NekndTeacherPool.class,
                true);
        List<NekndTeacherPool> nekndTeacherPoolList = result.getList();
        Long userId = LoginHelper.getUserId();
        String message = nekndTeacherPoolService.importTeacherPool(nekndTeacherPoolList, userId.toString());
        return R.ok(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "师资库数据", NekndTeacherPool.class, response);
    }

    /**
     * 获取师资库详细信息
     */
    @SaIgnore
    @GetMapping(value = "/{id}")
    public R<NekndTeacherPool> getInfo(@PathVariable("id") Integer id) {
        return R.ok(nekndTeacherPoolService.selectNekndTeacherPoolById(id));
    }

    /**
     * 新增师资库
     */
    @PreAuthorize("@ss.hasPermi('system:teacherPool:add')")
    @Log(title = "师资库", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@RequestBody NekndTeacherPool nekndTeacherPool) {
        return toAjax(nekndTeacherPoolService.insertNekndTeacherPool(nekndTeacherPool));
    }

    /**
     * 修改师资库
     */
    @PreAuthorize("@ss.hasPermi('system:teacherPool:edit')")
    @Log(title = "师资库", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@RequestBody NekndTeacherPool nekndTeacherPool) {
        return toAjax(nekndTeacherPoolService.updateNekndTeacherPool(nekndTeacherPool));
    }

    /**
     * 删除师资库
     */
    @PreAuthorize("@ss.hasPermi('system:teacherPool:remove')")
    @Log(title = "师资库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Integer[] ids) {
        return toAjax(nekndTeacherPoolService.deleteNekndTeacherPoolByIds(ids));
    }
}
