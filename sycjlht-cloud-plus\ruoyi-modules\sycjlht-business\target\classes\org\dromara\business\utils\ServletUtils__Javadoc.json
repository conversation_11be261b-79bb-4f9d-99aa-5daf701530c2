{"doc": " 客户端工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getParameter", "paramTypes": ["java.lang.String"], "doc": " 获取String参数\n"}, {"name": "getParameter", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取String参数\n"}, {"name": "getParameterToInt", "paramTypes": ["java.lang.String"], "doc": " 获取Integer参数\n"}, {"name": "getParameterToInt", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取Integer参数\n"}, {"name": "getParameterToBool", "paramTypes": ["java.lang.String"], "doc": " 获取Boolean参数\n"}, {"name": "getParameterToBool", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 获取Boolean参数\n"}, {"name": "getParams", "paramTypes": ["jakarta.servlet.ServletRequest"], "doc": " 获得所有请求参数\n\n @param request 请求对象{@link ServletRequest}\n @return Map\n"}, {"name": "getParamMap", "paramTypes": ["jakarta.servlet.ServletRequest"], "doc": " 获得所有请求参数\n\n @param request 请求对象{@link ServletRequest}\n @return Map\n"}, {"name": "getRequest", "paramTypes": [], "doc": " 获取request\n"}, {"name": "getResponse", "paramTypes": [], "doc": " 获取response\n"}, {"name": "getSession", "paramTypes": [], "doc": " 获取session\n"}, {"name": "renderString", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": " 将字符串渲染到客户端\n\n @param response 渲染对象\n @param string 待渲染的字符串\n"}, {"name": "isAjaxRequest", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 是否是Ajax异步请求\n\n @param request\n"}, {"name": "urlEncode", "paramTypes": ["java.lang.String"], "doc": " 内容编码\n\n @param str 内容\n @return 编码后的内容\n"}, {"name": "urlDecode", "paramTypes": ["java.lang.String"], "doc": " 内容解码\n\n @param str 内容\n @return 解码后的内容\n"}], "constructors": []}