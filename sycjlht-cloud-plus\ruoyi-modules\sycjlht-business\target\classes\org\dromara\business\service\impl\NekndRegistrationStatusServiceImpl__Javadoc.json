{"doc": " 培训报名Service业务层处理\n\n <AUTHOR>\n @date 2024-06-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": " 查询培训报名\n\n @param id 培训报名主键\n @return 培训报名\n"}, {"name": "selectNekndRegistrationStatusList", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 查询培训报名列表\n\n @param nekndRegistrationStatus 培训报名\n @return 培训报名\n"}, {"name": "insertNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 新增培训报名\n\n @param nekndRegistrationStatus 培训报名\n @return 结果\n"}, {"name": "updateNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 修改培训报名\n\n @param nekndRegistrationStatus 培训报名\n @return 结果\n"}, {"name": "deleteNekndRegistrationStatusByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除培训报名\n\n @param ids 需要删除的培训报名主键\n @return 结果\n"}, {"name": "deleteNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": " 删除培训报名信息\n\n @param id 培训报名主键\n @return 结果\n"}, {"name": "queryEnterpriseTraining", "paramTypes": ["int", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询企业培训\n\n @param userId 用户ID\n @param pageQuery 分页查询\n @return 培训列表\n"}, {"name": "cancelRegistration", "paramTypes": ["int", "java.lang.Integer"], "doc": " 取消报名\n\n @param userId 用户ID\n @param trainingId 培训ID\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询培训报名列表\n\n @param nekndRegistrationStatus 培训报名\n @param pageQuery 分页查询\n @return 培训报名集合\n"}, {"name": "batch<PERSON><PERSON><PERSON>", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量审核\n\n @param ids 主键集合\n @param status 审核状态\n @return 结果\n"}, {"name": "processRegistration", "paramTypes": ["int", "org.dromara.business.domain.NekndRegistrationStatus"], "doc": " 处理报名\n\n @param userId 用户ID\n @param nekndRegistrationStatus 报名信息\n @return 结果\n"}, {"name": "getRegistrationStatus", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 获取报名状态\n\n @param userId 用户ID\n @param trainingId 培训ID\n @return 报名状态\n"}, {"name": "queryPersonalTraining", "paramTypes": ["int", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询个人培训\n\n @param userId 用户ID\n @param pageQuery 分页查询\n @return 培训列表\n"}], "constructors": []}