{"doc": " 用户点赞记录Controller\n \n <AUTHOR>\n @date 2025-04-03\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询用户点赞记录列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndLikeRecords"], "doc": " 导出用户点赞记录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取用户点赞记录详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": " 新增用户点赞记录\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": " 修改用户点赞记录\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除用户点赞记录\n"}, {"name": "toggleLike", "paramTypes": ["java.lang.Integer"], "doc": " 点赞和取消点赞切换\n"}, {"name": "checkLike", "paramTypes": ["java.lang.Integer"], "doc": " 检查用户是否已点赞课程\n"}, {"name": "getMyLikes", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取用户点赞的课程列表\n"}, {"name": "getCourseStats", "paramTypes": ["java.lang.Integer"], "doc": " 获取课程的点赞统计\n"}, {"name": "batchCancelLike", "paramTypes": ["java.util.List"], "doc": " 批量取消点赞\n"}], "constructors": []}