<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndScientificResearchCoursesMapper">
    
    <resultMap type="NekndScientificResearchCourses" id="NekndScientificResearchCoursesResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="content"    column="content"    />
        <result property="recommendedIndex"    column="recommended_index"    />
        <result property="cost"    column="cost"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="scientificResearchId"    column="scientific_research_id"    />
    </resultMap>

    <sql id="selectNekndScientificResearchCoursesVo">
        select id, title, cover_uri, content, recommended_index, cost, del_flag, create_id, create_by, create_time, update_by, update_time, scientific_research_id from neknd_scientific_research_courses
    </sql>

    <select id="selectNekndScientificResearchCoursesList" parameterType="NekndScientificResearchCourses" resultMap="NekndScientificResearchCoursesResult">
        <include refid="selectNekndScientificResearchCoursesVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="coverUri != null  and coverUri != ''"> and cover_uri = #{coverUri}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="recommendedIndex != null  and recommendedIndex != ''"> and recommended_index = #{recommendedIndex}</if>
            <if test="cost != null "> and cost = #{cost}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="scientificResearchId != null "> and scientific_research_id = #{scientificResearchId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndScientificResearchCoursesById" parameterType="Integer" resultMap="NekndScientificResearchCoursesResult">
        <include refid="selectNekndScientificResearchCoursesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndScientificResearchCourses" parameterType="NekndScientificResearchCourses" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_scientific_research_courses
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="coverUri != null">cover_uri,</if>
            <if test="content != null">content,</if>
            <if test="recommendedIndex != null">recommended_index,</if>
            <if test="cost != null">cost,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="scientificResearchId != null">scientific_research_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="coverUri != null">#{coverUri},</if>
            <if test="content != null">#{content},</if>
            <if test="recommendedIndex != null">#{recommendedIndex},</if>
            <if test="cost != null">#{cost},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="scientificResearchId != null">#{scientificResearchId},</if>
         </trim>
    </insert>

    <update id="updateNekndScientificResearchCourses" parameterType="NekndScientificResearchCourses">
        update neknd_scientific_research_courses
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="content != null">content = #{content},</if>
            <if test="recommendedIndex != null">recommended_index = #{recommendedIndex},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="scientificResearchId != null">scientific_research_id = #{scientificResearchId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndScientificResearchCoursesById" parameterType="Integer">
        delete from neknd_scientific_research_courses where id = #{id}
    </delete>

    <delete id="deleteNekndScientificResearchCoursesByIds" parameterType="String">
        delete from neknd_scientific_research_courses where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>