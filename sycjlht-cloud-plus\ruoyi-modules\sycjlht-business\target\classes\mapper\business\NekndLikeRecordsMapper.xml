<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndLikeRecordsMapper">
    
    <resultMap type="NekndLikeRecords" id="NekndLikeRecordsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="isCanceled"    column="is_canceled"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNekndLikeRecordsVo">
        select id, user_id, course_id, is_canceled, create_time, update_time from neknd_like_records
    </sql>

    <select id="selectNekndLikeRecordsList" parameterType="NekndLikeRecords" resultMap="NekndLikeRecordsResult">
        <include refid="selectNekndLikeRecordsVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="isCanceled != null  and isCanceled != ''"> and is_canceled = #{isCanceled}</if>
        </where>
    </select>
    
    <select id="selectNekndLikeRecordsById" parameterType="Long" resultMap="NekndLikeRecordsResult">
        <include refid="selectNekndLikeRecordsVo"/>
        where id = #{id}
    </select>
    <select id="selectLikeRecordsByUserIdAndCourseId" resultMap="NekndLikeRecordsResult">
        <include refid="selectNekndLikeRecordsVo"/>
        where user_id = #{userId} and course_id = #{courseId}
    </select>

    <insert id="insertNekndLikeRecords" parameterType="NekndLikeRecords">
        insert into neknd_like_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="isCanceled != null">is_canceled,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="isCanceled != null">#{isCanceled},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNekndLikeRecords" parameterType="NekndLikeRecords">
        update neknd_like_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="isCanceled != null">is_canceled = #{isCanceled},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndLikeRecordsById" parameterType="Long">
        delete from neknd_like_records where id = #{id}
    </delete>

    <delete id="deleteNekndLikeRecordsByIds" parameterType="String">
        delete from neknd_like_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>