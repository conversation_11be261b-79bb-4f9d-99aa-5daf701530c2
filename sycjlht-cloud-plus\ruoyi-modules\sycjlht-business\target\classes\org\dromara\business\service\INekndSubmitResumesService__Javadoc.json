{"doc": " 查看投递简历Service接口\n\n <AUTHOR>\n @date 2024-05-12\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSubmitResumesById", "paramTypes": ["java.lang.Integer"], "doc": " 查询查看投递简历\n\n @param id 查看投递简历主键\n @return 查看投递简历\n"}, {"name": "selectNekndSubmitResumesList", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": " 查询查看投递简历列表\n\n @param nekndSubmitResumes 查看投递简历\n @return 查看投递简历集合\n"}, {"name": "insertNekndSubmitResumes", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": " 新增查看投递简历\n\n @param nekndSubmitResumes 查看投递简历\n @return 结果\n"}, {"name": "updateNekndSubmitResumes", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": " 修改查看投递简历\n\n @param nekndSubmitResumes 查看投递简历\n @return 结果\n"}, {"name": "deleteNekndSubmitResumesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除查看投递简历\n\n @param ids 需要删除的查看投递简历主键集合\n @return 结果\n"}, {"name": "deleteNekndSubmitResumesById", "paramTypes": ["java.lang.Integer"], "doc": " 删除查看投递简历信息\n\n @param id 查看投递简历主键\n @return 结果\n"}], "constructors": []}