{"doc": " 数字人才画像Service接口\n\n <AUTHOR>\n @date 2024-11-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPortraitTalentById", "paramTypes": ["java.lang.Integer"], "doc": " 查询数字人才画像\n\n @param id 数字人才画像主键\n @return 数字人才画像\n"}, {"name": "selectPortraitTalentInfoById", "paramTypes": ["java.lang.Integer"], "doc": " 查询数字人才画像\n\n @param id 数字人才画像主键\n @return 数字人才画像\n"}, {"name": "selectNekndPortraitTalentList", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": " 查询数字人才画像列表\n\n @param nekndPortraitTalent 数字人才画像\n @return 数字人才画像集合\n"}, {"name": "selectPortraitTalentList", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": " 查询数字人才画像列表\n\n @param nekndPortraitTalent 数字人才画像\n @return 数字人才画像集合\n"}, {"name": "insertNekndPortraitTalent", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": " 新增数字人才画像\n\n @param nekndPortraitTalent 数字人才画像\n @return 结果\n"}, {"name": "updateNekndPortraitTalent", "paramTypes": ["org.dromara.business.domain.NekndPortraitTalent"], "doc": " 修改数字人才画像\n\n @param nekndPortraitTalent 数字人才画像\n @return 结果\n"}, {"name": "deleteNekndPortraitTalentByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除数字人才画像\n\n @param ids 需要删除的数字人才画像主键集合\n @return 结果\n"}, {"name": "deleteNekndPortraitTalentById", "paramTypes": ["java.lang.Integer"], "doc": " 删除数字人才画像信息\n\n @param id 数字人才画像主键\n @return 结果\n"}], "constructors": []}