<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndPlatformConfigAboutMapper">
    
    <resultMap type="NekndPlatformConfigAbout" id="NekndPlatformConfigAboutResult">
        <result property="id"    column="id"    />
        <result property="state"    column="state"    />
        <result property="platformClassify"    column="platform_classify"    />
        <result property="platformIntroduceOnePicture"    column="platform_introduce_one_picture"    />
        <result property="platformIntroduceOneText"    column="platform_introduce_one_text"    />
        <result property="platformIntroduceTwoPicture"    column="platform_introduce_two_picture"    />
        <result property="platformIntroduceTwoText"    column="platform_introduce_two_text"    />
        <result property="organizationChart"    column="organization_chart"    />
        <result property="organizationChartText"    column="organization_chart_text"    />
        <result property="constitutionFileUri"    column="constitution_file_uri"    />
        <result property="applyFileUri"    column="apply_file_uri"    />
        <result property="email"    column="email"    />
        <result property="adressPicture"    column="adress_picture"    />
        <result property="adress"    column="adress"    />
        <result property="phone"    column="phone"    />
        <result property="contact"    column="contact"    />
        <result property="surveyPicture"    column="survey_picture"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="theme"    column="theme"    />
    </resultMap>

    <resultMap id="NekndPlatformConfigAboutNekndAboutClassifyResult" type="NekndPlatformConfigAbout" extends="NekndPlatformConfigAboutResult">
        <collection property="nekndAboutClassifyList" notNullColumn="sub_id" javaType="java.util.List" resultMap="NekndAboutClassifyResult" />
    </resultMap>

    <resultMap type="NekndAboutClassify" id="NekndAboutClassifyResult">
        <result property="id"    column="sub_id"    />
        <result property="platformConfigId"    column="sub_platform_config_id"    />
        <result property="url"    column="sub_url"    />
        <result property="urlName"    column="sub_url_name"    />
        <result property="info"    column="sub_info"    />
        <result property="delFlag"    column="sub_del_flag"    />
    </resultMap>

    <sql id="selectNekndPlatformConfigAboutVo">
        select id, state, platform_classify, platform_introduce_one_picture, platform_introduce_one_text, platform_introduce_two_picture, platform_introduce_two_text, organization_chart, organization_chart_text, constitution_file_uri, apply_file_uri, email, adress_picture, adress, phone, contact, survey_picture, del_flag, theme from neknd_platform_config_about
    </sql>

    <select id="selectNekndPlatformConfigAboutList" parameterType="NekndPlatformConfigAbout" resultMap="NekndPlatformConfigAboutResult">
        <include refid="selectNekndPlatformConfigAboutVo"/>
        <where>  
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="platformClassify != null  and platformClassify != ''"> and platform_classify = #{platformClassify}</if>
            <if test="platformIntroduceOnePicture != null  and platformIntroduceOnePicture != ''"> and platform_introduce_one_picture = #{platformIntroduceOnePicture}</if>
            <if test="platformIntroduceOneText != null  and platformIntroduceOneText != ''"> and platform_introduce_one_text = #{platformIntroduceOneText}</if>
            <if test="platformIntroduceTwoPicture != null  and platformIntroduceTwoPicture != ''"> and platform_introduce_two_picture = #{platformIntroduceTwoPicture}</if>
            <if test="platformIntroduceTwoText != null  and platformIntroduceTwoText != ''"> and platform_introduce_two_text = #{platformIntroduceTwoText}</if>
            <if test="organizationChart != null  and organizationChart != ''"> and organization_chart = #{organizationChart}</if>
            <if test="organizationChartText != null  and organizationChartText != ''"> and organization_chart_text = #{organizationChartText}</if>
            <if test="constitutionFileUri != null  and constitutionFileUri != ''"> and constitution_file_uri = #{constitutionFileUri}</if>
            <if test="applyFileUri != null  and applyFileUri != ''"> and apply_file_uri = #{applyFileUri}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="adressPicture != null  and adressPicture != ''"> and adress_picture = #{adressPicture}</if>
            <if test="adress != null  and adress != ''"> and adress = #{adress}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
             <if test="surveyPicture != null  and surveyPicture != ''"> and survey_picture = #{surveyPicture}</if>
              <if test="theme != null  and theme != ''"> and theme = #{theme}</if>
        </where>
    </select>
    
    <select id="selectNekndPlatformConfigAboutById" parameterType="Integer" resultMap="NekndPlatformConfigAboutNekndAboutClassifyResult">
        select a.id, a.state, a.platform_classify, a.platform_introduce_one_picture, a.platform_introduce_one_text, a.platform_introduce_two_picture, a.platform_introduce_two_text, a.organization_chart, a.organization_chart_text, a.constitution_file_uri, a.apply_file_uri, a.email, a.adress_picture, a.adress, a.phone, a.contact, a.survey_picture, a.del_flag, a.theme,
 b.id as sub_id, b.platform_config_id as sub_platform_config_id, b.url as sub_url, b.url_name as sub_url_name, b.info as sub_info, b.del_flag as sub_del_flag
        from neknd_platform_config_about a
        left join neknd_about_classify b on b.platform_config_id = a.id
        where a.id = #{id}
    </select>
        
    <insert id="insertNekndPlatformConfigAbout" parameterType="NekndPlatformConfigAbout" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_platform_config_about
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="state != null and state != ''">state,</if>
            <if test="platformClassify != null and platformClassify != ''">platform_classify,</if>
            <if test="platformIntroduceOnePicture != null and platformIntroduceOnePicture != ''">platform_introduce_one_picture,</if>
            <if test="platformIntroduceOneText != null and platformIntroduceOneText != ''">platform_introduce_one_text,</if>
            <if test="platformIntroduceTwoPicture != null and platformIntroduceTwoPicture != ''">platform_introduce_two_picture,</if>
            <if test="platformIntroduceTwoText != null and platformIntroduceTwoText != ''">platform_introduce_two_text,</if>
            <if test="organizationChart != null and organizationChart != ''">organization_chart,</if>
            <if test="organizationChartText != null and organizationChartText != ''">organization_chart_text,</if>
            <if test="constitutionFileUri != null and constitutionFileUri != ''">constitution_file_uri,</if>
            <if test="applyFileUri != null and applyFileUri != ''">apply_file_uri,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="adressPicture != null and adressPicture != ''">adress_picture,</if>
            <if test="adress != null and adress != ''">adress,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="surveyPicture != null and surveyPicture != ''">survey_picture,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="theme != null">theme,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="state != null and state != ''">#{state},</if>
            <if test="platformClassify != null and platformClassify != ''">#{platformClassify},</if>
            <if test="platformIntroduceOnePicture != null and platformIntroduceOnePicture != ''">#{platformIntroduceOnePicture},</if>
            <if test="platformIntroduceOneText != null and platformIntroduceOneText != ''">#{platformIntroduceOneText},</if>
            <if test="platformIntroduceTwoPicture != null and platformIntroduceTwoPicture != ''">#{platformIntroduceTwoPicture},</if>
            <if test="platformIntroduceTwoText != null and platformIntroduceTwoText != ''">#{platformIntroduceTwoText},</if>
            <if test="organizationChart != null and organizationChart != ''">#{organizationChart},</if>
            <if test="organizationChartText != null and organizationChartText != ''">#{organizationChartText},</if>
            <if test="constitutionFileUri != null and constitutionFileUri != ''">#{constitutionFileUri},</if>
            <if test="applyFileUri != null and applyFileUri != ''">#{applyFileUri},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="adressPicture != null and adressPicture != ''">#{adressPicture},</if>
            <if test="adress != null and adress != ''">#{adress},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="surveyPicture != null and surveyPicture != ''">#{surveyPicture},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="theme != null">#{theme},</if>
         </trim>
    </insert>

    <update id="updateNekndPlatformConfigAbout" parameterType="NekndPlatformConfigAbout">
        update neknd_platform_config_about
        <trim prefix="SET" suffixOverrides=",">
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="platformClassify != null and platformClassify != ''">platform_classify = #{platformClassify},</if>
            <if test="platformIntroduceOnePicture != null and platformIntroduceOnePicture != ''">platform_introduce_one_picture = #{platformIntroduceOnePicture},</if>
            <if test="platformIntroduceOneText != null and platformIntroduceOneText != ''">platform_introduce_one_text = #{platformIntroduceOneText},</if>
            <if test="platformIntroduceTwoPicture != null and platformIntroduceTwoPicture != ''">platform_introduce_two_picture = #{platformIntroduceTwoPicture},</if>
            <if test="platformIntroduceTwoText != null and platformIntroduceTwoText != ''">platform_introduce_two_text = #{platformIntroduceTwoText},</if>
            <if test="organizationChart != null and organizationChart != ''">organization_chart = #{organizationChart},</if>
            <if test="organizationChartText != null and organizationChartText != ''">organization_chart_text = #{organizationChartText},</if>
            <if test="constitutionFileUri != null and constitutionFileUri != ''">constitution_file_uri = #{constitutionFileUri},</if>
            <if test="applyFileUri != null and applyFileUri != ''">apply_file_uri = #{applyFileUri},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="adressPicture != null and adressPicture != ''">adress_picture = #{adressPicture},</if>
            <if test="adress != null and adress != ''">adress = #{adress},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="surveyPicture != null and surveyPicture != ''">survey_picture = #{surveyPicture},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="theme != null and theme != ''">theme = #{theme},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndPlatformConfigAboutById" parameterType="Integer">
        delete from neknd_platform_config_about where id = #{id}
    </delete>

    <delete id="deleteNekndPlatformConfigAboutByIds" parameterType="String">
        delete from neknd_platform_config_about where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteNekndAboutClassifyByPlatformConfigIds" parameterType="String">
        delete from neknd_about_classify where platform_config_id in 
        <foreach item="platformConfigId" collection="array" open="(" separator="," close=")">
            #{platformConfigId}
        </foreach>
    </delete>

    <delete id="deleteNekndAboutClassifyByPlatformConfigId" parameterType="Integer">
        delete from neknd_about_classify where platform_config_id = #{platformConfigId}
    </delete>

    <insert id="batchNekndAboutClassify">
        insert into neknd_about_classify( id, platform_config_id, url, url_name, info, del_flag) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.platformConfigId}, #{item.url}, #{item.urlName}, #{item.info}, #{item.delFlag})
        </foreach>
    </insert>
</mapper>