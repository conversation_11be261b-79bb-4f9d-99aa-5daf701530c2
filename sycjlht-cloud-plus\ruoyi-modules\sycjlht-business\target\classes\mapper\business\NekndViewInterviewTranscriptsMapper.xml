<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndViewInterviewTranscriptsMapper">
    
    <resultMap type="NekndViewInterviewTranscripts" id="NekndViewInterviewTranscriptsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="address"    column="address"    />
        <result property="phone"    column="phone"    />
        <result property="workYear"    column="work_year"    />
        <result property="technical"    column="technical"    />
        <result property="expectedMoney"    column="expected_money"    />
        <result property="workExperienceJson"    column="work_experience_json"    />
        <result property="educationalBackgroundJson"    column="educational_background_json"    />
        <result property="deptId"    column="dept_id"    />
        <result property="employId"    column="employ_id"    />
        <result property="jobName"    column="job_name"    />
        <result property="jobType"    column="job_type"    />
        <result property="jobSalary"    column="job_salary"    />
        <result property="time" column="view_time"/>
        <result property="status" column="status"/>
        <association property="nekndCompany" javaType="NekndCompany">
          <result property="deptId" column="dept_id"/>
          <result property="companyName" column="company_name"/>
          <result property="status" column="status"/>
        </association>
    </resultMap>

    <sql id="selectNekndViewInterviewTranscriptsVo">
        select id, user_id, name, sex, address, phone, work_year, technical, expected_money, work_experience_json, educational_background_json, dept_id, employ_id,view_time, job_name, job_type, job_salary,status from neknd_view_interview_transcripts
    </sql>

    <select id="selectNekndViewInterviewTranscriptsList" parameterType="NekndViewInterviewTranscripts" resultMap="NekndViewInterviewTranscriptsResult">
        <include refid="selectNekndViewInterviewTranscriptsVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="workYear != null  and workYear != ''"> and work_year = #{workYear}</if>
            <if test="technical != null  and technical != ''"> and technical = #{technical}</if>
            <if test="expectedMoney != null  and expectedMoney != ''"> and expected_money = #{expectedMoney}</if>
            <if test="workExperienceJson != null  and workExperienceJson != ''"> and work_experience_json = #{workExperienceJson}</if>
            <if test="educationalBackgroundJson != null  and educationalBackgroundJson != ''"> and educational_background_json = #{educationalBackgroundJson}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="employId != null "> and employ_id = #{employId}</if>
            <if test="jobName != null  and jobName != ''"> and job_name like concat('%', #{jobName}, '%')</if>
            <if test="jobType != null  and jobType != ''"> and job_type = #{jobType}</if>
            <if test="jobSalary != null  and jobSalary != ''"> and job_salary = #{jobSalary}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by view_time desc
    </select>
    
    <select id="selectNekndViewInterviewTranscriptsById" parameterType="Long" resultMap="NekndViewInterviewTranscriptsResult">
        <include refid="selectNekndViewInterviewTranscriptsVo"/>
        where id = #{id}
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        select count(1) from neknd_view_interview_transcripts where user_id =#{userId} and employ_id =#{employId}
    </select>

    <insert id="insertNekndViewInterviewTranscripts" parameterType="NekndViewInterviewTranscripts" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_view_interview_transcripts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="sex != null">sex,</if>
            <if test="address != null">address,</if>
            <if test="phone != null">phone,</if>
            <if test="workYear != null">work_year,</if>
            <if test="technical != null">technical,</if>
            <if test="expectedMoney != null">expected_money,</if>
            <if test="workExperienceJson != null">work_experience_json,</if>
            <if test="educationalBackgroundJson != null">educational_background_json,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="employId != null">employ_id,</if>
            <if test="jobName != null">job_name,</if>
            <if test="jobType != null">job_type,</if>
            <if test="jobSalary != null">job_salary,</if>
            <if test="time != null">view_time,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="address != null">#{address},</if>
            <if test="phone != null">#{phone},</if>
            <if test="workYear != null">#{workYear},</if>
            <if test="technical != null">#{technical},</if>
            <if test="expectedMoney != null">#{expectedMoney},</if>
            <if test="workExperienceJson != null">#{workExperienceJson},</if>
            <if test="educationalBackgroundJson != null">#{educationalBackgroundJson},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="employId != null">#{employId},</if>
            <if test="jobName != null">#{jobName},</if>
            <if test="jobType != null">#{jobType},</if>
            <if test="jobSalary != null">#{jobSalary},</if>
            <if test="time != null">#{time},</if>
            <if test="status != null">#{status}</if>
         </trim>
    </insert>

    <update id="updateNekndViewInterviewTranscripts" parameterType="NekndViewInterviewTranscripts">
        update neknd_view_interview_transcripts
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="address != null">address = #{address},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="workYear != null">work_year = #{workYear},</if>
            <if test="technical != null">technical = #{technical},</if>
            <if test="expectedMoney != null">expected_money = #{expectedMoney},</if>
            <if test="workExperienceJson != null">work_experience_json = #{workExperienceJson},</if>
            <if test="educationalBackgroundJson != null">educational_background_json = #{educationalBackgroundJson},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="employId != null">employ_id = #{employId},</if>
            <if test="jobName != null">job_name = #{jobName},</if>
            <if test="jobType != null">job_type = #{jobType},</if>
            <if test="jobSalary != null">job_salary = #{jobSalary},</if>
            <if test="time != null">view_time = #{time},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndViewInterviewTranscriptsById" parameterType="Long">
        delete from neknd_view_interview_transcripts where id = #{id}
    </delete>

    <delete id="deleteNekndViewInterviewTranscriptsByIds" parameterType="String">
        delete from neknd_view_interview_transcripts where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>



    <select id="selectPerson" parameterType="integer" resultMap="NekndViewInterviewTranscriptsResult">
        select company.company_name,view.job_name,view.view_time,view.job_salary,view.job_type,view.status from neknd_view_interview_transcripts as view LEFT JOIN neknd_company as company on view.dept_id=company.dept_id
        where user_id=#{userid} ORDER BY view_time DESC
    </select>

    <select id="selectEnterprise"  resultMap="NekndViewInterviewTranscriptsResult">
        select
            company.company_name,view.user_id,view.employ_id,view.dept_id,
            view.job_name,view.job_salary,view.name,view.view_time,
            view.sex,view.address,view.phone,view.work_year,
            view.technical,view.expected_money,view.job_type,view.status,company.address
        from neknd_view_interview_transcripts as view LEFT JOIN neknd_company as company
        on view.dept_id=company.dept_id
        <where>
            <if test="statusList != null" >
                and view.status IN
                <foreach item="item" index="index" collection="statusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != '' ">and view.status=#{status}</if>
            <if test="deptId != null and deptId != '' ">and view.dept_id=#{deptId}</if>
        </where>
        ORDER BY view.view_time DESC
    </select>


    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from neknd_view_interview_transcripts where user_id =#{userId} and employ_id =#{employId} and dept_id =#{deptId}
    </select>

    <select id="getCountInterviewedByDeptId" resultType="java.lang.Integer">
        select count(1) from neknd_view_interview_transcripts where dept_id=#{deptId} and status IN
        <foreach item="item" index="index" collection="statusList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getCountInterviewedByUserId" resultType="java.lang.Integer">
        select count(1) from neknd_view_interview_transcripts where user_id=#{userId} and status IN
        <foreach item="item" index="index" collection="statusList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateViewStatus" parameterType="NekndViewInterviewTranscripts">
        update neknd_view_interview_transcripts
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
        </trim>
        where user_id = #{userId} and employ_id = #{employId} and dept_id = #{deptId}
    </update>
</mapper>