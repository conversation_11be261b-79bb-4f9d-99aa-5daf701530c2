{"doc": " 企业数字化应用Controller\n \n <AUTHOR>\n @date 2024-05-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询企业数字化应用列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndSupplyApplication"], "doc": " 导出企业数字化应用列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取企业数字化应用详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": " 新增企业数字化应用\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": " 修改企业数字化应用\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除企业数字化应用\n"}], "constructors": []}