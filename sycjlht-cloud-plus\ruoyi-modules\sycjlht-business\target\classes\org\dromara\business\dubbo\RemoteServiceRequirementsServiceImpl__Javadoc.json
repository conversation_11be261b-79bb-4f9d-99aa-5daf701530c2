{"doc": " 服务需求远程服务实现\n\n <AUTHOR>\n @date 2024-12-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndServiceRequirementsByProviderId", "paramTypes": ["java.lang.Integer"], "doc": " 查询服务需求列表\n\n @param serviceProviderId 服务提供商ID\n @return 服务需求集合\n"}, {"name": "updateServiceProviderNameBatch", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": " 批量更新服务需求的服务商名称\n\n @param serviceProviderId 服务提供商ID\n @param serviceProviderName 服务提供商名称\n @return 更新结果\n"}], "constructors": []}