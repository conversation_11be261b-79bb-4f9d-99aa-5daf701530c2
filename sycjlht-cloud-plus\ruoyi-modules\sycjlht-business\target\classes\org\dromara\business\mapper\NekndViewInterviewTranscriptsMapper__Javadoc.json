{"doc": " 面试记录查看Mapper接口\n\n <AUTHOR>\n @date 2024-06-27\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndViewInterviewTranscriptsById", "paramTypes": ["java.lang.Long"], "doc": " 查询面试记录查看\n\n @param id 面试记录查看主键\n @return 面试记录查看\n"}, {"name": "selectNekndViewInterviewTranscriptsList", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 查询面试记录查看列表\n\n @param nekndViewInterviewTranscripts 面试记录查看\n @return 面试记录查看集合\n"}, {"name": "insertNekndViewInterviewTranscripts", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 新增面试记录查看\n\n @param id 面试记录查看\n @return 结果\n"}, {"name": "updateNekndViewInterviewTranscripts", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": " 修改面试记录查看\n\n @param nekndViewInterviewTranscripts 面试记录查看\n @return 结果\n"}, {"name": "deleteNekndViewInterviewTranscriptsById", "paramTypes": ["java.lang.Long"], "doc": " 删除面试记录查看\n\n @param id 面试记录查看主键\n @return 结果\n"}, {"name": "deleteNekndViewInterviewTranscriptsByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除面试记录查看\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}