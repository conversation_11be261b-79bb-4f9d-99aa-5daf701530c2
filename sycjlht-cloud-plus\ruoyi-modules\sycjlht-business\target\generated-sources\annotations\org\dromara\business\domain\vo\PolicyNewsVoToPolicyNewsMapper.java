package org.dromara.business.domain.vo;

import io.github.linpeilie.AutoMapperConfig__908;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.PolicyNews;
import org.dromara.business.domain.PolicyNewsToPolicyNewsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__908.class,
    uses = {PolicyNewsToPolicyNewsVoMapper.class},
    imports = {}
)
public interface PolicyNewsVoToPolicyNewsMapper extends BaseMapper<PolicyNewsVo, PolicyNews> {
}
