package org.dromara.business.domain.vo;

import io.github.linpeilie.AutoMapperConfig__907;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.PolicyNews;
import org.dromara.business.domain.PolicyNewsToPolicyNewsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__907.class,
    uses = {PolicyNewsToPolicyNewsVoMapper.class},
    imports = {}
)
public interface PolicyNewsVoToPolicyNewsMapper extends BaseMapper<PolicyNewsVo, PolicyNews> {
}
