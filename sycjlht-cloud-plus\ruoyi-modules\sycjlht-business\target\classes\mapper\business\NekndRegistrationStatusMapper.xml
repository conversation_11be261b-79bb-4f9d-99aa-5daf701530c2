<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndRegistrationStatusMapper">
    
    <resultMap type="NekndRegistrationStatus" id="NekndRegistrationStatusResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="trainId"    column="train_id"    />
        <result property="deptTitle"    column="dept_title"    />
        <result property="deptStatus"    column="dept_status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="classHour"    column="class_hour"    />
        <result property="state"    column="state"    />
        <result property="price"    column="price"    />
        <result property="sector"    column="sector"    />
        <result property="issuingUnit"    column="issuing_unit"    />
        <result property="organizer"    column="organizer"    />
        <result property="registrationStatus"    column="registration_status"    />
        <result property="delfalg" column="delfalg"/>
        <result property="deptId" column="dept_id" />
    </resultMap>

    <sql id="selectNekndRegistrationStatusVo">
        select id, user_id, name, sex, phone, email,dept_id, train_id, dept_title, dept_status, start_time, end_time, class_hour, state, price, sector, issuing_unit, organizer, registration_status,delfalg from neknd_registration_status
    </sql>

    <select id="selectNekndRegistrationStatusList" parameterType="NekndRegistrationStatus" resultMap="NekndRegistrationStatusResult">
        <include refid="selectNekndRegistrationStatusVo"/>
        <where>
            delfalg = 0
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="trainId != null "> and train_id = #{trainId}</if>
            <if test="deptTitle != null  and deptTitle != ''"> and dept_title = #{deptTitle}</if>
            <if test="deptStatus != null  and deptStatus != ''"> and dept_status = #{deptStatus}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="classHour != null  and classHour != ''"> and class_hour = #{classHour}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="sector != null  and sector != ''"> and sector = #{sector}</if>
            <if test="issuingUnit != null  and issuingUnit != ''"> and issuing_unit = #{issuingUnit}</if>
            <if test="organizer != null  and organizer != ''"> and organizer = #{organizer}</if>
            <if test="registrationStatus != null  and registrationStatus != ''"> and registration_status = #{registrationStatus}</if>
<!--            <if test="delfalg != null and delfalg != ''" >and delfalg = #{delfalg}</if>-->
        </where>
--             order by create_time desc 不能根据创建时间排序，没有这个字段
    </select>
    
    <select id="selectNekndRegistrationStatusById" parameterType="Integer" resultMap="NekndRegistrationStatusResult">
        <include refid="selectNekndRegistrationStatusVo"/>
        where id = #{id}
    </select>
    <select id="getStatus" parameterType="NekndRegistrationStatus" resultType="java.lang.String">
        select registration_status  from neknd_registration_status where user_id = #{userId} and train_id =#{trainId}
    </select>
    <select id="getIsExistByUserAndTrainId" resultType="java.lang.Integer">
        select count(1) from neknd_registration_status
        where user_id=#{userId} and  train_id=#{trainId}  and delfalg=0
    </select>
    <select id="getInfo" resultType="com.neknd.system.domain.NekndRegistrationStatus">
        <include refid="selectNekndRegistrationStatusVo"></include>
        where user_id=#{userId} and  train_id=#{trainId} and delfalg=0
    </select>


    <insert id="insertNekndRegistrationStatus" parameterType="NekndRegistrationStatus">
        insert into neknd_registration_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="sex != null">sex,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="trainId != null">train_id,</if>
            <if test="deptTitle != null">dept_title,</if>
            <if test="deptStatus != null">dept_status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="classHour != null">class_hour,</if>
            <if test="state != null">state,</if>
            <if test="price != null">price,</if>
            <if test="sector != null">sector,</if>
            <if test="issuingUnit != null">issuing_unit,</if>
            <if test="organizer != null">organizer,</if>
            <if test="registrationStatus != null">registration_status,</if>
            <if test="delfalg != null and delfalg!= ''">delfalg,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="trainId != null">#{trainId},</if>
            <if test="deptTitle != null">#{deptTitle},</if>
            <if test="deptStatus != null">#{deptStatus},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="classHour != null">#{classHour},</if>
            <if test="state != null">#{state},</if>
            <if test="price != null">#{price},</if>
            <if test="sector != null">#{sector},</if>
            <if test="issuingUnit != null">#{issuingUnit},</if>
            <if test="organizer != null">#{organizer},</if>
            <if test="registrationStatus != null">#{registrationStatus},</if>
            <if test="delfalg != null and delfalg!= ''">#{delfalg}</if>
            <if test="deptId != null">#{deptId}</if>
         </trim>
    </insert>

    <update id="updateNekndRegistrationStatus" parameterType="NekndRegistrationStatus">
        update neknd_registration_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="trainId != null">train_id = #{trainId},</if>
            <if test="deptTitle != null">dept_title = #{deptTitle},</if>
            <if test="deptStatus != null">dept_status = #{deptStatus},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="classHour != null">class_hour = #{classHour},</if>
            <if test="state != null">state = #{state},</if>
            <if test="price != null">price = #{price},</if>
            <if test="sector != null">sector = #{sector},</if>
            <if test="issuingUnit != null">issuing_unit = #{issuingUnit},</if>
            <if test="organizer != null">organizer = #{organizer},</if>
            <if test="registrationStatus != null">registration_status = #{registrationStatus},</if>
            <if test="delfalg != null and delfalg != ''">delfalg = #{delfalg}</if>
            <if test="deptId != null">dept_id = #{deptId}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndRegistrationStatusById" parameterType="Integer">
        delete from neknd_registration_status where id = #{id}
    </delete>

    <delete id="deleteNekndRegistrationStatusByIds" parameterType="String">
        delete from neknd_registration_status where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="delfalg" parameterType="Integer">
        update neknd_registration_status set delfalg = 1 where id=#{id};
    </update>



    <update id="delfalgs" parameterType="Integer">
        update neknd_registration_status set delfalg = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectPersonalTraining" parameterType="Integer" resultMap="NekndRegistrationStatusResult">
     <include refid="selectNekndRegistrationStatusVo"/>
        where user_id=#{id} and dept_status=2 and  registration_status=0 and delfalg=0
    </select>

    <select id="selectEnterpriseTraining" parameterType="Integer" resultMap="NekndRegistrationStatusResult">
        <include refid="selectNekndRegistrationStatusVo"/>
        where dept_id=#{id} and dept_status=2 and registration_status=0 and delfalg=0
    </select>
    <select id="selectTrainStudentsByTrainId" resultMap="NekndRegistrationStatusResult">
        <include refid="selectNekndRegistrationStatusVo"/>
        <where>
            delfalg = 0 and registration_status = 0 and train_id = #{id}
         </where>
    </select>


</mapper>