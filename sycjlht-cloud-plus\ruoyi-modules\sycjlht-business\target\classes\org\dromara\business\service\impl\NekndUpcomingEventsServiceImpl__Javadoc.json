{"doc": " 活动预告信息Service业务层处理\n\n <AUTHOR>\n @date 2024-10-10\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUpcomingEventsById", "paramTypes": ["java.lang.Integer"], "doc": " 查询活动预告信息\n\n @param id 活动预告信息主键\n @return 活动预告信息\n"}, {"name": "selectNekndUpcomingEventsList", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": " 查询活动预告信息列表\n\n @param nekndUpcomingEvents 活动预告信息\n @return 活动预告信息\n"}, {"name": "selectPageNekndUpcomingEventsList", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询活动预告信息列表（分页）\n\n @param nekndUpcomingEvents 活动预告信息\n @param pageQuery 分页查询参数\n @return 活动预告信息分页集合\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": " 构建查询条件\n"}, {"name": "insertNekndUpcomingEvents", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": " 新增活动预告信息\n\n @param nekndUpcomingEvents 活动预告信息\n @return 结果\n"}, {"name": "updateNekndUpcomingEvents", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": " 修改活动预告信息\n\n @param nekndUpcomingEvents 活动预告信息\n @return 结果\n"}, {"name": "deleteNekndUpcomingEventsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除活动预告信息\n\n @param ids 需要删除的活动预告信息主键\n @return 结果\n"}, {"name": "deleteNekndUpcomingEventsById", "paramTypes": ["java.lang.Integer"], "doc": " 删除活动预告信息信息\n\n @param id 活动预告信息主键\n @return 结果\n"}], "constructors": []}