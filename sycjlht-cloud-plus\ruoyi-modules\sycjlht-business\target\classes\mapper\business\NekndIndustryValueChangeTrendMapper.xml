<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndIndustryValueChangeTrendMapper">
    
    <resultMap type="NekndIndustryValueChangeTrend" id="NekndIndustryValueChangeTrendResult">
        <result property="industryId"    column="industry_id"    />
        <result property="year"    column="year"    />
        <result property="outputValue"    column="output_value"    />
    </resultMap>

    <sql id="selectNekndIndustryValueChangeTrendVo">
        select industry_id, year, output_value from neknd_industry_value_change_trend
    </sql>

    <select id="selectNekndIndustryValueChangeTrendList" parameterType="NekndIndustryValueChangeTrend" resultMap="NekndIndustryValueChangeTrendResult">
        <include refid="selectNekndIndustryValueChangeTrendVo"/>
        <where>  
            <if test="year != null "> and year = #{year}</if>
            <if test="outputValue != null "> and output_value = #{outputValue}</if>
        </where>
    </select>
    
    <select id="selectNekndIndustryValueChangeTrendByIndustryId" parameterType="Long" resultMap="NekndIndustryValueChangeTrendResult">
        <include refid="selectNekndIndustryValueChangeTrendVo"/>
        where industry_id = #{industryId}
    </select>

    <select id="selectNekndIndustryValueChangeTrendByIndustryIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_industry_value_change_trend
        where industry_id = #{industryId}
    </select>
        
    <insert id="insertNekndIndustryValueChangeTrend" parameterType="NekndIndustryValueChangeTrend" useGeneratedKeys="true" keyProperty="industryId">
        insert into neknd_industry_value_change_trend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="industryId != null">industry_id,</if>
            <if test="year != null">year,</if>
            <if test="outputValue != null">output_value,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="industryId != null">#{industryId},</if>
            <if test="year != null">#{year},</if>
            <if test="outputValue != null">#{outputValue},</if>
        </trim>
        On Duplicate Key Update industry_id = values(industry_id), year = values(year), output_value = values(output_value)
    </insert>

    <update id="updateNekndIndustryValueChangeTrend" parameterType="NekndIndustryValueChangeTrend">
        update neknd_industry_value_change_trend
        <trim prefix="SET" suffixOverrides=",">
            <if test="year != null">year = #{year},</if>
            <if test="outputValue != null">output_value = #{outputValue},</if>
        </trim>
        where industry_id = #{industryId}
    </update>

    <delete id="deleteNekndIndustryValueChangeTrendByIndustryId" parameterType="Long">
        delete from neknd_industry_value_change_trend where industry_id = #{industryId}
    </delete>

    <delete id="deleteNekndIndustryValueChangeTrendByIndustryIds" parameterType="String">
        delete from neknd_industry_value_change_trend where industry_id in 
        <foreach item="industryId" collection="array" open="(" separator="," close=")">
            #{industryId}
        </foreach>
    </delete>
</mapper>