{"doc": " 共同体Mapper接口\n\n <AUTHOR>\n @date 2025-01-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCommunityById", "paramTypes": ["java.lang.Integer"], "doc": " 查询共同体\n\n @param id 共同体主键\n @return 共同体\n"}, {"name": "selectNekndCommunityList", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": " 查询共同体列表\n\n @param nekndCommunity 共同体\n @return 共同体集合\n"}, {"name": "insertNekndCommunity", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": " 新增共同体\n\n @param nekndCommunity 共同体\n @return 结果\n"}, {"name": "updateNekndCommunity", "paramTypes": ["org.dromara.business.domain.NekndCommunity"], "doc": " 修改共同体\n\n @param nekndCommunity 共同体\n @return 结果\n"}, {"name": "deleteNekndCommunityById", "paramTypes": ["java.lang.Integer"], "doc": " 删除共同体\n\n @param id 共同体主键\n @return 结果\n"}, {"name": "deleteNekndCommunityByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除共同体\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}