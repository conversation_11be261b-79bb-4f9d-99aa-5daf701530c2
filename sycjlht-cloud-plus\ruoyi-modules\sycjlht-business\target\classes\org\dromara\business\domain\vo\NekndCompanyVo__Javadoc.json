{"doc": " 企业信息VO对象\n 用于避免跨模块依赖，后续通过Dubbo RPC调用获取完整数据\n \n <AUTHOR>\n @date 2025-08-02\n", "fields": [{"name": "deptId", "doc": "部门ID "}, {"name": "companyName", "doc": "企业名称 "}, {"name": "unifiedSocialCreditCode", "doc": "统一社会信用代码 "}, {"name": "enterpriseType", "doc": "企业类型 "}, {"name": "<PERSON><PERSON><PERSON>", "doc": "联系人 "}, {"name": "contactPhone", "doc": "联系电话 "}, {"name": "email", "doc": "企业邮箱 "}, {"name": "address", "doc": "企业地址 "}, {"name": "introduction", "doc": "企业简介 "}, {"name": "establishDate", "doc": "成立日期 "}, {"name": "registeredCapital", "doc": "注册资本 "}, {"name": "employeeCount", "doc": "员工人数 "}, {"name": "industry", "doc": "所属行业 "}, {"name": "businessScope", "doc": "经营范围 "}, {"name": "reviewStatus", "doc": "审核状态 "}, {"name": "status", "doc": "状态（0正常 1停用） "}], "enumConstants": [], "methods": [], "constructors": []}