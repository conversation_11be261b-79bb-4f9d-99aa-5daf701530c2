{"doc": " 师资库Controller\n\n <AUTHOR>\n @date 2024-12-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询师资库列表\n"}, {"name": "getList", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 匿名查询师资库列表\n"}, {"name": "export", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "org.dromara.business.domain.NekndTeacherPool"], "doc": " 导出师资库列表\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 下载导入模板\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Integer"], "doc": " 获取师资库详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": " 新增师资库\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": " 修改师资库\n"}, {"name": "remove", "paramTypes": ["java.lang.Integer[]"], "doc": " 删除师资库\n"}], "constructors": []}