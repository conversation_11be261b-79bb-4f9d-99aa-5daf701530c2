{"doc": " 企业/学校评价Mapper接口\n\n <AUTHOR>\n @date 2024-05-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPersonEvaluateById", "paramTypes": ["java.lang.Integer"], "doc": " 查询企业/学校评价\n\n @param id 企业/学校评价主键\n @return 企业/学校评价\n"}, {"name": "selectNekndPersonEvaluateList", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": " 查询企业/学校评价列表\n\n @param nekndPersonEvaluate 企业/学校评价\n @return 企业/学校评价集合\n"}, {"name": "insertNekndPersonEvaluate", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": " 新增企业/学校评价\n\n @param nekndPersonEvaluate 企业/学校评价\n @return 结果\n"}, {"name": "updateNekndPersonEvaluate", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": " 修改企业/学校评价\n\n @param nekndPersonEvaluate 企业/学校评价\n @return 结果\n"}, {"name": "deleteNekndPersonEvaluateById", "paramTypes": ["java.lang.Integer"], "doc": " 删除企业/学校评价\n\n @param id 企业/学校评价主键\n @return 结果\n"}, {"name": "deleteNekndPersonEvaluateByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除企业/学校评价\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}