{"doc": " 字典工具类\n\n <AUTHOR>\n", "fields": [{"name": "SEPARATOR", "doc": " 分隔符\n"}], "enumConstants": [], "methods": [{"name": "setDict<PERSON>ache", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 设置字典缓存\n\n @param key 参数键\n @param dictDatas 字典数据列表\n"}, {"name": "getDictCache", "paramTypes": ["java.lang.String"], "doc": " 获取字典缓存\n\n @param key 参数键\n @return dictDatas 字典数据列表\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典值获取字典标签\n\n @param dictType 字典类型\n @param dictValue 字典值\n @return 字典标签\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典标签获取字典值\n\n @param dictType 字典类型\n @param dictLabel 字典标签\n @return 字典值\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典值获取字典标签\n\n @param dictType 字典类型\n @param dictValue 字典值\n @param separator 分隔符\n @return 字典标签\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典标签获取字典值\n\n @param dictType 字典类型\n @param dictLabel 字典标签\n @param separator 分隔符\n @return 字典值\n"}, {"name": "removeDictCache", "paramTypes": ["java.lang.String"], "doc": " 删除指定字典缓存\n\n @param key 字典键\n"}, {"name": "clearDictCache", "paramTypes": [], "doc": " 清空字典缓存\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 设置cache key\n\n @param configKey 参数键\n @return 缓存键key\n"}], "constructors": []}