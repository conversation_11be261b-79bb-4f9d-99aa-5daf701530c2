{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getParticipateData", "paramTypes": [], "doc": " 参与园区、企业、高校数\n"}, {"name": "getProvincialInvestmentRanking", "paramTypes": [], "doc": " 各省投入资金排名\n"}, {"name": "getSpecialtyCoverageMatchRate", "paramTypes": [], "doc": " 专业覆盖匹配度\n"}, {"name": "getProvincialLocalEmploymentRate", "paramTypes": [], "doc": " 各省园区本地就业率\n"}, {"name": "getProvincialCountryLevelPark", "paramTypes": [], "doc": " 各省国家级数量分布\n"}, {"name": "getProvincialEnterpriseType", "paramTypes": [], "doc": " 各省企业类型数量分布\n"}, {"name": "getProvincialComprehensiveRanking", "paramTypes": [], "doc": " 各省综合排名\n"}, {"name": "getIndustryEducationBenchmark", "paramTypes": [], "doc": " 专项冠军\n"}, {"name": "getWarningPanel", "paramTypes": [], "doc": " 预警面板\n"}], "constructors": []}