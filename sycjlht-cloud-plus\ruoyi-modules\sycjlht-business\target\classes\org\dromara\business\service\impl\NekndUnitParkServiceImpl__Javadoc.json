{"doc": " 成员单位-园区Service业务层处理\n\n <AUTHOR>\n @date 2025-05-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUnitParkById", "paramTypes": ["java.lang.Long"], "doc": " 查询成员单位-园区\n\n @param id 成员单位-园区主键\n @return 成员单位-园区\n"}, {"name": "selectNekndUnitParkByUid", "paramTypes": ["java.lang.Long"], "doc": " 根据uId查询成员单位-园区\n\n @param uid 成员单位-园区主键\n @return 成员单位-园区\n"}, {"name": "selectNekndUnitParkList", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": " 查询成员单位-园区列表\n\n @param nekndUnitPark 成员单位-园区\n @return 成员单位-园区\n"}, {"name": "selectPageNekndUnitParkList", "paramTypes": ["org.dromara.business.domain.NekndUnitPark", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询成员单位-园区列表\n\n @param nekndUnitPark 成员单位-园区\n @param pageQuery 分页参数\n @return 成员单位-园区分页数据\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": " 构建查询条件\n\n @param nekndUnitPark 成员单位-园区\n @return 查询条件\n"}, {"name": "insertNekndUnitPark", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": " 新增成员单位-园区\n\n @param nekndUnitPark 成员单位-园区\n @return 结果\n"}, {"name": "updateNekndUnitPark", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": " 修改成员单位-园区\n\n @param nekndUnitPark 成员单位-园区\n @return 结果\n"}, {"name": "deleteNekndUnitParkByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除成员单位-园区\n\n @param ids 需要删除的成员单位-园区主键\n @return 结果\n"}, {"name": "deleteNekndUnitParkById", "paramTypes": ["java.lang.Long"], "doc": " 删除成员单位-园区信息\n\n @param id 成员单位-园区主键\n @return 结果\n"}], "constructors": []}