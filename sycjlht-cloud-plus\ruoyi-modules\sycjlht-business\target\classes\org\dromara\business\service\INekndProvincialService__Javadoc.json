{"doc": " 国级Service接口\n\n <AUTHOR>\n @date 2024-05-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndProvincialByPid", "paramTypes": ["java.lang.Long"], "doc": " 查询省级\n\n @param pid 省级主键\n @return 省级\n"}, {"name": "selectNekndProvincialList", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": " 查询省级列表\n\n @param nekndProvincial 省级\n @return 省级集合\n"}, {"name": "insertNekndProvincial", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": " 新增省级\n\n @param nekndProvincial 省级\n @return 结果\n"}, {"name": "updateNekndProvincial", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": " 修改省级\n\n @param nekndProvincial 省级\n @return 结果\n"}, {"name": "deleteNekndProvincialByPids", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除省级\n\n @param pids 需要删除的省级主键集合\n @return 结果\n"}, {"name": "deleteNekndProvincialByPid", "paramTypes": ["java.lang.Long"], "doc": " 删除省级信息\n\n @param pid 省级主键\n @return 结果\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.NekndProvincial", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询省级列表\n\n @param entity 查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}, {"name": "searchProvincials", "paramTypes": ["java.lang.String"], "doc": " 搜索省级数据\n\n @param keyword 搜索关键字\n @return 省级列表\n"}, {"name": "getProvincialStatistics", "paramTypes": [], "doc": " 获取省级统计信息\n\n @return 统计数据\n"}], "constructors": []}