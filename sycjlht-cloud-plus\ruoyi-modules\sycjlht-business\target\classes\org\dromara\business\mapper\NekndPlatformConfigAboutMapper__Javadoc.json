{"doc": " 关于平台信息编辑Mapper接口\n\n <AUTHOR>\n @date 2025-02-05\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPlatformConfigAboutById", "paramTypes": ["java.lang.Integer"], "doc": " 查询关于平台信息编辑\n\n @param id 关于平台信息编辑主键\n @return 关于平台信息编辑\n"}, {"name": "selectNekndPlatformConfigAboutList", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 查询关于平台信息编辑列表\n\n @param nekndPlatformConfigAbout 关于平台信息编辑\n @return 关于平台信息编辑集合\n"}, {"name": "insertNekndPlatformConfigAbout", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 新增关于平台信息编辑\n\n @param nekndPlatformConfigAbout 关于平台信息编辑\n @return 结果\n"}, {"name": "updateNekndPlatformConfigAbout", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": " 修改关于平台信息编辑\n\n @param nekndPlatformConfigAbout 关于平台信息编辑\n @return 结果\n"}, {"name": "deleteNekndPlatformConfigAboutById", "paramTypes": ["java.lang.Integer"], "doc": " 删除关于平台信息编辑\n\n @param id 关于平台信息编辑主键\n @return 结果\n"}, {"name": "deleteNekndPlatformConfigAboutByIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除关于平台信息编辑\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}, {"name": "deleteNekndAboutClassifyByPlatformConfigIds", "paramTypes": ["java.lang.Integer[]"], "doc": " 批量删除关于(头部简介)信息\n\n @param ids 需要删除的数据主键集合\n @return 结果\n"}, {"name": "batchNekndAboutClassify", "paramTypes": ["java.util.List"], "doc": " 批量新增关于(头部简介)信息\n\n @param nekndAboutClassifyList 关于(头部简介)信息列表\n @return 结果\n"}, {"name": "deleteNekndAboutClassifyByPlatformConfigId", "paramTypes": ["java.lang.Integer"], "doc": " 通过关于平台信息编辑主键删除关于(头部简介)信息信息\n\n @param id 关于平台信息编辑ID\n @return 结果\n"}], "constructors": []}