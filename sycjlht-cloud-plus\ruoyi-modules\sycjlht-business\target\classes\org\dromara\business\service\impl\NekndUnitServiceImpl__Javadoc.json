{"doc": " 成员单位信息Service业务层处理\n\n <AUTHOR>\n @date 2024-10-22\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": " 查询成员单位信息\n\n @param id 成员单位信息主键\n @return 成员单位信息\n"}, {"name": "selectNekndUnitList", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 查询成员单位信息列表\n\n @param nekndUnit 成员单位信息\n @return 成员单位信息\n"}, {"name": "selectPageNekndUnitList", "paramTypes": ["org.dromara.business.domain.NekndUnit", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询成员单位信息列表\n\n @param nekndUnit 成员单位信息\n @param pageQuery 分页查询参数\n @return 分页数据\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 构建查询条件\n"}, {"name": "insertNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 新增成员单位信息\n\n @param nekndUnit 成员单位信息\n @return 结果\n"}, {"name": "updateNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": " 修改成员单位信息\n\n @param nekndUnit 成员单位信息\n @return 结果\n"}, {"name": "deleteNekndUnitByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除成员单位信息\n\n @param ids 需要删除的成员单位信息主键\n @return 结果\n"}, {"name": "deleteNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": " 删除成员单位信息信息\n\n @param id 成员单位信息主键\n @return 结果\n"}, {"name": "processJsonField", "paramTypes": ["java.util.HashMap", "java.lang.String", "java.lang.String", "com.fasterxml.jackson.databind.ObjectMapper"], "doc": " 通用处理方法：解析 JSON 字符串字段，并计算所有 percentage 的平均值\n"}, {"name": "getIndustrialParkData", "paramTypes": ["java.lang.String"], "doc": " 检测平台-园区数据\n @param name\n @return\n"}, {"name": "getIndustrialCityData", "paramTypes": ["java.lang.String"], "doc": " 检测平台-市数据\n @param name\n @return\n"}, {"name": "getIndustrialProvincialData", "paramTypes": ["java.lang.String"], "doc": " 检测平台-省数据\n @param name\n @return\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新单位状态\n\n @param ids 单位ID列表\n @param status 目标状态\n @return 更新结果\n"}, {"name": "getUnitStatistics", "paramTypes": [], "doc": " 获取单位统计信息\n\n @return 统计数据\n"}], "constructors": []}