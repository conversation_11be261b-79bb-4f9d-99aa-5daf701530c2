{"doc": " 【请填写功能名称】Service接口\n\n <AUTHOR>\n @date 2024-12-25\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryProportionTeachersByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 查询【请填写功能名称】\n\n @param industryId 【请填写功能名称】主键\n @return 【请填写功能名称】\n"}, {"name": "selectNekndIndustryProportionTeachersList", "paramTypes": ["org.dromara.business.domain.NekndIndustryProportionTeachers"], "doc": " 查询【请填写功能名称】列表\n\n @param nekndIndustryProportionTeachers 【请填写功能名称】\n @return 【请填写功能名称】集合\n"}, {"name": "insertNekndIndustryProportionTeachers", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "org.dromara.business.domain.NekndIndustryProportionTeachers"], "doc": " 新增【请填写功能名称】\n\n @param nekndIndustryProportionTeachers 【请填写功能名称】\n @return 结果\n"}, {"name": "updateNekndIndustryProportionTeachers", "paramTypes": ["org.dromara.business.domain.NekndIndustryProportionTeachers"], "doc": " 修改【请填写功能名称】\n\n @param nekndIndustryProportionTeachers 【请填写功能名称】\n @return 结果\n"}, {"name": "deleteNekndIndustryProportionTeachersByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除【请填写功能名称】\n\n @param industryIds 需要删除的【请填写功能名称】主键集合\n @return 结果\n"}, {"name": "deleteNekndIndustryProportionTeachersByIndustryId", "paramTypes": ["java.lang.Long"], "doc": " 删除【请填写功能名称】信息\n\n @param industryId 【请填写功能名称】主键\n @return 结果\n"}], "constructors": []}