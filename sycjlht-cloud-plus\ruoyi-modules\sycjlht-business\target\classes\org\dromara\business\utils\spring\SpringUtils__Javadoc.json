{"doc": " spring工具类 方便在非spring管理环境中获取bean\n\n <AUTHOR>\n", "fields": [{"name": "beanFactory", "doc": "Spring应用上下文环境 "}], "enumConstants": [], "methods": [{"name": "get<PERSON>ean", "paramTypes": ["java.lang.String"], "doc": " 获取对象\n\n @param name\n @return Object 一个以所给名字注册的bean的实例\n @throws BeansException\n\n"}, {"name": "get<PERSON>ean", "paramTypes": ["java.lang.Class"], "doc": " 获取类型为requiredType的对象\n\n @param clz\n @return\n @throws BeansException\n\n"}, {"name": "containsBean", "paramTypes": ["java.lang.String"], "doc": " 如果BeanFactory包含一个与所给名称匹配的bean定义，则返回true\n\n @param name\n @return boolean\n"}, {"name": "isSingleton", "paramTypes": ["java.lang.String"], "doc": " 判断以给定名字注册的bean定义是一个singleton还是一个prototype。 如果与给定名字相应的bean定义没有被找到，将会抛出一个异常（NoSuchBeanDefinitionException）\n\n @param name\n @return boolean\n @throws NoSuchBeanDefinitionException\n\n"}, {"name": "getType", "paramTypes": ["java.lang.String"], "doc": " @param name\n @return Class 注册对象的类型\n @throws NoSuchBeanDefinitionException\n\n"}, {"name": "getAliases", "paramTypes": ["java.lang.String"], "doc": " 如果给定的bean名字在bean定义中有别名，则返回这些别名\n\n @param name\n @return\n @throws NoSuchBeanDefinitionException\n\n"}, {"name": "getAopProxy", "paramTypes": ["java.lang.Object"], "doc": " 获取aop代理对象\n\n @param invoker\n @return\n"}, {"name": "getActiveProfiles", "paramTypes": [], "doc": " 获取当前的环境配置，无配置返回null\n\n @return 当前的环境配置\n"}, {"name": "getActiveProfile", "paramTypes": [], "doc": " 获取当前的环境配置，当有多个环境配置时，只获取第一个\n\n @return 当前的环境配置\n"}, {"name": "getRequiredProperty", "paramTypes": ["java.lang.String"], "doc": " 获取配置文件中的值\n\n @param key 配置文件的key\n @return 当前的配置文件的值\n\n"}], "constructors": []}